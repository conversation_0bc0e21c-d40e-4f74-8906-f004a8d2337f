# Modern Todo Application - Environment Configuration Template
# Copy this file to .env and configure with your actual values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment (development, production, test)
NODE_ENV=production

# Application Information
VITE_APP_NAME=Modern Todo
VITE_APP_VERSION=1.0.0

# Logging Configuration
LOG_LEVEL=info

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Name (required)
DATABASE_NAME=modern_todo_prod

# Local Database Path
DATABASE_PATH=./data/prod.db

# MotherDuck Configuration (optional, for cloud sync)
MOTHERDUCK_TOKEN=your_motherduck_token_here

# Database Connection Settings
CONNECTION_TIMEOUT=10000
MAX_CONNECTIONS=5
MIN_CONNECTIONS=1

# Database Security
ENABLE_WAL=true
ENCRYPTION_ENABLED=true
DATABASE_ENCRYPTION_KEY=your_32_character_encryption_key_here

# =============================================================================
# AUTHENTICATION & SECURITY CONFIGURATION
# =============================================================================

# Session Configuration
SESSION_TIMEOUT=86400000
SESSION_SECRET=your_session_secret_key_minimum_32_characters_long

# Password Policy
PASSWORD_MIN_LENGTH=12
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# Authentication Security
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
REQUIRE_2FA=false
ENABLE_BIOMETRIC=false

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Encryption
ENCRYPTION_ALGORITHM=aes-256-gcm
ENCRYPTION_KEY=your_encryption_key_32_characters_long
SALT_ROUNDS=12

# Security Features
ENABLE_AUDIT_LOGGING=true
ENABLE_INTRUSION_DETECTION=true
ENABLE_SECURITY_HEADERS=true
ENABLE_CSRF_PROTECTION=true

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# Allowed Origins (comma-separated)
ALLOWED_ORIGINS=https://api.motherduck.com

# =============================================================================
# ELECTRON SECURITY CONFIGURATION
# =============================================================================

# Electron Security Settings
DISABLE_NODE_INTEGRATION=true
ENABLE_CONTEXT_ISOLATION=true
ENABLE_SANDBOX=false
ENABLE_DEVTOOLS=false

# =============================================================================
# SYNC & OFFLINE CONFIGURATION
# =============================================================================

# Sync Settings
SYNC_INTERVAL=30000
ENABLE_OFFLINE_MODE=true
AUTO_SYNC=true

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Audit Logging
AUDIT_LOG_LEVEL=info
AUDIT_LOG_FILE=./logs/audit.log
AUDIT_LOG_MAX_SIZE=10485760
AUDIT_LOG_MAX_FILES=5

# Security Monitoring
SECURITY_LOG_FILE=./logs/security.log
FAILED_LOGIN_THRESHOLD=3
SUSPICIOUS_ACTIVITY_THRESHOLD=10

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================

# Error Handling
EXPOSE_STACK_TRACES=false
DETAILED_ERROR_MESSAGES=false

# Update Configuration
ENABLE_AUTO_UPDATES=true
UPDATE_SERVER_URL=https://your-update-server.com

# Code Signing (for production builds)
CODE_SIGN_IDENTITY=your_code_signing_identity
NOTARIZE_APPLE_ID=your_apple_id_for_notarization

# =============================================================================
# DEVELOPMENT OVERRIDES (only for development environment)
# =============================================================================

# Development-specific settings (uncomment for development)
# NODE_ENV=development
# LOG_LEVEL=debug
# ENABLE_DEVTOOLS=true
# EXPOSE_STACK_TRACES=true
# DETAILED_ERROR_MESSAGES=true
# CSP_REPORT_ONLY=true
# ENCRYPTION_ENABLED=false
# REQUIRE_2FA=false
