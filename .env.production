# Production Environment Configuration
# Modern Todo Application - Production Build

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
NODE_ENV=production

# Application Information
VITE_APP_NAME=Modern Todo
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Modern todo application with advanced features

# Logging Configuration
LOG_LEVEL=warn
ENABLE_CONSOLE_LOGS=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database Name
DATABASE_NAME=modern_todo_prod

# Local Database Path
DATABASE_PATH=./data/prod.db

# Database Connection Settings
CONNECTION_TIMEOUT=15000
MAX_CONNECTIONS=10
MIN_CONNECTIONS=2

# Database Security
ENABLE_WAL=true
ENCRYPTION_ENABLED=true
DATABASE_BACKUP_ENABLED=true
BACKUP_INTERVAL=3600000

# =============================================================================
# AUTHENTICATION & SECURITY CONFIGURATION
# =============================================================================

# Session Configuration
SESSION_TIMEOUT=3600000
SESSION_CLEANUP_INTERVAL=300000

# Password Policy
PASSWORD_MIN_LENGTH=12
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# Authentication Security
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=1800000
REQUIRE_2FA=false
ENABLE_BIOMETRIC=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50
AUTH_RATE_LIMIT_MAX_REQUESTS=3

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Encryption
ENCRYPTION_ALGORITHM=aes-256-gcm
SALT_ROUNDS=14

# Security Features
ENABLE_AUDIT_LOGGING=true
ENABLE_INTRUSION_DETECTION=true
ENABLE_SECURITY_HEADERS=true
ENABLE_CSRF_PROTECTION=true

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# Allowed Origins
ALLOWED_ORIGINS=https://api.motherduck.com

# =============================================================================
# ELECTRON SECURITY CONFIGURATION
# =============================================================================

# Electron Security Settings
DISABLE_NODE_INTEGRATION=true
ENABLE_CONTEXT_ISOLATION=true
ENABLE_SANDBOX=true
ENABLE_DEVTOOLS=false

# =============================================================================
# SYNC & OFFLINE CONFIGURATION
# =============================================================================

# Sync Settings
SYNC_INTERVAL=60000
ENABLE_OFFLINE_MODE=true
AUTO_SYNC=true
SYNC_RETRY_ATTEMPTS=3
SYNC_RETRY_DELAY=5000

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Memory Management
MEMORY_LIMIT=512
GC_INTERVAL=300000

# Cache Configuration
ENABLE_CACHING=true
CACHE_SIZE_LIMIT=100
CACHE_TTL=3600000

# Search Performance
SEARCH_INDEX_SIZE_LIMIT=10000
SEARCH_DEBOUNCE_MS=300

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Audit Logging
AUDIT_LOG_LEVEL=info
AUDIT_LOG_FILE=./logs/audit.log
AUDIT_LOG_MAX_SIZE=10485760
AUDIT_LOG_MAX_FILES=10

# Security Monitoring
SECURITY_LOG_FILE=./logs/security.log
FAILED_LOGIN_THRESHOLD=3
SUSPICIOUS_ACTIVITY_THRESHOLD=5

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_FILE=./logs/performance.log
PERFORMANCE_METRICS_INTERVAL=60000

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================

# Error Handling
EXPOSE_STACK_TRACES=false
DETAILED_ERROR_MESSAGES=false
ENABLE_ERROR_REPORTING=true

# Update Configuration
ENABLE_AUTO_UPDATES=true
UPDATE_CHECK_INTERVAL=86400000

# Build Optimization
ENABLE_TREE_SHAKING=true
ENABLE_CODE_SPLITTING=true
ENABLE_COMPRESSION=true
ENABLE_ASSET_OPTIMIZATION=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Advanced Features
ENABLE_ANALYTICS=true
ENABLE_TELEMETRY=false
ENABLE_CRASH_REPORTING=true
ENABLE_USAGE_STATISTICS=true

# Experimental Features
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=false

# =============================================================================
# RESOURCE LIMITS
# =============================================================================

# File Limits
MAX_FILE_SIZE=10485760
MAX_ATTACHMENT_SIZE=5242880
MAX_EXPORT_SIZE=52428800

# Database Limits
MAX_TODOS_PER_USER=10000
MAX_CATEGORIES_PER_USER=100
MAX_TAGS_PER_TODO=20

# Search Limits
MAX_SEARCH_RESULTS=1000
SEARCH_TIMEOUT=5000
