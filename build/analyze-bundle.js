#!/usr/bin/env node

/**
 * Bundle Analyzer Script
 * Analyzes production bundle for optimization opportunities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundleAnalyzer {
  constructor() {
    this.distPath = path.join(process.cwd(), 'dist', 'renderer');
    this.analysis = {
      files: [],
      totalSize: 0,
      recommendations: [],
      metrics: {}
    };
  }

  log(message, color = '\x1b[0m') {
    console.log(`${color}${message}\x1b[0m`);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  analyzeFiles() {
    if (!fs.existsSync(this.distPath)) {
      throw new Error('Build output not found. Run production build first.');
    }

    this.log('📊 Analyzing bundle files...', '\x1b[36m');

    const analyzeDirectory = (dir, relativePath = '') => {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativeFilePath = path.join(relativePath, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          analyzeDirectory(fullPath, relativeFilePath);
        } else {
          const fileInfo = {
            path: relativeFilePath,
            size: stats.size,
            type: this.getFileType(item),
            gzipSize: this.estimateGzipSize(fullPath)
          };
          
          this.analysis.files.push(fileInfo);
          this.analysis.totalSize += stats.size;
        }
      });
    };

    analyzeDirectory(this.distPath);
    this.analysis.files.sort((a, b) => b.size - a.size);
  }

  getFileType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const typeMap = {
      '.js': 'JavaScript',
      '.css': 'CSS',
      '.html': 'HTML',
      '.png': 'Image',
      '.jpg': 'Image',
      '.jpeg': 'Image',
      '.svg': 'Image',
      '.gif': 'Image',
      '.woff': 'Font',
      '.woff2': 'Font',
      '.ttf': 'Font',
      '.eot': 'Font',
      '.json': 'JSON',
      '.map': 'Source Map'
    };
    return typeMap[ext] || 'Other';
  }

  estimateGzipSize(filePath) {
    try {
      // Simple estimation: gzip typically reduces text files by 60-80%
      const content = fs.readFileSync(filePath, 'utf8');
      const isText = /\.(js|css|html|json|svg)$/i.test(filePath);
      return isText ? Math.round(content.length * 0.3) : fs.statSync(filePath).size;
    } catch {
      return fs.statSync(filePath).size;
    }
  }

  generateMetrics() {
    const filesByType = {};
    let totalGzipSize = 0;

    this.analysis.files.forEach(file => {
      if (!filesByType[file.type]) {
        filesByType[file.type] = { count: 0, size: 0, gzipSize: 0 };
      }
      filesByType[file.type].count++;
      filesByType[file.type].size += file.size;
      filesByType[file.type].gzipSize += file.gzipSize;
      totalGzipSize += file.gzipSize;
    });

    this.analysis.metrics = {
      filesByType,
      totalGzipSize,
      compressionRatio: (1 - totalGzipSize / this.analysis.totalSize) * 100,
      largestFiles: this.analysis.files.slice(0, 10)
    };
  }

  generateRecommendations() {
    const { files, totalSize, metrics } = this.analysis;
    
    // Check for large JavaScript files
    const largeJsFiles = files.filter(f => f.type === 'JavaScript' && f.size > 500 * 1024);
    if (largeJsFiles.length > 0) {
      this.analysis.recommendations.push({
        type: 'warning',
        message: `Large JavaScript files detected (${largeJsFiles.length} files > 500KB)`,
        suggestion: 'Consider code splitting or lazy loading for better performance'
      });
    }

    // Check total bundle size
    if (totalSize > 5 * 1024 * 1024) {
      this.analysis.recommendations.push({
        type: 'error',
        message: `Large total bundle size: ${this.formatBytes(totalSize)}`,
        suggestion: 'Consider removing unused dependencies or implementing code splitting'
      });
    } else if (totalSize > 2 * 1024 * 1024) {
      this.analysis.recommendations.push({
        type: 'warning',
        message: `Moderate bundle size: ${this.formatBytes(totalSize)}`,
        suggestion: 'Monitor bundle size growth and consider optimization'
      });
    }

    // Check for source maps in production
    const sourceMaps = files.filter(f => f.type === 'Source Map');
    if (sourceMaps.length > 0) {
      this.analysis.recommendations.push({
        type: 'warning',
        message: `Source maps found in production build (${sourceMaps.length} files)`,
        suggestion: 'Disable source maps for production builds to reduce bundle size'
      });
    }

    // Check compression ratio
    if (metrics.compressionRatio < 50) {
      this.analysis.recommendations.push({
        type: 'info',
        message: `Low compression ratio: ${metrics.compressionRatio.toFixed(1)}%`,
        suggestion: 'Enable gzip compression on your web server for better performance'
      });
    }

    // Check for duplicate dependencies
    const jsFiles = files.filter(f => f.type === 'JavaScript');
    if (jsFiles.length > 10) {
      this.analysis.recommendations.push({
        type: 'info',
        message: `Many JavaScript chunks: ${jsFiles.length} files`,
        suggestion: 'Review chunk splitting strategy to optimize loading performance'
      });
    }
  }

  displayResults() {
    const { files, totalSize, metrics } = this.analysis;

    console.log('\n' + '='.repeat(60));
    this.log('📦 BUNDLE ANALYSIS REPORT', '\x1b[1m\x1b[36m');
    console.log('='.repeat(60));

    // Summary
    this.log('\n📊 SUMMARY', '\x1b[1m\x1b[33m');
    this.log(`Total files: ${files.length}`);
    this.log(`Total size: ${this.formatBytes(totalSize)}`);
    this.log(`Estimated gzipped: ${this.formatBytes(metrics.totalGzipSize)}`);
    this.log(`Compression ratio: ${metrics.compressionRatio.toFixed(1)}%`);

    // Files by type
    this.log('\n📁 FILES BY TYPE', '\x1b[1m\x1b[33m');
    Object.entries(metrics.filesByType).forEach(([type, data]) => {
      this.log(`${type}: ${data.count} files, ${this.formatBytes(data.size)} (${this.formatBytes(data.gzipSize)} gzipped)`);
    });

    // Largest files
    this.log('\n📈 LARGEST FILES', '\x1b[1m\x1b[33m');
    metrics.largestFiles.forEach((file, index) => {
      const percentage = ((file.size / totalSize) * 100).toFixed(1);
      this.log(`${index + 1}. ${file.path} - ${this.formatBytes(file.size)} (${percentage}%)`);
    });

    // Recommendations
    if (this.analysis.recommendations.length > 0) {
      this.log('\n💡 RECOMMENDATIONS', '\x1b[1m\x1b[33m');
      this.analysis.recommendations.forEach(rec => {
        const color = rec.type === 'error' ? '\x1b[31m' : rec.type === 'warning' ? '\x1b[33m' : '\x1b[36m';
        const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : 'ℹ️';
        this.log(`${icon} ${rec.message}`, color);
        this.log(`   ${rec.suggestion}`, '\x1b[37m');
      });
    } else {
      this.log('\n✅ No optimization recommendations - bundle looks good!', '\x1b[32m');
    }

    console.log('\n' + '='.repeat(60));
  }

  saveReport() {
    const reportPath = path.join('build', 'reports', `bundle-analysis-${Date.now()}.json`);
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    
    const report = {
      timestamp: new Date().toISOString(),
      analysis: this.analysis,
      summary: {
        totalFiles: this.analysis.files.length,
        totalSize: this.analysis.totalSize,
        totalGzipSize: this.analysis.metrics.totalGzipSize,
        compressionRatio: this.analysis.metrics.compressionRatio
      }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`\n📄 Report saved: ${reportPath}`, '\x1b[32m');
  }

  async analyze() {
    try {
      this.analyzeFiles();
      this.generateMetrics();
      this.generateRecommendations();
      this.displayResults();
      this.saveReport();
    } catch (error) {
      this.log(`❌ Analysis failed: ${error.message}`, '\x1b[31m');
      process.exit(1);
    }
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.analyze();
}

module.exports = BundleAnalyzer;
