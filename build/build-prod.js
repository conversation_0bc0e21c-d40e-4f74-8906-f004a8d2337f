#!/usr/bin/env node

/**
 * Production Build Script
 * Comprehensive production build with optimization and validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { performance } = require('perf_hooks');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

class ProductionBuilder {
  constructor() {
    this.startTime = performance.now();
    this.buildSteps = [];
    this.errors = [];
    this.warnings = [];
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
  }

  logStep(step, status = 'info') {
    const statusColors = {
      info: colors.blue,
      success: colors.green,
      warning: colors.yellow,
      error: colors.red,
    };
    this.log(`[${status.toUpperCase()}] ${step}`, statusColors[status]);
  }

  async executeCommand(command, description) {
    this.logStep(`${description}...`, 'info');
    const stepStart = performance.now();
    
    try {
      const output = execSync(command, { 
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: process.cwd()
      });
      
      const duration = Math.round(performance.now() - stepStart);
      this.buildSteps.push({
        step: description,
        duration,
        status: 'success'
      });
      
      this.logStep(`${description} completed in ${duration}ms`, 'success');
      return output;
    } catch (error) {
      const duration = Math.round(performance.now() - stepStart);
      this.buildSteps.push({
        step: description,
        duration,
        status: 'error',
        error: error.message
      });
      
      this.errors.push(`${description}: ${error.message}`);
      this.logStep(`${description} failed: ${error.message}`, 'error');
      throw error;
    }
  }

  validateEnvironment() {
    this.logStep('Validating build environment', 'info');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const requiredNodeVersion = '18.0.0';
    if (!this.isVersionCompatible(nodeVersion.slice(1), requiredNodeVersion)) {
      throw new Error(`Node.js ${requiredNodeVersion} or higher required. Current: ${nodeVersion}`);
    }

    // Check if production environment file exists
    if (!fs.existsSync('.env.production')) {
      this.warnings.push('Production environment file (.env.production) not found');
    }

    // Check if required directories exist
    const requiredDirs = ['src', 'src/main', 'src/renderer'];
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        throw new Error(`Required directory missing: ${dir}`);
      }
    }

    this.logStep('Environment validation completed', 'success');
  }

  isVersionCompatible(current, required) {
    const currentParts = current.split('.').map(Number);
    const requiredParts = required.split('.').map(Number);
    
    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const requiredPart = requiredParts[i] || 0;
      
      if (currentPart > requiredPart) return true;
      if (currentPart < requiredPart) return false;
    }
    
    return true;
  }

  async cleanBuildDirectory() {
    this.logStep('Cleaning build directory', 'info');
    
    if (fs.existsSync('dist')) {
      await this.executeCommand('rm -rf dist', 'Remove existing dist directory');
    }
    
    if (fs.existsSync('release')) {
      await this.executeCommand('rm -rf release', 'Remove existing release directory');
    }
  }

  async installDependencies() {
    await this.executeCommand('npm ci --production=false', 'Install dependencies');
  }

  async runLinting() {
    try {
      await this.executeCommand('npm run lint', 'Run ESLint');
    } catch (error) {
      this.warnings.push('Linting failed - continuing with build');
    }
  }

  async runTests() {
    try {
      await this.executeCommand('npm test -- --passWithNoTests --watchAll=false', 'Run tests');
    } catch (error) {
      this.warnings.push('Tests failed - continuing with build');
    }
  }

  async buildMain() {
    await this.executeCommand('npm run build:main', 'Build main process');
  }

  async buildRenderer() {
    // Use production Vite config if it exists
    const viteConfig = fs.existsSync('vite.config.prod.ts') 
      ? '--config vite.config.prod.ts' 
      : '';
    
    await this.executeCommand(
      `npx vite build ${viteConfig}`,
      'Build renderer process with production optimizations'
    );
  }

  async analyzeBundle() {
    this.logStep('Analyzing bundle size', 'info');
    
    try {
      // Generate bundle analysis if rollup-plugin-analyzer is available
      const distPath = path.join(process.cwd(), 'dist', 'renderer');
      if (fs.existsSync(distPath)) {
        const files = fs.readdirSync(distPath, { recursive: true });
        const jsFiles = files.filter(file => file.endsWith('.js'));
        const cssFiles = files.filter(file => file.endsWith('.css'));
        
        let totalSize = 0;
        const fileSizes = [];
        
        [...jsFiles, ...cssFiles].forEach(file => {
          const filePath = path.join(distPath, file);
          if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            totalSize += stats.size;
            fileSizes.push({
              file,
              size: this.formatBytes(stats.size)
            });
          }
        });
        
        this.log(`\n${colors.cyan}Bundle Analysis:${colors.reset}`);
        this.log(`Total bundle size: ${this.formatBytes(totalSize)}`);
        this.log(`JavaScript files: ${jsFiles.length}`);
        this.log(`CSS files: ${cssFiles.length}`);
        
        if (totalSize > 5 * 1024 * 1024) { // 5MB
          this.warnings.push(`Large bundle size: ${this.formatBytes(totalSize)}`);
        }
      }
    } catch (error) {
      this.warnings.push(`Bundle analysis failed: ${error.message}`);
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async validateBuild() {
    this.logStep('Validating build output', 'info');
    
    const requiredFiles = [
      'dist/main/index.js',
      'dist/renderer/index.html',
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required build output missing: ${file}`);
      }
    }
    
    this.logStep('Build validation completed', 'success');
  }

  async generateBuildReport() {
    const endTime = performance.now();
    const totalDuration = Math.round(endTime - this.startTime);
    
    const report = {
      timestamp: new Date().toISOString(),
      duration: totalDuration,
      steps: this.buildSteps,
      errors: this.errors,
      warnings: this.warnings,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    };
    
    // Write build report
    const reportPath = path.join('build', 'reports', `build-${Date.now()}.json`);
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Display summary
    this.log(`\n${colors.bright}=== BUILD SUMMARY ===${colors.reset}`);
    this.log(`Total duration: ${totalDuration}ms`);
    this.log(`Steps completed: ${this.buildSteps.length}`);
    this.log(`Errors: ${this.errors.length}`, this.errors.length > 0 ? colors.red : colors.green);
    this.log(`Warnings: ${this.warnings.length}`, this.warnings.length > 0 ? colors.yellow : colors.green);
    this.log(`Report saved: ${reportPath}`);
    
    if (this.warnings.length > 0) {
      this.log(`\n${colors.yellow}Warnings:${colors.reset}`);
      this.warnings.forEach(warning => this.log(`  - ${warning}`, colors.yellow));
    }
    
    if (this.errors.length > 0) {
      this.log(`\n${colors.red}Errors:${colors.reset}`);
      this.errors.forEach(error => this.log(`  - ${error}`, colors.red));
      process.exit(1);
    }
    
    this.log(`\n${colors.green}✅ Production build completed successfully!${colors.reset}`);
  }

  async build() {
    try {
      this.log(`${colors.bright}🚀 Starting production build...${colors.reset}\n`);
      
      this.validateEnvironment();
      await this.cleanBuildDirectory();
      await this.installDependencies();
      await this.runLinting();
      await this.runTests();
      await this.buildMain();
      await this.buildRenderer();
      await this.analyzeBundle();
      await this.validateBuild();
      await this.generateBuildReport();
      
    } catch (error) {
      this.errors.push(error.message);
      await this.generateBuildReport();
    }
  }
}

// Run the build
if (require.main === module) {
  const builder = new ProductionBuilder();
  builder.build();
}

module.exports = ProductionBuilder;
