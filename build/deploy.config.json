{"platforms": ["win32", "darwin", "linux"], "targets": {"win32": ["nsis", "portable"], "darwin": ["dmg", "zip"], "linux": ["AppImage", "deb", "rpm"]}, "signing": {"enabled": false, "identity": null, "appleId": null, "description": "Set CODE_SIGN_IDENTITY and NOTARIZE_APPLE_ID environment variables to enable signing"}, "distribution": {"enabled": false, "channels": ["stable", "beta"], "autoPublish": false, "updateServer": null}, "optimization": {"compression": "maximum", "removeDevDependencies": true, "pruneNodeModules": true, "minifyCode": true}, "validation": {"minimumPackageSize": 52428800, "maximumPackageSize": 524288000, "requiredFiles": ["dist/main/index.js", "dist/renderer/index.html", "package.json"]}, "backup": {"enabled": true, "retentionDays": 30, "includeSourceMaps": false}, "notifications": {"enabled": false, "webhook": null, "email": null}}