#!/usr/bin/env node

/**
 * Deployment Script
 * Handles production deployment with validation and rollback capabilities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class Deployer {
  constructor() {
    this.config = this.loadConfig();
    this.deploymentId = `deploy-${Date.now()}`;
    this.backupPath = path.join('backups', this.deploymentId);
  }

  loadConfig() {
    const configPath = path.join('build', 'deploy.config.json');
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    }
    
    // Default configuration
    return {
      platforms: ['win32', 'darwin', 'linux'],
      targets: {
        win32: ['nsis', 'portable'],
        darwin: ['dmg', 'zip'],
        linux: ['AppImage', 'deb', 'rpm']
      },
      signing: {
        enabled: false,
        identity: process.env.CODE_SIGN_IDENTITY,
        appleId: process.env.NOTARIZE_APPLE_ID
      },
      distribution: {
        enabled: false,
        channels: ['stable', 'beta'],
        autoPublish: false
      }
    };
  }

  log(message, color = '\x1b[0m') {
    console.log(`${color}[DEPLOY] ${message}\x1b[0m`);
  }

  async executeCommand(command, description) {
    this.log(`${description}...`, '\x1b[36m');
    try {
      const output = execSync(command, { 
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: process.cwd()
      });
      this.log(`✅ ${description} completed`, '\x1b[32m');
      return output;
    } catch (error) {
      this.log(`❌ ${description} failed: ${error.message}`, '\x1b[31m');
      throw error;
    }
  }

  validatePrerequisites() {
    this.log('Validating deployment prerequisites', '\x1b[33m');
    
    // Check if build exists
    if (!fs.existsSync('dist')) {
      throw new Error('Build output not found. Run production build first.');
    }
    
    // Check required files
    const requiredFiles = [
      'dist/main/index.js',
      'dist/renderer/index.html',
      'package.json'
    ];
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }
    
    // Check environment variables for signing
    if (this.config.signing.enabled) {
      if (!this.config.signing.identity) {
        this.log('⚠️ Code signing enabled but no identity provided', '\x1b[33m');
      }
    }
    
    this.log('✅ Prerequisites validated', '\x1b[32m');
  }

  createBackup() {
    this.log('Creating deployment backup', '\x1b[33m');
    
    fs.mkdirSync(this.backupPath, { recursive: true });
    
    // Backup current release if it exists
    if (fs.existsSync('release')) {
      execSync(`cp -r release "${path.join(this.backupPath, 'release')}"`);
    }
    
    // Create deployment manifest
    const manifest = {
      deploymentId: this.deploymentId,
      timestamp: new Date().toISOString(),
      version: this.getVersion(),
      platforms: this.config.platforms,
      config: this.config
    };
    
    fs.writeFileSync(
      path.join(this.backupPath, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );
    
    this.log(`✅ Backup created: ${this.backupPath}`, '\x1b[32m');
  }

  getVersion() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    return packageJson.version;
  }

  async buildForPlatform(platform) {
    this.log(`Building for ${platform}`, '\x1b[36m');
    
    const targets = this.config.targets[platform] || ['default'];
    const targetFlag = targets.join(',');
    
    let buildCommand = 'npm run package';
    
    // Platform-specific build commands
    switch (platform) {
      case 'win32':
        buildCommand += ` -- --win --x64 -p never`;
        if (targets.length > 0) {
          buildCommand += ` --win ${targetFlag}`;
        }
        break;
      case 'darwin':
        buildCommand += ` -- --mac --x64 -p never`;
        if (targets.length > 0) {
          buildCommand += ` --mac ${targetFlag}`;
        }
        break;
      case 'linux':
        buildCommand += ` -- --linux --x64 -p never`;
        if (targets.length > 0) {
          buildCommand += ` --linux ${targetFlag}`;
        }
        break;
    }
    
    await this.executeCommand(buildCommand, `Build ${platform} packages`);
  }

  async signPackages() {
    if (!this.config.signing.enabled) {
      this.log('Code signing disabled, skipping', '\x1b[33m');
      return;
    }
    
    this.log('Signing packages', '\x1b[36m');
    
    // This would implement actual code signing logic
    // For now, just log the intent
    this.log('⚠️ Code signing implementation needed', '\x1b[33m');
  }

  validatePackages() {
    this.log('Validating generated packages', '\x1b[33m');
    
    if (!fs.existsSync('release')) {
      throw new Error('No packages found in release directory');
    }
    
    const packages = fs.readdirSync('release');
    if (packages.length === 0) {
      throw new Error('No packages generated');
    }
    
    // Validate package integrity
    packages.forEach(pkg => {
      const pkgPath = path.join('release', pkg);
      const stats = fs.statSync(pkgPath);
      
      if (stats.size === 0) {
        throw new Error(`Empty package: ${pkg}`);
      }
      
      // Basic size validation (packages should be at least 50MB for Electron apps)
      if (stats.size < 50 * 1024 * 1024) {
        this.log(`⚠️ Small package size for ${pkg}: ${this.formatBytes(stats.size)}`, '\x1b[33m');
      }
    });
    
    this.log(`✅ ${packages.length} packages validated`, '\x1b[32m');
    return packages;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateChecksums(packages) {
    this.log('Generating checksums', '\x1b[33m');
    
    const checksums = {};
    
    packages.forEach(pkg => {
      const pkgPath = path.join('release', pkg);
      try {
        const checksum = execSync(`shasum -a 256 "${pkgPath}"`, { encoding: 'utf8' });
        checksums[pkg] = checksum.split(' ')[0];
      } catch (error) {
        this.log(`⚠️ Failed to generate checksum for ${pkg}`, '\x1b[33m');
      }
    });
    
    // Save checksums file
    fs.writeFileSync(
      path.join('release', 'checksums.txt'),
      Object.entries(checksums)
        .map(([file, hash]) => `${hash}  ${file}`)
        .join('\n')
    );
    
    this.log('✅ Checksums generated', '\x1b[32m');
  }

  generateReleaseNotes() {
    this.log('Generating release notes', '\x1b[33m');
    
    const version = this.getVersion();
    const releaseNotes = {
      version,
      date: new Date().toISOString().split('T')[0],
      deploymentId: this.deploymentId,
      platforms: this.config.platforms,
      features: [
        'Production-optimized build',
        'Enhanced security features',
        'Performance improvements'
      ],
      bugfixes: [],
      breaking: []
    };
    
    fs.writeFileSync(
      path.join('release', 'release-notes.json'),
      JSON.stringify(releaseNotes, null, 2)
    );
    
    this.log('✅ Release notes generated', '\x1b[32m');
  }

  async deploy() {
    try {
      this.log('🚀 Starting deployment process', '\x1b[1m\x1b[36m');
      
      this.validatePrerequisites();
      this.createBackup();
      
      // Clean previous release
      if (fs.existsSync('release')) {
        await this.executeCommand('rm -rf release', 'Clean previous release');
      }
      
      // Build for all configured platforms
      for (const platform of this.config.platforms) {
        if (process.platform === platform || process.env.CI) {
          await this.buildForPlatform(platform);
        } else {
          this.log(`⏭️ Skipping ${platform} (not current platform)`, '\x1b[33m');
        }
      }
      
      await this.signPackages();
      const packages = this.validatePackages();
      this.generateChecksums(packages);
      this.generateReleaseNotes();
      
      // Display summary
      this.log('\n' + '='.repeat(50), '\x1b[1m');
      this.log('🎉 DEPLOYMENT COMPLETED', '\x1b[1m\x1b[32m');
      this.log('='.repeat(50), '\x1b[1m');
      this.log(`Version: ${this.getVersion()}`);
      this.log(`Deployment ID: ${this.deploymentId}`);
      this.log(`Packages: ${packages.length}`);
      this.log(`Backup: ${this.backupPath}`);
      
      packages.forEach(pkg => {
        const pkgPath = path.join('release', pkg);
        const size = this.formatBytes(fs.statSync(pkgPath).size);
        this.log(`  📦 ${pkg} (${size})`);
      });
      
      this.log('\n✅ Deployment ready for distribution!', '\x1b[1m\x1b[32m');
      
    } catch (error) {
      this.log(`❌ Deployment failed: ${error.message}`, '\x1b[31m');
      this.log(`💾 Backup available at: ${this.backupPath}`, '\x1b[33m');
      process.exit(1);
    }
  }
}

// Run the deployment
if (require.main === module) {
  const deployer = new Deployer();
  deployer.deploy();
}

module.exports = Deployer;
