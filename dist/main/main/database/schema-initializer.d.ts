/**
 * Database Schema Initializer for MotherDuck
 *
 * Handles automatic database schema creation and seeding when connecting
 * to a new or empty MotherDuck database for the first time.
 */
export interface InitializationResult {
    wasInitialized: boolean;
    tablesCreated: string[];
    seedDataInserted: boolean;
    initializationTime: number;
    message: string;
}
export declare class DatabaseSchemaInitializer {
    private static readonly SCHEMA_VERSION;
    private static readonly INITIALIZATION_TABLE;
    /**
     * Check if database needs initialization
     */
    needsInitialization(): Promise<boolean>;
    /**
     * Initialize database schema and seed data
     */
    initializeDatabase(includeSeedData?: boolean): Promise<InitializationResult>;
    /**
     * Create initialization tracking table
     */
    private createInitializationTable;
    /**
     * Create core application tables
     */
    private createCoreTables;
    /**
     * Create database indexes for performance
     */
    private createIndexes;
    /**
     * Insert seed data for development and testing
     */
    private insertSeedData;
    /**
     * Record initialization start
     */
    private recordInitializationStart;
    /**
     * Record successful initialization completion
     */
    private recordInitializationComplete;
    /**
     * Record initialization error
     */
    private recordInitializationError;
    /**
     * Get initialization history
     */
    getInitializationHistory(): Promise<any[]>;
}
export declare const schemaInitializer: DatabaseSchemaInitializer;
