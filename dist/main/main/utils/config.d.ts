import { DatabaseConfig, MCPConfig } from '@shared/types';
export declare class ConfigService {
    private static instance;
    private constructor();
    static getInstance(): ConfigService;
    getDatabaseConfig(): DatabaseConfig;
    getMCPConfig(): MCPConfig;
    getAppConfig(): {
        nodeEnv: string;
        logLevel: string;
        sessionTimeout: number;
        maxLoginAttempts: number;
        lockoutDuration: number;
        syncInterval: number;
        enableOfflineMode: boolean;
        autoSync: boolean;
        enableDevtools: boolean;
    };
    getSecurityConfig(): {
        encryptionEnabled: boolean;
        require2FA: boolean;
        enableBiometric: boolean;
        passwordMinLength: number;
        passwordRequireUppercase: boolean;
        passwordRequireLowercase: boolean;
        passwordRequireNumbers: boolean;
        passwordRequireSpecial: boolean;
        sessionTimeout: number;
        sessionSecret: string;
        maxLoginAttempts: number;
        lockoutDuration: number;
        encryptionAlgorithm: string;
        encryptionKey: string;
        saltRounds: number;
        rateLimitWindowMs: number;
        rateLimitMaxRequests: number;
        authRateLimitMaxRequests: number;
        enableAuditLogging: boolean;
        enableIntrusionDetection: boolean;
        enableSecurityHeaders: boolean;
        enableCSRFProtection: boolean;
        cspEnabled: boolean;
        cspReportOnly: boolean;
        disableNodeIntegration: boolean;
        enableContextIsolation: boolean;
        enableSandbox: boolean;
        auditLogLevel: string;
        auditLogFile: string;
        auditLogMaxSize: number;
        auditLogMaxFiles: number;
        securityLogFile: string;
        failedLoginThreshold: number;
        suspiciousActivityThreshold: number;
        exposeStackTraces: boolean;
        detailedErrorMessages: boolean;
        enableAutoUpdates: boolean;
    };
    private generateSecureSecret;
    private generateEncryptionKey;
    validateConfig(): void;
    private validateSecurityConfig;
    isDevelopment(): boolean;
    isProduction(): boolean;
}
export declare const config: ConfigService;
