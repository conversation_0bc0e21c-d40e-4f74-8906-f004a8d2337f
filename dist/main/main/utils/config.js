"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.ConfigService = void 0;
const dotenv = __importStar(require("dotenv"));
// Load environment variables
dotenv.config();
class ConfigService {
    static instance;
    constructor() { }
    static getInstance() {
        if (!ConfigService.instance) {
            ConfigService.instance = new ConfigService();
        }
        return ConfigService.instance;
    }
    getDatabaseConfig() {
        return {
            path: process.env.DATABASE_PATH || './data/todo.db',
            motherduckToken: process.env.MOTHERDUCK_TOKEN,
            connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
            maxConnections: parseInt(process.env.MAX_CONNECTIONS || '5'),
            minConnections: parseInt(process.env.MIN_CONNECTIONS || '1'),
            enableWAL: process.env.ENABLE_WAL === 'true',
            enableEncryption: process.env.ENCRYPTION_ENABLED === 'true',
        };
    }
    getMCPConfig() {
        // MotherDuck token is now optional since we support multiple MCP servers
        const motherduckToken = process.env.MOTHERDUCK_TOKEN || '';
        return {
            motherduckToken,
            databaseName: process.env.DATABASE_NAME || 'todo_app',
            connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
            retryAttempts: parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'),
            retryDelay: parseInt(process.env.MCP_RETRY_DELAY || '1000'),
            enableMetrics: process.env.ENABLE_MCP_METRICS === 'true',
            logLevel: process.env.LOG_LEVEL || 'info',
        };
    }
    getAppConfig() {
        return {
            nodeEnv: process.env.NODE_ENV || 'development',
            logLevel: process.env.LOG_LEVEL || 'debug',
            sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
            maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
            lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
            syncInterval: parseInt(process.env.SYNC_INTERVAL || '30000'),
            enableOfflineMode: process.env.ENABLE_OFFLINE_MODE === 'true',
            autoSync: process.env.AUTO_SYNC === 'true',
            enableDevtools: process.env.ENABLE_DEVTOOLS === 'true',
        };
    }
    getSecurityConfig() {
        return {
            // Basic security settings
            encryptionEnabled: process.env.ENCRYPTION_ENABLED === 'true',
            require2FA: process.env.REQUIRE_2FA === 'true',
            enableBiometric: process.env.ENABLE_BIOMETRIC === 'true',
            // Password policy
            passwordMinLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '12'),
            passwordRequireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
            passwordRequireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
            passwordRequireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
            passwordRequireSpecial: process.env.PASSWORD_REQUIRE_SPECIAL !== 'false',
            // Session management
            sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
            sessionSecret: process.env.SESSION_SECRET || this.generateSecureSecret(),
            maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
            lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
            // Encryption
            encryptionAlgorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
            encryptionKey: process.env.ENCRYPTION_KEY || this.generateEncryptionKey(),
            saltRounds: parseInt(process.env.SALT_ROUNDS || '12'),
            // Rate limiting
            rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
            rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
            authRateLimitMaxRequests: parseInt(process.env.AUTH_RATE_LIMIT_MAX_REQUESTS || '5'),
            // Security features
            enableAuditLogging: process.env.ENABLE_AUDIT_LOGGING !== 'false',
            enableIntrusionDetection: process.env.ENABLE_INTRUSION_DETECTION !== 'false',
            enableSecurityHeaders: process.env.ENABLE_SECURITY_HEADERS !== 'false',
            enableCSRFProtection: process.env.ENABLE_CSRF_PROTECTION !== 'false',
            // Content Security Policy
            cspEnabled: process.env.CSP_ENABLED !== 'false',
            cspReportOnly: process.env.CSP_REPORT_ONLY === 'true' || this.isDevelopment(),
            // Electron security
            disableNodeIntegration: process.env.DISABLE_NODE_INTEGRATION !== 'false',
            enableContextIsolation: process.env.ENABLE_CONTEXT_ISOLATION !== 'false',
            enableSandbox: process.env.ENABLE_SANDBOX === 'true',
            // Monitoring
            auditLogLevel: process.env.AUDIT_LOG_LEVEL || 'info',
            auditLogFile: process.env.AUDIT_LOG_FILE || './logs/audit.log',
            auditLogMaxSize: parseInt(process.env.AUDIT_LOG_MAX_SIZE || '10485760'),
            auditLogMaxFiles: parseInt(process.env.AUDIT_LOG_MAX_FILES || '5'),
            securityLogFile: process.env.SECURITY_LOG_FILE || './logs/security.log',
            failedLoginThreshold: parseInt(process.env.FAILED_LOGIN_THRESHOLD || '3'),
            suspiciousActivityThreshold: parseInt(process.env.SUSPICIOUS_ACTIVITY_THRESHOLD || '10'),
            // Production settings
            exposeStackTraces: process.env.EXPOSE_STACK_TRACES === 'true' || this.isDevelopment(),
            detailedErrorMessages: process.env.DETAILED_ERROR_MESSAGES === 'true' || this.isDevelopment(),
            enableAutoUpdates: process.env.ENABLE_AUTO_UPDATES !== 'false',
        };
    }
    generateSecureSecret() {
        const crypto = require('crypto');
        return crypto.randomBytes(32).toString('hex');
    }
    generateEncryptionKey() {
        const crypto = require('crypto');
        return crypto.randomBytes(32).toString('hex');
    }
    validateConfig() {
        const requiredEnvVars = ['DATABASE_NAME'];
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        if (missingVars.length > 0) {
            throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
        }
        // Validate security configuration
        this.validateSecurityConfig();
        // Warn about missing MotherDuck token for cloud features
        if (!process.env.MOTHERDUCK_TOKEN) {
            console.warn('Warning: MOTHERDUCK_TOKEN not set. Cloud sync features will be disabled.');
        }
    }
    validateSecurityConfig() {
        const securityConfig = this.getSecurityConfig();
        const errors = [];
        // Validate session secret
        if (!securityConfig.sessionSecret || securityConfig.sessionSecret.length < 32) {
            errors.push('SESSION_SECRET must be at least 32 characters long');
        }
        // Validate encryption key if encryption is enabled
        if (securityConfig.encryptionEnabled &&
            (!securityConfig.encryptionKey || securityConfig.encryptionKey.length < 32)) {
            errors.push('ENCRYPTION_KEY must be at least 32 characters long when encryption is enabled');
        }
        // Validate password policy
        if (securityConfig.passwordMinLength < 8) {
            errors.push('PASSWORD_MIN_LENGTH must be at least 8 characters');
        }
        // Validate rate limiting
        if (securityConfig.maxLoginAttempts < 1) {
            errors.push('MAX_LOGIN_ATTEMPTS must be at least 1');
        }
        if (securityConfig.lockoutDuration < 60000) {
            errors.push('LOCKOUT_DURATION must be at least 60000ms (1 minute)');
        }
        // Production-specific validations
        if (this.isProduction()) {
            if (securityConfig.exposeStackTraces) {
                errors.push('EXPOSE_STACK_TRACES should be false in production');
            }
            if (securityConfig.detailedErrorMessages) {
                errors.push('DETAILED_ERROR_MESSAGES should be false in production');
            }
            if (!securityConfig.enableAuditLogging) {
                errors.push('ENABLE_AUDIT_LOGGING should be true in production');
            }
        }
        if (errors.length > 0) {
            throw new Error(`Security configuration validation failed:\n${errors.join('\n')}`);
        }
    }
    isDevelopment() {
        return process.env.NODE_ENV === 'development';
    }
    isProduction() {
        return process.env.NODE_ENV === 'production';
    }
}
exports.ConfigService = ConfigService;
// Export singleton instance
exports.config = ConfigService.getInstance();
