import { ValidationError } from '@shared/utils/validation';
/**
 * Enhanced error types for better error handling
 */
export declare enum ErrorCode {
    VALIDATION_ERROR = "VALIDATION_ERROR",
    TITLE_REQUIRED = "TITLE_REQUIRED",
    TITLE_TOO_SHORT = "TITLE_TOO_SHORT",
    TITLE_TOO_LONG = "TITLE_TOO_LONG",
    DESCRIPTION_TOO_LONG = "DESCRIPTION_TOO_LONG",
    INVALID_STATUS = "INVALID_STATUS",
    INVALID_PRIORITY = "INVALID_PRIORITY",
    INVALID_DATE_FORMAT = "INVALID_DATE_FORMAT",
    INVALID_DATE = "INVALID_DATE",
    DUE_DATE_IN_PAST = "DUE_DATE_IN_PAST",
    DUE_DATE_TOO_FAR = "DUE_DATE_TOO_FAR",
    REMINDER_IN_PAST = "REMINDER_IN_PAST",
    REMINDER_AFTER_DUE_DATE = "REMINDER_AFTER_DUE_DATE",
    INVALID_REMINDER_FORMAT = "INVALID_REMINDER_FORMAT",
    INVALID_REMINDER_DATE = "INVALID_REMINDER_DATE",
    TAGS_NOT_ARRAY = "TAGS_NOT_ARRAY",
    TOO_MANY_TAGS = "TOO_MANY_TAGS",
    TAG_NOT_STRING = "TAG_NOT_STRING",
    TAG_EMPTY = "TAG_EMPTY",
    TAG_TOO_LONG = "TAG_TOO_LONG",
    DUPLICATE_TAGS = "DUPLICATE_TAGS",
    INVALID_DURATION_FORMAT = "INVALID_DURATION_FORMAT",
    DURATION_TOO_LONG = "DURATION_TOO_LONG",
    DURATION_TOO_SHORT = "DURATION_TOO_SHORT",
    USER_ID_REQUIRED = "USER_ID_REQUIRED",
    INVALID_USER_ID_FORMAT = "INVALID_USER_ID_FORMAT",
    INVALID_CATEGORY_ID_FORMAT = "INVALID_CATEGORY_ID_FORMAT",
    INVALID_STATUS_TRANSITION = "INVALID_STATUS_TRANSITION",
    DATABASE_ERROR = "DATABASE_ERROR",
    QUERY_ERROR = "QUERY_ERROR",
    TRANSACTION_ERROR = "TRANSACTION_ERROR",
    CONNECTION_ERROR = "CONNECTION_ERROR",
    CONSTRAINT_VIOLATION = "CONSTRAINT_VIOLATION",
    RECORD_NOT_FOUND = "RECORD_NOT_FOUND",
    DUPLICATE_RECORD = "DUPLICATE_RECORD",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    INVALID_SESSION = "INVALID_SESSION",
    SESSION_EXPIRED = "SESSION_EXPIRED",
    UNAUTHORIZED = "UNAUTHORIZED",
    FORBIDDEN = "FORBIDDEN",
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION",
    INVALID_OPERATION = "INVALID_OPERATION",
    RESOURCE_CONFLICT = "RESOURCE_CONFLICT",
    NETWORK_ERROR = "NETWORK_ERROR",
    API_ERROR = "API_ERROR",
    TIMEOUT_ERROR = "TIMEOUT_ERROR",
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
    UNKNOWN_ERROR = "UNKNOWN_ERROR",
    INTERNAL_ERROR = "INTERNAL_ERROR"
}
export declare enum ErrorSeverity {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
export interface ErrorContext {
    userId?: string;
    todoId?: string;
    operation?: string;
    timestamp?: Date;
    userAgent?: string;
    sessionId?: string;
    [key: string]: any;
}
/**
 * Enhanced validation error class
 */
export declare class TodoValidationError extends Error {
    readonly code: ErrorCode;
    readonly field?: string;
    readonly value?: any;
    readonly severity: ErrorSeverity;
    readonly context?: ErrorContext;
    readonly validationErrors: ValidationError[];
    constructor(message: string, validationErrors?: ValidationError[], options?: {
        code?: ErrorCode;
        field?: string;
        value?: any;
        severity?: ErrorSeverity;
        context?: ErrorContext;
    });
    /**
     * Get user-friendly error message
     */
    getUserMessage(): string;
    /**
     * Get errors for a specific field
     */
    getFieldErrors(field: string): ValidationError[];
    /**
     * Check if error is for a specific field
     */
    hasFieldError(field: string): boolean;
    /**
     * Convert to JSON for logging/debugging
     */
    toJSON(): {
        name: string;
        message: string;
        code: ErrorCode;
        field: string | undefined;
        value: any;
        severity: ErrorSeverity;
        context: ErrorContext | undefined;
        validationErrors: ValidationError[];
        stack: string | undefined;
    };
}
/**
 * Enhanced database error class
 */
export declare class TodoDatabaseError extends Error {
    readonly code: ErrorCode;
    readonly severity: ErrorSeverity;
    readonly context?: ErrorContext;
    readonly originalError?: Error;
    constructor(message: string, options?: {
        code?: ErrorCode;
        severity?: ErrorSeverity;
        context?: ErrorContext;
        originalError?: Error;
    });
    /**
     * Get user-friendly error message
     */
    getUserMessage(): string;
    /**
     * Convert to JSON for logging/debugging
     */
    toJSON(): {
        name: string;
        message: string;
        code: ErrorCode;
        severity: ErrorSeverity;
        context: ErrorContext | undefined;
        originalError: string | undefined;
        stack: string | undefined;
    };
}
/**
 * Enhanced business logic error class
 */
export declare class TodoBusinessError extends Error {
    readonly code: ErrorCode;
    readonly severity: ErrorSeverity;
    readonly context?: ErrorContext;
    constructor(message: string, options?: {
        code?: ErrorCode;
        severity?: ErrorSeverity;
        context?: ErrorContext;
    });
    /**
     * Convert to JSON for logging/debugging
     */
    toJSON(): {
        name: string;
        message: string;
        code: ErrorCode;
        severity: ErrorSeverity;
        context: ErrorContext | undefined;
        stack: string | undefined;
    };
}
/**
 * Error factory functions
 */
export declare class TodoErrorFactory {
    static createValidationError(validationErrors: ValidationError[], context?: ErrorContext): TodoValidationError;
    static createDatabaseError(message: string, originalError?: Error, context?: ErrorContext): TodoDatabaseError;
    static createNotFoundError(resource?: string, context?: ErrorContext): TodoDatabaseError;
    static createBusinessError(message: string, context?: ErrorContext): TodoBusinessError;
}
