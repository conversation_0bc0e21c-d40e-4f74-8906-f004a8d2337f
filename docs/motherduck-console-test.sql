-- MotherDuck Console Test Script
-- Copy and paste these commands into the MotherDuck web console
-- at https://app.motherduck.com

-- 1. CREATE A SIMPLE TEST TABLE
CREATE TABLE IF NOT EXISTS console_test_todos (
  id INTEGER PRIMARY KEY,
  title VARCHAR NOT NULL,
  description TEXT,
  priority VARCHAR DEFAULT 'medium',
  completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT current_timestamp
);

-- 2. INSERT SINGLE RECORD
INSERT INTO console_test_todos (id, title, description, priority)
VALUES (1, 'Learn MotherDuck', 'Understand how to insert data into MotherDuck cloud database', 'high');

-- 3. INSERT MULTIPLE RECORDS
INSERT INTO console_test_todos (id, title, description, priority, completed)
VALUES 
  (2, 'Setup Database', 'Configure MotherDuck connection in application', 'high', true),
  (3, 'Write Tests', 'Create unit tests for database operations', 'medium', false),
  (4, 'Deploy App', 'Deploy application to production environment', 'low', false),
  (5, 'Documentation', 'Write comprehensive user documentation', 'medium', false);

-- 4. INSERT WITH CURRENT TIMESTAMP
INSERT INTO console_test_todos (id, title, description, created_at)
VALUES (6, 'Timestamp Test', 'Testing current timestamp insertion', current_timestamp);

-- 5. INSERT WITH SPECIAL CHARACTERS
INSERT INTO console_test_todos (id, title, description)
VALUES (7, 'Task with ''quotes''', 'Description with ''single quotes'' and special chars: @#$%');

-- 6. INSERT AND RETURN DATA
INSERT INTO console_test_todos (id, title, description, priority)
VALUES (8, 'Return Test', 'Testing RETURNING clause functionality', 'high')
RETURNING id, title, created_at;

-- 7. QUERY ALL DATA
SELECT 
  id,
  title,
  description,
  priority,
  completed,
  created_at
FROM console_test_todos 
ORDER BY id;

-- 8. COUNT BY PRIORITY
SELECT 
  priority,
  COUNT(*) as count,
  COUNT(CASE WHEN completed THEN 1 END) as completed_count
FROM console_test_todos 
GROUP BY priority
ORDER BY priority;

-- 9. UPDATE DATA
UPDATE console_test_todos 
SET completed = true, priority = 'high'
WHERE id = 3;

-- 10. VERIFY UPDATE
SELECT id, title, priority, completed 
FROM console_test_todos 
WHERE id = 3;

-- 11. ADVANCED QUERY - RECENT TASKS
SELECT 
  title, 
  priority,
  created_at,
  CASE 
    WHEN completed THEN '✅ Done'
    ELSE '⏳ Pending'
  END as status
FROM console_test_todos 
WHERE created_at >= current_timestamp - INTERVAL '1 hour'
ORDER BY created_at DESC;

-- 12. DELETE A RECORD
DELETE FROM console_test_todos 
WHERE id = 7;

-- 13. FINAL COUNT
SELECT 
  COUNT(*) as total_tasks,
  COUNT(CASE WHEN completed THEN 1 END) as completed_tasks,
  COUNT(CASE WHEN NOT completed THEN 1 END) as pending_tasks
FROM console_test_todos;

-- 14. BULK INSERT EXAMPLE
INSERT INTO console_test_todos (id, title, priority)
VALUES 
  (10, 'Bulk Task 1', 'low'),
  (11, 'Bulk Task 2', 'medium'),
  (12, 'Bulk Task 3', 'high');

-- 15. FINAL QUERY - ALL DATA WITH FORMATTING
SELECT 
  id,
  title,
  priority,
  CASE 
    WHEN completed THEN '✅'
    ELSE '⏳'
  END as status,
  DATE_TRUNC('minute', created_at) as created_time
FROM console_test_todos 
ORDER BY priority DESC, created_at ASC;

-- 16. CLEANUP (OPTIONAL - uncomment to remove test data)
-- DROP TABLE console_test_todos;
