# MotherDuck Data Insertion Guide

## Overview

MotherDuck uses **identical SQL syntax** to local DuckDB. There are **no differences** in how you insert, update, or query data between local DuckDB and MotherDuck cloud database.

## 1. Basic Data Insertion Syntax

### Single Record Insert
```sql
INSERT INTO table_name (column1, column2, column3)
VALUES (value1, value2, value3);
```

### Multiple Records Insert
```sql
INSERT INTO table_name (column1, column2, column3)
VALUES 
  (value1a, value2a, value3a),
  (value1b, value2b, value3b),
  (value1c, value2c, value3c);
```

### Insert with RETURNING clause
```sql
INSERT INTO table_name (column1, column2)
VALUES (value1, value2)
RETURNING id, column1, created_at;
```

## 2. Creating Tables in MotherDuck

### Basic Table Creation
```sql
CREATE TABLE IF NOT EXISTS todos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR NOT NULL,
  description TEXT,
  completed BOOLEAN DEFAULT false,
  priority VARCHAR CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  due_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT current_timestamp,
  updated_at TIMESTAMP DEFAULT current_timestamp
);
```

### Table with Foreign Keys
```sql
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT current_timestamp
);

CREATE TABLE IF NOT EXISTS todos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  title VARCHAR NOT NULL,
  description TEXT,
  completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT current_timestamp,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 3. Data Insertion Examples

### Insert Users
```sql
-- Single user
INSERT INTO users (email, name)
VALUES ('<EMAIL>', 'John Doe');

-- Multiple users
INSERT INTO users (email, name)
VALUES 
  ('<EMAIL>', 'Jane Smith'),
  ('<EMAIL>', 'Bob Wilson'),
  ('<EMAIL>', 'Alice Brown');
```

### Insert Todos
```sql
-- Get user ID first
SELECT id FROM users WHERE email = '<EMAIL>';

-- Insert todos for that user (replace 'user-uuid-here' with actual UUID)
INSERT INTO todos (user_id, title, description, priority, due_date)
VALUES 
  ('user-uuid-here', 'Learn MotherDuck', 'Understand cloud database features', 'high', '2025-08-20 10:00:00'),
  ('user-uuid-here', 'Build Todo App', 'Complete the modern todo application', 'high', '2025-08-25 17:00:00'),
  ('user-uuid-here', 'Write Tests', 'Create comprehensive test suite', 'medium', '2025-08-22 12:00:00'),
  ('user-uuid-here', 'Deploy App', 'Deploy to production environment', 'low', '2025-09-01 09:00:00');
```

### Insert with Current Timestamp
```sql
INSERT INTO todos (user_id, title, description, created_at)
VALUES ('user-uuid-here', 'Timestamp Test', 'Testing current timestamp', current_timestamp);
```

## 4. Handling Special Characters

### Escaping Single Quotes
```sql
-- Wrong way (will cause SQL error)
INSERT INTO todos (title, description)
VALUES ('Don't forget', 'This won't work');

-- Correct way (escape single quotes by doubling them)
INSERT INTO todos (title, description)
VALUES ('Don''t forget', 'This won''t work becomes this will work');
```

### Safe String Handling in Application Code
```typescript
// In your TypeScript/JavaScript code
function insertTodoSafely(title: string, description: string) {
  const safeTitle = title.replace(/'/g, "''");
  const safeDescription = description.replace(/'/g, "''");
  
  return dbConnection.executeQuery(`
    INSERT INTO todos (title, description)
    VALUES ('${safeTitle}', '${safeDescription}')
    RETURNING id
  `);
}
```

## 5. Advanced Data Types

### JSON Data
```sql
-- Add JSON column to existing table
ALTER TABLE todos ADD COLUMN metadata JSON;

-- Insert with JSON data
INSERT INTO todos (user_id, title, metadata)
VALUES (
  'user-uuid-here',
  'Complex Todo',
  '{
    "tags": ["important", "urgent"],
    "estimated_hours": 2.5,
    "custom_fields": {
      "client": "Acme Corp",
      "project": "Website Redesign"
    }
  }'
);
```

### Array Data
```sql
-- Create table with array column
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  tags VARCHAR[],
  team_members UUID[]
);

-- Insert with arrays
INSERT INTO projects (name, tags, team_members)
VALUES (
  'Website Redesign',
  ['web', 'design', 'frontend'],
  ['user-uuid-1', 'user-uuid-2', 'user-uuid-3']
);
```

## 6. Bulk Data Operations

### Bulk Insert from Application
```typescript
// Example: Bulk insert multiple todos
async function bulkInsertTodos(userId: string, todosData: Array<{title: string, description: string, priority: string}>) {
  const values = todosData.map(todo => 
    `('${userId}', '${todo.title.replace(/'/g, "''")}', '${todo.description.replace(/'/g, "''")}', '${todo.priority}')`
  ).join(',\n    ');

  return await dbConnection.executeQuery(`
    INSERT INTO todos (user_id, title, description, priority)
    VALUES 
      ${values}
  `);
}

// Usage
const newTodos = [
  { title: 'Task 1', description: 'First task', priority: 'high' },
  { title: 'Task 2', description: 'Second task', priority: 'medium' },
  { title: 'Task 3', description: 'Third task', priority: 'low' }
];

await bulkInsertTodos('user-uuid-here', newTodos);
```

### Import from CSV
```sql
-- Create table for CSV import
CREATE TABLE imported_tasks (
  id INTEGER,
  title VARCHAR,
  description TEXT,
  priority VARCHAR,
  completed BOOLEAN
);

-- Import from CSV file (if you have CSV data)
COPY imported_tasks FROM 'path/to/tasks.csv' (HEADER);

-- Or insert from VALUES for testing
INSERT INTO imported_tasks (id, title, description, priority, completed)
VALUES 
  (1, 'Imported Task 1', 'Description 1', 'high', false),
  (2, 'Imported Task 2', 'Description 2', 'medium', true),
  (3, 'Imported Task 3', 'Description 3', 'low', false);
```

## 7. Data Modification

### Update Records
```sql
-- Update single record
UPDATE todos 
SET completed = true, updated_at = current_timestamp
WHERE id = 'todo-uuid-here';

-- Update multiple records
UPDATE todos 
SET priority = 'high'
WHERE due_date < current_timestamp + INTERVAL '1 day'
  AND completed = false;

-- Update with JOIN
UPDATE todos 
SET priority = 'urgent'
FROM users 
WHERE todos.user_id = users.id 
  AND users.email = '<EMAIL>'
  AND todos.completed = false;
```

### Delete Records
```sql
-- Delete single record
DELETE FROM todos WHERE id = 'todo-uuid-here';

-- Delete with conditions
DELETE FROM todos 
WHERE completed = true 
  AND created_at < current_timestamp - INTERVAL '30 days';

-- Delete with JOIN
DELETE FROM todos 
USING users 
WHERE todos.user_id = users.id 
  AND users.email = '<EMAIL>';
```

## 8. Querying Data

### Basic Queries
```sql
-- Select all todos
SELECT * FROM todos ORDER BY created_at DESC;

-- Select with conditions
SELECT title, description, priority 
FROM todos 
WHERE completed = false 
  AND priority IN ('high', 'medium')
ORDER BY priority DESC, created_at ASC;

-- Count records
SELECT 
  priority,
  COUNT(*) as total_count,
  COUNT(CASE WHEN completed THEN 1 END) as completed_count
FROM todos 
GROUP BY priority
ORDER BY priority;
```

### Advanced Queries with JOINs
```sql
-- Todos with user information
SELECT 
  t.id,
  t.title,
  t.priority,
  t.completed,
  u.name as user_name,
  u.email as user_email
FROM todos t
JOIN users u ON t.user_id = u.id
WHERE t.completed = false
ORDER BY t.created_at DESC;

-- Aggregate data by user
SELECT 
  u.name,
  u.email,
  COUNT(t.id) as total_todos,
  COUNT(CASE WHEN t.completed THEN 1 END) as completed_todos,
  COUNT(CASE WHEN t.priority = 'high' THEN 1 END) as high_priority_todos
FROM users u
LEFT JOIN todos t ON u.id = t.user_id
GROUP BY u.id, u.name, u.email
ORDER BY total_todos DESC;
```

## 9. Testing Your Data in MotherDuck Console

You can test these SQL commands directly in the MotherDuck web console:

1. **Go to MotherDuck Console**: https://app.motherduck.com
2. **Select Your Database**: Choose `todo_app_dev` from the database list
3. **Run SQL Commands**: Use the SQL editor to run any of the above commands
4. **View Results**: See the data in the results panel

### Quick Test Commands
```sql
-- 1. Create a simple test table
CREATE TABLE test_table (
  id INTEGER PRIMARY KEY,
  name VARCHAR,
  created_at TIMESTAMP DEFAULT current_timestamp
);

-- 2. Insert test data
INSERT INTO test_table (id, name)
VALUES (1, 'Test Record 1'), (2, 'Test Record 2');

-- 3. Query the data
SELECT * FROM test_table;

-- 4. Update data
UPDATE test_table SET name = 'Updated Record' WHERE id = 1;

-- 5. Verify update
SELECT * FROM test_table WHERE id = 1;
```

## 10. Key Points Summary

✅ **Identical Syntax**: MotherDuck uses exactly the same SQL syntax as local DuckDB  
✅ **No Special Commands**: All standard SQL operations work identically  
✅ **Automatic Sync**: Data is automatically synced to the cloud  
✅ **Full Feature Support**: Foreign keys, constraints, JSON, arrays all supported  
✅ **Performance**: Cloud operations may have slightly higher latency than local  
✅ **Reliability**: Built-in redundancy and backup in the cloud  

## 11. Common Patterns for Todo Application

### Complete User and Todo Setup
```sql
-- 1. Create user
INSERT INTO users (email, name)
VALUES ('<EMAIL>', 'Test User')
RETURNING id;

-- 2. Use the returned ID to create todos
INSERT INTO todos (user_id, title, description, priority, due_date)
VALUES 
  ('returned-user-id', 'Morning Standup', 'Daily team meeting', 'high', '2025-08-18 09:00:00'),
  ('returned-user-id', 'Code Review', 'Review pull requests', 'medium', '2025-08-18 14:00:00'),
  ('returned-user-id', 'Documentation', 'Update API docs', 'low', '2025-08-19 16:00:00');

-- 3. Query user's todos
SELECT 
  t.title,
  t.description,
  t.priority,
  t.due_date,
  t.completed
FROM todos t
JOIN users u ON t.user_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY t.due_date ASC;
```

This guide covers all the essential patterns for inserting and managing data in MotherDuck. The key takeaway is that MotherDuck is 100% compatible with DuckDB SQL syntax!
