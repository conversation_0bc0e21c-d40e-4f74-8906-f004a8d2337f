# MotherDuck Integration Guide

## Overview

The todo application now supports real MotherDuck cloud database integration, allowing you to seamlessly switch between local DuckDB and cloud-hosted MotherDuck databases.

## Features

### ✅ Implemented Features

1. **Automatic Connection Detection**: The application automatically detects whether to use local or cloud database based on the presence of a MotherDuck token
2. **Seamless Switching**: No code changes required - just add/remove the token to switch between local and cloud
3. **Cloud-Specific Optimizations**: When connected to MotherDuck, the application applies cloud-specific settings for optimal performance
4. **Connection Health Monitoring**: Built-in health checks and connection status monitoring
5. **Comprehensive Testing**: Full test suite to verify both local and cloud connections

### 🔧 Configuration

#### Local Database (Default)
- Uses local DuckDB file: `./data/dev.db`
- No additional configuration required
- Fully offline capable

#### MotherDuck Cloud Database
- Requires MotherDuck token
- Automatic cloud optimizations
- Real-time sync capabilities

## Setup Instructions

### 1. Get MotherDuck Token

1. Visit [MotherDuck Console](https://app.motherduck.com)
2. Sign up or log in to your account
3. Navigate to Settings → API Tokens
4. Generate a new token
5. Copy the token for configuration

### 2. Configure Environment

Add your MotherDuck token to the `.env` file:

```bash
# Enable MotherDuck cloud database
MOTHERDUCK_TOKEN=your_motherduck_token_here

# Optional: Specify database name (defaults to 'todo_app')
DATABASE_NAME=my_todo_database
```

### 3. Test Connection

Run the MotherDuck connection test:

```bash
npm run db:test-motherduck
```

## Usage Examples

### Check Connection Type

```typescript
import { dbConnection } from '@main/database/connection';

// Check if connected to MotherDuck
const isCloud = dbConnection.isMotherDuckConnection();
console.log(`Connection type: ${dbConnection.getConnectionType()}`);
```

### Get MotherDuck Information

```typescript
if (dbConnection.isMotherDuckConnection()) {
  const info = await dbConnection.getMotherDuckInfo();
  console.log('MotherDuck Info:', info);
  // Output: { database: 'todo_app', schema: 'main', user: 'your_user', connectionType: 'motherduck' }
}
```

### Sync to Cloud

```typescript
if (dbConnection.isMotherDuckConnection()) {
  await dbConnection.syncToMotherDuck();
  console.log('Data synced to MotherDuck successfully');
}
```

## Technical Implementation

### Connection Logic

The database connection service automatically determines the connection type:

```typescript
// In src/main/database/connection.ts
private async connect(): Promise<void> {
  const dbConfig = config.getDatabaseConfig();
  
  if (dbConfig.motherduckToken) {
    console.log('Connecting to MotherDuck cloud database...');
    await this.connectToMotherDuck(dbConfig);
  } else {
    console.log('Connecting to local DuckDB database...');
    await this.connectToLocalDatabase(dbConfig);
  }
}
```

### MotherDuck Connection String

The application uses the standard MotherDuck connection format:

```typescript
// Format: md:database_name
const connectionString = `md:${databaseName}`;
```

### Cloud Optimizations

When connected to MotherDuck, the application applies cloud-specific settings:

```typescript
const motherDuckSettings = [
  'SET enable_http_metadata_cache = true',
  'SET http_timeout = 30000',
];
```

## Testing

### Local Database Test

```bash
# Test local database (no token required)
npm run db:test-motherduck
```

Expected output:
```
✅ Local database connection is working. Add MOTHERDUCK_TOKEN to test cloud features.
🔗 Connection Type: local
```

### MotherDuck Cloud Test

```bash
# Set your token first
export MOTHERDUCK_TOKEN=your_token_here

# Test cloud database
npm run db:test-motherduck
```

Expected output:
```
🎉 MotherDuck connection is working perfectly!
🔗 Connection Type: motherduck
```

## Migration Guide

### From Local to Cloud

1. **Backup Local Data** (if needed):
   ```bash
   cp ./data/dev.db ./data/backup.db
   ```

2. **Add MotherDuck Token**:
   ```bash
   echo "MOTHERDUCK_TOKEN=your_token_here" >> .env
   ```

3. **Restart Application**: The app will automatically connect to MotherDuck

4. **Verify Connection**:
   ```bash
   npm run db:test-motherduck
   ```

### From Cloud to Local

1. **Remove MotherDuck Token**:
   ```bash
   # Comment out or remove the token from .env
   # MOTHERDUCK_TOKEN=your_token_here
   ```

2. **Restart Application**: The app will automatically use local database

## Troubleshooting

### Common Issues

1. **Invalid Token Error**
   - Verify token is correct and not expired
   - Check MotherDuck console for token status

2. **Connection Timeout**
   - Check internet connectivity
   - Verify MotherDuck service status

3. **Database Not Found**
   - Database will be created automatically if it doesn't exist
   - Verify `DATABASE_NAME` environment variable

### Debug Commands

```bash
# Test database connection
npm run db:test-motherduck

# Test general database operations
npm run db:test

# Initialize database schema
npm run db:init
```

## Security Considerations

1. **Token Security**: Never commit MotherDuck tokens to version control
2. **Environment Variables**: Use `.env` files for local development
3. **Production**: Use secure environment variable management in production
4. **Access Control**: Configure appropriate database permissions in MotherDuck console

## Performance Considerations

1. **Network Latency**: Cloud operations may have higher latency than local
2. **Caching**: The application enables HTTP metadata caching for better performance
3. **Connection Pooling**: Built-in connection management optimizes resource usage
4. **Batch Operations**: Consider batching operations for better cloud performance

## Next Steps

1. **Data Migration Tools**: Implement tools to migrate data between local and cloud
2. **Sync Strategies**: Develop offline-first sync capabilities
3. **Monitoring**: Add detailed performance and usage metrics
4. **Backup Automation**: Implement automated backup strategies
5. **Multi-Database Support**: Support for multiple MotherDuck databases
