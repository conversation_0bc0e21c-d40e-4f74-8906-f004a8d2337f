# Priority Visual Indicators

A comprehensive priority indicator system for the Modern Todo Application that provides consistent, accessible, and visually appealing priority representations throughout the user interface.

## Overview

The priority visual indicators system consists of:

1. **Centralized Priority Utilities** (`priorityUtils.ts`)
2. **Reusable PriorityIndicator Component** (`PriorityIndicator.tsx`)
3. **Integration across Todo components**

## Priority Levels

The system supports five priority levels:

- **Very High** - Critical/urgent tasks (red, AlertTriangle icon)
- **High** - Important tasks (orange, ArrowUp icon)
- **Medium** - Normal priority (yellow, Flag icon)
- **Low** - Less important (blue, ArrowDown icon)
- **Very Low** - Minimal priority (gray, Minus icon)

## Component Variants

### 1. Icon Variant
Simple icon representation with optional label.
```tsx
<PriorityIndicator priority="high" variant="icon" showLabel={true} />
```

### 2. Badge Variant
Rounded badge with background color and border.
```tsx
<PriorityIndicator priority="medium" variant="badge" size="sm" />
```

### 3. Bar Variant
Horizontal bar indicator with gradient colors.
```tsx
<PriorityIndicator priority="very_high" variant="bar" />
```

### 4. Chip Variant
Pill-shaped indicator with icon and label.
```tsx
<PriorityIndicator priority="low" variant="chip" showLabel={true} />
```

### 5. Full Variant
Comprehensive display with gradient background and glow effects.
```tsx
<PriorityIndicator priority="very_high" variant="full" showLabel={true} />
```

### 6. Minimal Variant
Subtle dot indicator with optional label.
```tsx
<PriorityIndicator priority="medium" variant="minimal" showLabel={true} />
```

### 7. Glow Variant
Special effects for very high priority items with pulsing animation.
```tsx
<PriorityIndicator priority="very_high" variant="glow" showLabel={true} />
```

## Size Options

- **Small (sm)** - Compact for dense layouts
- **Medium (md)** - Standard size (default)
- **Large (lg)** - Prominent display

## Color Scheme

### Very High Priority
- Color: Red (#DC2626)
- Background: Light red (#FEF2F2)
- Border: Red border (#FECACA)
- Gradient: Red to dark red
- Special: Glow effect and pulse animation

### High Priority
- Color: Orange (#EA580C)
- Background: Light orange (#FFF7ED)
- Border: Orange border (#FED7AA)
- Gradient: Orange to dark orange
- Special: Subtle glow effect

### Medium Priority
- Color: Yellow (#CA8A04)
- Background: Light yellow (#FEFCE8)
- Border: Yellow border (#FEF3C7)
- Gradient: Yellow to dark yellow

### Low Priority
- Color: Blue (#2563EB)
- Background: Light blue (#EFF6FF)
- Border: Blue border (#DBEAFE)
- Gradient: Blue to dark blue

### Very Low Priority
- Color: Gray (#6B7280)
- Background: Light gray (#F9FAFB)
- Border: Gray border (#E5E7EB)
- Gradient: Gray to dark gray

## Usage Examples

### In Todo Items
```tsx
// Priority badge in todo metadata
<PriorityIndicator 
  priority={todo.priority} 
  variant="badge" 
  size="sm" 
  showLabel={false}
  animated={true}
/>

// Priority bar at top of high-priority todos
{isHighPriority(todo.priority) && (
  <PriorityIndicator 
    priority={todo.priority} 
    variant="bar" 
    animated={true}
    className="rounded-t-2xl"
  />
)}
```

### In Forms
```tsx
// Priority selection preview
<PriorityIndicator 
  priority={selectedPriority} 
  variant="chip" 
  size="sm" 
  showLabel={true}
  animated={true}
/>
```

### In Filters
```tsx
// Filter option with visual indicator
<PriorityIndicator 
  priority={priority} 
  variant="icon" 
  size="sm" 
  showLabel={true}
  animated={false}
/>
```

## Utility Functions

### Priority Configuration
```tsx
import { getPriorityConfig, getPriorityColor, getPriorityIcon } from '@renderer/utils/priorityUtils';

const config = getPriorityConfig('high');
const color = getPriorityColor('very_high');
const IconComponent = getPriorityIcon('medium');
```

### Priority Checks
```tsx
import { isHighPriority, isVeryHighPriority } from '@renderer/utils/priorityUtils';

if (isVeryHighPriority(todo.priority)) {
  // Apply special styling for very high priority
}

if (isHighPriority(todo.priority)) {
  // Apply enhanced visibility for high priority
}
```

### Priority Sorting
```tsx
import { getPriorityWeight, sortPrioritiesByWeight } from '@renderer/utils/priorityUtils';

const weight = getPriorityWeight('high'); // Returns 4
const sortedPriorities = sortPrioritiesByWeight(['low', 'very_high', 'medium']);
```

## Accessibility

- All indicators use semantic colors with sufficient contrast
- Icons provide visual meaning beyond color alone
- Labels are available for screen readers
- Consistent sizing and spacing for touch targets

## Animation

- Smooth entrance animations with framer-motion
- Pulse animations for very high priority items
- Hover effects for interactive elements
- Configurable animation enable/disable

## Integration Points

1. **TodoItem.tsx** - Priority badges and bars
2. **TodoForm.tsx** - Priority selection with preview
3. **TodoFilterPanel.tsx** - Visual filter options
4. **TodoList.tsx** - Sorting and display
5. **Dashboard** - Priority statistics and overviews

## Performance

- Lightweight component with minimal re-renders
- Memoized utility functions
- Efficient CSS classes and animations
- Tree-shakable utility exports

## Testing

Comprehensive test suite covering:
- All priority levels
- All variants and sizes
- Animation states
- Accessibility features
- Integration scenarios
