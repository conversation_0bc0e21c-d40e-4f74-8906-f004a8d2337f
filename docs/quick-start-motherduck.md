# Quick Start: MotherDuck Auto-Initialization

## 🚀 Getting Started

Your MotherDuck database is now configured with **automatic initialization**! Here's what happens when you connect:

### 1. **First Connection** (New Database)
```bash
npm run db:test-auto-init
```

**What happens automatically:**
- ✅ Connects to MotherDuck cloud database
- ✅ Detects empty database needs initialization  
- ✅ Creates all application tables (users, todos, categories, etc.)
- ✅ Creates performance indexes
- ✅ Inserts demo data (1 user, 6 categories, 3 sample todos)
- ✅ Records initialization in tracking table
- ⏱️ **Total time: ~2.4 seconds**

### 2. **Subsequent Connections** (Existing Database)
```bash
npm run db:test-auto-init
```

**What happens automatically:**
- ✅ Connects to MotherDuck cloud database
- ✅ Detects database already initialized
- ✅ Skips initialization (no duplicate work)
- ✅ Ready to use immediately
- ⏱️ **Total time: ~0.5 seconds**

## 📋 What Gets Created

### Database Tables
1. **users** - User accounts and profiles
2. **categories** - Todo categories (Work, Personal, Learning, etc.)
3. **todos** - Main todo items with metadata
4. **todo_categories** - Category assignments
5. **user_sessions** - Authentication sessions
6. **_motherduck_initialization** - System tracking table

### Demo Data
- **Demo User**: <EMAIL>
- **6 Categories**: Work, Personal, Learning, Health, Shopping, Travel
- **3 Sample Todos**: Welcome message, feature exploration, setup completion

### Performance Indexes
- 9 indexes on frequently queried columns for optimal performance

## 🔧 Configuration

### Environment Variables
```bash
# Required: MotherDuck token
MOTHERDUCK_TOKEN=your_token_here

# Optional: Database name (default: todo_app_dev)
DATABASE_NAME=my_custom_database

# Optional: Environment (affects seed data)
NODE_ENV=development  # Includes seed data
NODE_ENV=production   # Schema only, no seed data
```

## 🧪 Testing Commands

```bash
# Test automatic initialization
npm run db:test-auto-init

# Test MotherDuck connection
npm run db:test-motherduck

# Run simple data examples
npm run db:simple-examples
```

## 📊 Check Your Database

### In MotherDuck Console
1. Go to https://app.motherduck.com
2. Select your database: `todo_app_dev`
3. Run queries:

```sql
-- Check tables
SHOW TABLES;

-- Check demo data
SELECT * FROM users;
SELECT * FROM categories;
SELECT * FROM todos;

-- Check initialization history
SELECT * FROM _motherduck_initialization;
```

### In Application Code
```typescript
import { dbConnection } from '@main/database/connection';

// Initialize connection (auto-initialization happens here)
await dbConnection.initialize();

// Check what happened
const initResult = dbConnection.getInitializationResult();
console.log('Initialization:', initResult?.message);

// Get database info
const info = await dbConnection.getMotherDuckInfo();
console.log('Database:', info.database);
console.log('Tables created:', info.initializationResult?.tablesCreated);
```

## 🎯 Key Benefits

### ✅ **Zero Manual Setup**
- No need to manually create tables
- No need to run SQL scripts
- No need to insert demo data

### ✅ **One-Time Only**
- Initialization only runs once per database
- Safe reconnections don't trigger re-initialization
- Existing data is preserved

### ✅ **Development Ready**
- Immediate access to working database
- Demo user and sample data for testing
- All relationships and constraints in place

### ✅ **Production Safe**
- Robust error handling
- No seed data in production
- Comprehensive logging and tracking

## 🔍 Troubleshooting

### Common Issues

**Issue**: "Database not connected"
```bash
# Solution: Check your MotherDuck token
echo $MOTHERDUCK_TOKEN
# Should show your token, if empty, add to .env file
```

**Issue**: "Initialization failed"
```bash
# Solution: Check initialization history
npm run db:test-auto-init
# Look for error details in the output
```

**Issue**: "Tables already exist"
```bash
# This is normal! The system detects existing tables and skips creation
# Your database is ready to use
```

### Debug Commands

```bash
# Check connection type
node -e "
const { dbConnection } = require('./dist/main/database/connection.js');
dbConnection.initialize().then(() => {
  console.log('Type:', dbConnection.getConnectionType());
  console.log('Init:', dbConnection.getInitializationResult());
});
"

# Check MotherDuck info
node -e "
const { dbConnection } = require('./dist/main/database/connection.js');
dbConnection.initialize().then(async () => {
  const info = await dbConnection.getMotherDuckInfo();
  console.log(JSON.stringify(info, null, 2));
});
"
```

## 🎉 You're Ready!

Your MotherDuck database is now:
- ✅ **Automatically configured** with complete schema
- ✅ **Populated with demo data** for immediate testing  
- ✅ **Performance optimized** with proper indexes
- ✅ **Production ready** with robust error handling

Start building your todo application - the database is ready to go! 🚀
