suite('serialization', function () {
  setup(function () {
    var documents = [{
      id: 'a',
      title: 'Mr. <PERSON> kills Colonel <PERSON><PERSON>',
      body: 'Mr. <PERSON> killed Colonel <PERSON><PERSON> in the study with the candlestick. Mr<PERSON> is not a very nice fellow.',
      wordCount: 19
    },{
      id: 'b',
      title: 'Plumb waters plant',
      body: 'Professor <PERSON><PERSON><PERSON> has a green plant in his study',
      wordCount: 9
    },{
      id: 'c',
      title: '<PERSON> helps Professor',
      body: 'Miss <PERSON> watered Professor <PERSON><PERSON><PERSON> green plant while he was away from his office last week.',
      wordCount: 16
    },{
      id: 'd',
      title: 'All about JavaScript',
      body: 'JavaScript objects have a special __proto__ property',
      wordCount: 7
    }]

    this.idx = lunr(function () {
      this.ref('id')
      this.field('title')
      this.field('body')

      documents.forEach(function (document) {
        this.add(document)
      }, this)
    })

    this.serializedIdx = JSON.stringify(this.idx)
    this.loadedIdx = lunr.Index.load(JSON.parse(this.serializedIdx))
  })

  test('search', function () {
    var idxResults = this.idx.search('green'),
        serializedResults = this.loadedIdx.search('green')

    assert.deepEqual(idxResults, serializedResults)
  })

  test('__proto__ double serialization', function () {
    var doubleLoadedIdx = lunr.Index.load(JSON.parse(JSON.stringify(this.loadedIdx))),
        idxResults = this.idx.search('__proto__'),
        doubleSerializedResults = doubleLoadedIdx.search('__proto__')

    assert.deepEqual(idxResults, doubleSerializedResults)
  })
})
