import { BaseDAO } from './base.dao';
import { offlineFirstDataService } from '@main/services/offline-first-data.service';
import { DatabaseError, QueryResult, PaginationOptions, FilterOptions } from '@shared/types';

export interface OfflineFirstOptions {
  useCache?: boolean;
  forceRefresh?: boolean;
  enableOptimisticUpdates?: boolean;
  priority?: 'high' | 'medium' | 'low';
}

/**
 * Enhanced Base DAO with Offline-First capabilities
 * 
 * This extends the base DAO to provide:
 * - Offline-first data access
 * - Optimistic updates
 * - Local caching
 * - Background synchronization
 * - Graceful degradation when offline
 */
export abstract class OfflineFirstBaseDAO<T> extends BaseDAO<T> {
  constructor(tableName: string) {
    super(tableName);
  }

  /**
   * Enhanced find method with offline-first strategy
   */
  public async findOfflineFirst(
    query: string,
    params: any[] = [],
    options: OfflineFirstOptions = {}
  ): Promise<QueryResult<T>> {
    try {
      return await offlineFirstDataService.getData<T>(
        this.tableName,
        query,
        params,
        {
          useCache: options.useCache ?? true,
          forceRefresh: options.forceRefresh ?? false
        }
      );
    } catch (error) {
      console.error(`Offline-first query failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Enhanced create method with offline-first strategy
   */
  public async createOfflineFirst(
    data: Partial<T>,
    userId: string,
    options: OfflineFirstOptions = {}
  ): Promise<T> {
    const fields = Object.keys(data).filter(key => (data as any)[key] !== undefined);
    const values = fields.map(key => (data as any)[key]);
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');

    const query = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

    // Generate optimistic data for immediate UI feedback
    const optimisticData = options.enableOptimisticUpdates !== false ? {
      id: crypto.randomUUID(), // Temporary ID
      ...data,
      created_at: new Date(),
      updated_at: new Date()
    } as any : undefined;

    try {
      const result = await offlineFirstDataService.mutateData<T>(
        'CREATE',
        this.tableName,
        query,
        values,
        (optimisticData as any)?.id as string || crypto.randomUUID(),
        userId,
        optimisticData
      );

      if (result.rows.length === 0) {
        throw new DatabaseError(`Failed to create record in ${this.tableName}`, 'CREATE_ERROR');
      }

      return result.rows[0];
    } catch (error) {
      console.error(`Offline-first create failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Enhanced update method with offline-first strategy
   */
  public async updateOfflineFirst(
    id: string,
    updates: Partial<T>,
    userId: string,
    options: OfflineFirstOptions = {}
  ): Promise<T> {
    const fields = Object.keys(updates).filter(key => (updates as any)[key] !== undefined);
    const values = fields.map(key => (updates as any)[key]);
    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');

    const query = `
      UPDATE ${this.tableName} 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${fields.length + 1}
      RETURNING *
    `;

    const queryParams = [...values, id];

    // Generate optimistic data
    const optimisticData = options.enableOptimisticUpdates !== false ? {
      id,
      ...updates,
      updated_at: new Date()
    } as Partial<T> : undefined;

    try {
      const result = await offlineFirstDataService.mutateData<T>(
        'UPDATE',
        this.tableName,
        query,
        queryParams,
        id,
        userId,
        optimisticData as T
      );

      if (result.rows.length === 0) {
        throw new DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
      }

      return result.rows[0];
    } catch (error) {
      console.error(`Offline-first update failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Enhanced delete method with offline-first strategy
   */
  public async deleteOfflineFirst(
    id: string,
    userId: string,
    options: OfflineFirstOptions = {}
  ): Promise<boolean> {
    const query = `DELETE FROM ${this.tableName} WHERE id = $1`;

    try {
      const result = await offlineFirstDataService.mutateData<T>(
        'DELETE',
        this.tableName,
        query,
        [id],
        id,
        userId
      );

      return result.rowCount > 0;
    } catch (error) {
      console.error(`Offline-first delete failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Enhanced soft delete method with offline-first strategy
   */
  public async softDeleteOfflineFirst(
    id: string,
    userId: string,
    options: OfflineFirstOptions = {}
  ): Promise<T> {
    const query = `
      UPDATE ${this.tableName} 
      SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $1 
      RETURNING *
    `;

    // Generate optimistic data
    const optimisticData = options.enableOptimisticUpdates !== false ? {
      id,
      is_deleted: true,
      updated_at: new Date()
    } as any : undefined;

    try {
      const result = await offlineFirstDataService.mutateData<T>(
        'UPDATE',
        this.tableName,
        query,
        [id],
        id,
        userId,
        optimisticData as T
      );

      if (result.rows.length === 0) {
        throw new DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
      }

      return result.rows[0];
    } catch (error) {
      console.error(`Offline-first soft delete failed for ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Find by ID with offline-first strategy
   */
  public async findByIdOfflineFirst(
    id: string,
    options: OfflineFirstOptions = {}
  ): Promise<T | null> {
    const query = `SELECT * FROM ${this.tableName} WHERE id = $1`;
    
    try {
      const result = await this.findOfflineFirst(query, [id], options);
      return result.rows[0] || null;
    } catch (error) {
      console.error(`Offline-first findById failed for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Find all with offline-first strategy and pagination
   */
  public async findAllOfflineFirst(
    pagination?: PaginationOptions,
    filters?: FilterOptions,
    options: OfflineFirstOptions = {}
  ): Promise<{ data: T[]; total: number; hasMore: boolean }> {
    let query = `SELECT * FROM ${this.tableName} WHERE 1=1`;
    const params: any[] = [];
    let paramIndex = 1;

    // Apply filters
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query += ` AND ${key} = $${paramIndex}`;
          params.push(value);
          paramIndex++;
        }
      });
    }

    // Add ordering
    query += ` ORDER BY created_at DESC`;

    // Add pagination
    if (pagination) {
      const { page = 1, limit = 20 } = pagination;
      const offset = (page - 1) * limit;
      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);
    }

    try {
      const result = await this.findOfflineFirst(query, params, options);
      
      // Get total count for pagination
      let countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} WHERE 1=1`;
      const countParams: any[] = [];
      let countParamIndex = 1;

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            countQuery += ` AND ${key} = $${countParamIndex}`;
            countParams.push(value);
            countParamIndex++;
          }
        });
      }

      const countResult = await this.findOfflineFirst(countQuery, countParams, options);
      const total = (countResult.rows[0] as any)?.total || 0;

      const hasMore = pagination ? 
        (pagination.page * pagination.limit) < total : 
        false;

      return {
        data: result.rows,
        total,
        hasMore
      };
    } catch (error) {
      console.error(`Offline-first findAll failed for ${this.tableName}:`, error);
      return { data: [], total: 0, hasMore: false };
    }
  }

  /**
   * Check if record exists with offline-first strategy
   */
  public async existsOfflineFirst(
    id: string,
    options: OfflineFirstOptions = {}
  ): Promise<boolean> {
    const query = `SELECT EXISTS(SELECT 1 FROM ${this.tableName} WHERE id = $1) as exists`;
    
    try {
      const result = await this.findOfflineFirst(query, [id], options);
      return (result.rows[0] as any)?.exists || false;
    } catch (error) {
      console.error(`Offline-first exists check failed for ${this.tableName}:`, error);
      return false;
    }
  }

  /**
   * Get offline status for this DAO
   */
  public getOfflineStatus() {
    return {
      tableName: this.tableName,
      ...offlineFirstDataService.getOfflineStatus()
    };
  }

  /**
   * Force sync pending operations for this table
   */
  public async forceSyncTable(): Promise<void> {
    try {
      await offlineFirstDataService.forceSyncPendingOperations();
    } catch (error) {
      console.error(`Failed to force sync ${this.tableName}:`, error);
      throw error;
    }
  }

  /**
   * Clear cache for this table
   */
  public clearTableCache(): void {
    // This would ideally clear only cache entries for this table
    // For now, we clear all cache
    offlineFirstDataService.clearCache();
  }
}
