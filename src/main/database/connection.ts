import * as duckdb from 'duckdb';
import * as path from 'path';
import * as fs from 'fs/promises';
import { config } from '@main/utils/config';
import { DatabaseError, QueryResult, ConnectionStatus } from '@shared/types';
import { schemaInitializer, InitializationResult } from './schema-initializer';

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private db: duckdb.Database | null = null;
  private connection: duckdb.Connection | null = null;
  private isConnected = false;
  private connectionStatus: ConnectionStatus = {
    isConnected: false,
    connectionAttempts: 0,
  };
  private initializationResult: InitializationResult | null = null;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async initialize(): Promise<void> {
    try {
      await this.connect();
      console.log('Database connection initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database connection:', error);
      throw new DatabaseError('Database initialization failed', 'INIT_ERROR', error);
    }
  }

  private async connect(): Promise<void> {
    try {
      this.connectionStatus.connectionAttempts++;

      const dbConfig = config.getDatabaseConfig();

      // Check if MotherDuck token is provided for cloud connection
      if (dbConfig.motherduckToken) {
        console.log('Connecting to MotherDuck cloud database...');
        await this.connectToMotherDuck(dbConfig);
      } else {
        console.log('Connecting to local DuckDB database...');
        await this.connectToLocalDatabase(dbConfig);
      }

      // Set connection status early so executeQuery works
      this.isConnected = true;
      this.connectionStatus.isConnected = true;

      // Test connection first
      await this.testConnection();

      // Configure DuckDB settings
      await this.configureDatabase();

      // Initialize database schema if needed (MotherDuck only)
      if (this.isMotherDuckConnection()) {
        await this.initializeDatabaseSchema();
      }

      this.connectionStatus.lastConnectionAt = new Date();
      this.connectionStatus.lastError = undefined;

    } catch (error) {
      this.isConnected = false;
      this.connectionStatus.isConnected = false;
      this.connectionStatus.lastError = error instanceof Error ? error.message : 'Unknown error';
      throw new DatabaseError('Failed to connect to database', 'CONNECTION_ERROR', error);
    }
  }

  private async configureDatabase(): Promise<void> {
    if (!this.connection) {
      throw new DatabaseError('No database connection available', 'NO_CONNECTION');
    }

    const dbConfig = config.getDatabaseConfig();

    try {
      // Configure DuckDB settings
      const settings = [
        'SET memory_limit = \'1GB\'',
        'SET threads = 4',
        'SET enable_progress_bar = false',
      ];

      // Note: WAL mode is not supported by DuckDB

      for (const setting of settings) {
        try {
          await this.executeQuery(setting);
        } catch (error) {
          console.warn(`Failed to configure setting "${setting}":`, error);
        }
      }

      // Install and load required extensions
      const extensions = [
        'httpfs',  // For reading from HTTP(S) sources
        'json',    // For JSON operations
        'fts',     // For full-text search (if available)
      ];

      for (const ext of extensions) {
        try {
          await this.executeQuery(`INSTALL ${ext}`);
          await this.executeQuery(`LOAD ${ext}`);
        } catch (error) {
          console.warn(`Failed to load extension ${ext}:`, error);
          // Continue with other extensions
        }
      }

    } catch (error) {
      console.warn('Failed to configure some database settings:', error);
      // Don't throw here as basic functionality should still work
    }
  }

  private async connectToLocalDatabase(dbConfig: any): Promise<void> {
    // Ensure data directory exists
    const dataDir = path.dirname(dbConfig.path);
    await fs.mkdir(dataDir, { recursive: true });

    // Create local DuckDB database
    this.db = new duckdb.Database(dbConfig.path);

    // Get connection
    this.connection = this.db.connect();
  }

  private async connectToMotherDuck(dbConfig: any): Promise<void> {
    // Set the MotherDuck token as environment variable for DuckDB to use
    process.env.motherduck_token = dbConfig.motherduckToken;

    const databaseName = process.env.DATABASE_NAME || 'todo_app_dev';

    try {
      // First, install and load MotherDuck extension, then create database if needed
      console.log('Setting up MotherDuck connection...');

      // Create a temporary in-memory database to install MotherDuck extension
      const tempDb = new duckdb.Database(':memory:');
      const tempConn = tempDb.connect();

      // Install and load MotherDuck extension
      await new Promise<void>((resolve, reject) => {
        tempConn.all('INSTALL motherduck', (err) => {
          if (err) {
            reject(new Error(`Failed to install MotherDuck extension: ${err.message}`));
            return;
          }
          tempConn.all('LOAD motherduck', (err2) => {
            if (err2) {
              reject(new Error(`Failed to load MotherDuck extension: ${err2.message}`));
              return;
            }
            resolve();
          });
        });
      });

      // Connect to MotherDuck default database to create our database
      console.log('Connecting to MotherDuck to ensure database exists...');
      const mdDb = new duckdb.Database('md:');
      const mdConn = mdDb.connect();

      // Create database if it doesn't exist
      await new Promise<void>((resolve, reject) => {
        mdConn.all(`CREATE DATABASE IF NOT EXISTS ${databaseName}`, (err) => {
          if (err) {
            reject(new Error(`Failed to create database: ${err.message}`));
          } else {
            console.log(`Database '${databaseName}' is ready`);
            resolve();
          }
        });
      });

      // Close temporary connections
      tempDb.close();
      mdDb.close();

      // Now connect to the specific database
      const connectionString = `md:${databaseName}`;
      console.log(`Connecting to MotherDuck database: ${databaseName}`);

      // Create DuckDB database with MotherDuck connection string
      this.db = new duckdb.Database(connectionString);

      // Get connection
      this.connection = this.db.connect();

    } catch (error) {
      console.error('MotherDuck connection failed:', error);
      throw error;
    }
  }

  /**
   * Initialize database schema for MotherDuck
   */
  private async initializeDatabaseSchema(): Promise<void> {
    try {
      console.log('🔍 Checking if MotherDuck database needs initialization...');

      const needsInit = await schemaInitializer.needsInitialization();

      if (needsInit) {
        console.log('🚀 Initializing MotherDuck database schema...');

        // Initialize with seed data for development/demo purposes
        // In production, you might want to set this to false
        const includeSeedData = process.env.NODE_ENV !== 'production';

        this.initializationResult = await schemaInitializer.initializeDatabase(includeSeedData);

        console.log(`✅ Database initialization completed: ${this.initializationResult.message}`);

        if (this.initializationResult.seedDataInserted) {
          console.log('🌱 Seed data inserted - demo user and sample todos available');
        }
      } else {
        console.log('✅ Database already initialized - skipping schema creation');
        this.initializationResult = {
          wasInitialized: false,
          tablesCreated: [],
          seedDataInserted: false,
          initializationTime: 0,
          message: 'Database was already initialized'
        };
      }
    } catch (error) {
      console.error('❌ Failed to initialize database schema:', error);
      // Don't throw here - allow connection to proceed even if initialization fails
      // The application can still work with an existing schema
      this.initializationResult = {
        wasInitialized: false,
        tablesCreated: [],
        seedDataInserted: false,
        initializationTime: 0,
        message: `Initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async testConnection(): Promise<void> {
    if (!this.connection) {
      throw new DatabaseError('No database connection available', 'NO_CONNECTION');
    }

    try {
      await this.executeQuery('SELECT 1 as test');
    } catch (error) {
      throw new DatabaseError('Database connection test failed', 'CONNECTION_TEST_FAILED', error);
    }
  }

  public async executeQuery<T = any>(
    query: string, 
    params: any[] = []
  ): Promise<QueryResult<T>> {
    if (!this.connection || !this.isConnected) {
      throw new DatabaseError('Database not connected', 'NOT_CONNECTED');
    }

    return new Promise((resolve, reject) => {
      try {
        const callback = (err: Error | null, rows: any[]) => {
          if (err) {
            reject(new DatabaseError(`Query execution failed: ${err.message}`, 'QUERY_ERROR', err));
            return;
          }

          resolve({
            rows: rows || [],
            rowCount: rows ? rows.length : 0,
            command: query.trim().split(' ')[0].toUpperCase(),
          });
        };

        // Only pass params if they exist
        if (params && params.length > 0) {
          // Spread the parameters array for DuckDB
          this.connection!.all(query, ...params, callback);
        } else {
          this.connection!.all(query, callback);
        }
      } catch (error) {
        reject(new DatabaseError('Query execution failed', 'QUERY_ERROR', error));
      }
    });
  }

  public async executeTransaction<T>(
    callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<T>
  ): Promise<T> {
    if (!this.connection || !this.isConnected) {
      throw new DatabaseError('Database not connected', 'NOT_CONNECTED');
    }

    try {
      await this.executeQuery('BEGIN TRANSACTION');
      
      const executeInTransaction = async (query: string, params: any[] = []) => {
        return this.executeQuery(query, params);
      };

      const result = await callback(executeInTransaction);
      
      await this.executeQuery('COMMIT');
      return result;
      
    } catch (error) {
      try {
        await this.executeQuery('ROLLBACK');
      } catch (rollbackError) {
        console.error('Failed to rollback transaction:', rollbackError);
      }
      throw error;
    }
  }

  public async batch(queries: Array<{ query: string; params?: any[] }>): Promise<QueryResult[]> {
    const results: QueryResult[] = [];
    
    await this.executeTransaction(async (execute) => {
      for (const { query, params = [] } of queries) {
        const result = await execute(query, params);
        results.push(result);
      }
      return results;
    });

    return results;
  }

  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  public isReady(): boolean {
    return this.isConnected && this.connection !== null;
  }

  public async reconnect(): Promise<void> {
    if (this.connection) {
      try {
        this.connection.close();
      } catch (error) {
        console.warn('Error closing existing connection:', error);
      }
    }

    if (this.db) {
      try {
        this.db.close();
      } catch (error) {
        console.warn('Error closing existing database:', error);
      }
    }

    this.connection = null;
    this.db = null;
    this.isConnected = false;

    await this.connect();
  }

  public async close(): Promise<void> {
    try {
      if (this.connection) {
        this.connection.close();
        this.connection = null;
      }

      if (this.db) {
        this.db.close();
        this.db = null;
      }

      this.isConnected = false;
      this.connectionStatus.isConnected = false;
      
      console.log('Database connection closed successfully');
    } catch (error) {
      console.error('Error closing database connection:', error);
      throw new DatabaseError('Failed to close database connection', 'CLOSE_ERROR', error);
    }
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }
      
      await this.executeQuery('SELECT 1');
      return true;
    } catch (error) {
      console.warn('Database health check failed:', error);
      return false;
    }
  }

  // Get database statistics
  public async getStats(): Promise<any> {
    try {
      const [
        memoryUsage,
        tableCount,
        indexCount,
      ] = await Promise.all([
        this.executeQuery("SELECT * FROM pragma_database_size()"),
        this.executeQuery("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'main'"),
        this.executeQuery("SELECT COUNT(*) as count FROM information_schema.statistics"),
      ]);

      return {
        memoryUsage: memoryUsage.rows[0],
        tableCount: tableCount.rows[0]?.count || 0,
        indexCount: indexCount.rows[0]?.count || 0,
        isConnected: this.isConnected,
        connectionAttempts: this.connectionStatus.connectionAttempts,
        lastConnectionAt: this.connectionStatus.lastConnectionAt,
      };
    } catch (error) {
      console.warn('Failed to get database stats:', error);
      return {
        isConnected: this.isConnected,
        connectionAttempts: this.connectionStatus.connectionAttempts,
        lastConnectionAt: this.connectionStatus.lastConnectionAt,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Check if connected to MotherDuck
  public isMotherDuckConnection(): boolean {
    const dbConfig = config.getDatabaseConfig();
    return !!dbConfig.motherduckToken;
  }

  // Get connection type
  public getConnectionType(): 'local' | 'motherduck' {
    return this.isMotherDuckConnection() ? 'motherduck' : 'local';
  }

  // Get initialization result
  public getInitializationResult(): InitializationResult | null {
    return this.initializationResult;
  }

  // MotherDuck-specific methods
  public async getMotherDuckInfo(): Promise<any> {
    if (!this.isMotherDuckConnection()) {
      throw new DatabaseError('Not connected to MotherDuck', 'NOT_MOTHERDUCK');
    }

    try {
      const [databaseInfo, userInfo] = await Promise.all([
        this.executeQuery('SELECT current_database() as database_name, current_schema() as schema_name'),
        this.executeQuery('SELECT current_user() as user_name')
      ]);

      return {
        database: databaseInfo.rows[0]?.database_name,
        schema: databaseInfo.rows[0]?.schema_name,
        user: userInfo.rows[0]?.user_name,
        connectionType: 'motherduck',
        initializationResult: this.initializationResult
      };
    } catch (error) {
      console.error('Failed to get MotherDuck info:', error);
      throw new DatabaseError('Failed to get MotherDuck info', 'MOTHERDUCK_INFO_ERROR', error);
    }
  }

  // Get initialization history
  public async getInitializationHistory(): Promise<any[]> {
    if (!this.isMotherDuckConnection()) {
      throw new DatabaseError('Not connected to MotherDuck', 'NOT_MOTHERDUCK');
    }

    try {
      return await schemaInitializer.getInitializationHistory();
    } catch (error) {
      console.error('Failed to get initialization history:', error);
      throw new DatabaseError('Failed to get initialization history', 'INIT_HISTORY_ERROR', error);
    }
  }

  // Force re-initialization (use with caution)
  public async forceReinitialize(includeSeedData: boolean = false): Promise<InitializationResult> {
    if (!this.isMotherDuckConnection()) {
      throw new DatabaseError('Not connected to MotherDuck', 'NOT_MOTHERDUCK');
    }

    try {
      console.log('🔄 Force re-initializing MotherDuck database...');
      this.initializationResult = await schemaInitializer.initializeDatabase(includeSeedData);
      return this.initializationResult;
    } catch (error) {
      console.error('Failed to force re-initialize database:', error);
      throw new DatabaseError('Failed to force re-initialize database', 'FORCE_INIT_ERROR', error);
    }
  }
}

// Export singleton instance
export const dbConnection = DatabaseConnection.getInstance();