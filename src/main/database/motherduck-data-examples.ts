import { dbConnection } from './connection';

/**
 * MotherDuck Data Insertion Examples
 * 
 * This file demonstrates how to create tables and insert data into MotherDuck.
 * The syntax is identical to local DuckDB - MotherDuck is fully compatible!
 */

export class MotherDuckDataExamples {
  
  /**
   * 1. CREATE TABLES
   * MotherDuck supports all standard DuckDB table creation syntax
   */
  async createTodoTables(): Promise<void> {
    console.log('Creating todo application tables in MotherDuck...');

    // Drop existing tables if they exist (in reverse order due to foreign keys)
    try {
      await dbConnection.executeQuery('DROP TABLE IF EXISTS todo_categories');
      await dbConnection.executeQuery('DROP TABLE IF EXISTS todos');
      await dbConnection.executeQuery('DROP TABLE IF EXISTS categories');
      await dbConnection.executeQuery('DROP TABLE IF EXISTS users');
      console.log('Existing tables dropped');
    } catch (error) {
      console.log('No existing tables to drop or error dropping:', error);
    }

    // Create users table
    await dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR UNIQUE NOT NULL,
        name VARCHAR NOT NULL,
        password_hash VARCHAR NOT NULL,
        created_at TIMESTAMP DEFAULT current_timestamp,
        updated_at TIMESTAMP DEFAULT current_timestamp
      )
    `);

    // Create todos table
    await dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS todos (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        title VARCHAR NOT NULL,
        description TEXT,
        completed BOOLEAN DEFAULT false,
        priority VARCHAR CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
        due_date TIMESTAMP,
        created_at TIMESTAMP DEFAULT current_timestamp,
        updated_at TIMESTAMP DEFAULT current_timestamp,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);

    // Create categories table
    await dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR UNIQUE NOT NULL,
        color VARCHAR DEFAULT '#3B82F6',
        created_at TIMESTAMP DEFAULT current_timestamp
      )
    `);

    // Create todo_categories junction table
    await dbConnection.executeQuery(`
      CREATE TABLE IF NOT EXISTS todo_categories (
        todo_id UUID NOT NULL,
        category_id UUID NOT NULL,
        PRIMARY KEY (todo_id, category_id),
        FOREIGN KEY (todo_id) REFERENCES todos(id),
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `);

    console.log('✅ All tables created successfully in MotherDuck');
  }

  /**
   * 2. INSERT SINGLE RECORDS
   * Standard INSERT syntax works exactly the same
   */
  async insertSampleUser(): Promise<string> {
    console.log('Inserting sample user...');

    const result = await dbConnection.executeQuery(`
      INSERT INTO users (email, name, password_hash)
      VALUES ('<EMAIL>', 'John Doe', 'hashed_password_123')
      RETURNING id
    `);

    const userId = result.rows[0].id;
    console.log(`✅ User created with ID: ${userId}`);
    return userId;
  }

  /**
   * 3. INSERT MULTIPLE RECORDS
   * Batch inserts for better performance
   */
  async insertSampleTodos(userId: string): Promise<void> {
    console.log('Inserting sample todos...');

    await dbConnection.executeQuery(`
      INSERT INTO todos (user_id, title, description, priority, due_date, completed)
      VALUES 
        ('${userId}', 'Learn MotherDuck', 'Understand how to use MotherDuck cloud database', 'high', '2025-08-20 10:00:00', false),
        ('${userId}', 'Build Todo App', 'Complete the modern todo application', 'high', '2025-08-25 17:00:00', false),
        ('${userId}', 'Write Documentation', 'Document the MotherDuck integration', 'medium', '2025-08-22 12:00:00', false),
        ('${userId}', 'Test Cloud Sync', 'Verify data synchronization works', 'medium', NULL, true),
        ('${userId}', 'Deploy Application', 'Deploy to production environment', 'low', '2025-09-01 09:00:00', false)
    `);

    console.log('✅ Sample todos inserted successfully');
  }

  /**
   * 4. INSERT WITH PARAMETERIZED QUERIES (Safer approach)
   * Note: DuckDB Node.js doesn't support parameterized queries directly,
   * but you can use string interpolation safely with proper validation
   */
  async insertTodoSafely(userId: string, title: string, description: string, priority: 'low' | 'medium' | 'high'): Promise<string> {
    // Escape single quotes to prevent SQL injection
    const safeTitle = title.replace(/'/g, "''");
    const safeDescription = description.replace(/'/g, "''");
    
    const result = await dbConnection.executeQuery(`
      INSERT INTO todos (user_id, title, description, priority)
      VALUES ('${userId}', '${safeTitle}', '${safeDescription}', '${priority}')
      RETURNING id, title
    `);

    const todoId = result.rows[0].id;
    console.log(`✅ Todo created: "${result.rows[0].title}" with ID: ${todoId}`);
    return todoId;
  }

  /**
   * 5. INSERT CATEGORIES AND RELATIONSHIPS
   */
  async insertCategoriesAndAssignments(): Promise<void> {
    console.log('Creating categories...');

    // Insert categories
    const categoryResult = await dbConnection.executeQuery(`
      INSERT INTO categories (name, color)
      VALUES 
        ('Work', '#EF4444'),
        ('Personal', '#10B981'),
        ('Learning', '#8B5CF6'),
        ('Health', '#F59E0B')
      RETURNING id, name
    `);

    console.log('✅ Categories created:', categoryResult.rows);

    // Get some todos and categories to create relationships
    const todos = await dbConnection.executeQuery('SELECT id FROM todos LIMIT 3');
    const categories = await dbConnection.executeQuery('SELECT id FROM categories LIMIT 2');

    if (todos.rows.length > 0 && categories.rows.length > 0) {
      // Assign categories to todos
      await dbConnection.executeQuery(`
        INSERT INTO todo_categories (todo_id, category_id)
        VALUES 
          ('${todos.rows[0].id}', '${categories.rows[0].id}'),
          ('${todos.rows[1].id}', '${categories.rows[1].id}'),
          ('${todos.rows[2].id}', '${categories.rows[0].id}')
      `);

      console.log('✅ Todo-category relationships created');
    }
  }

  /**
   * 6. BULK INSERT FROM DATA ARRAYS
   * Useful for importing large datasets
   */
  async bulkInsertTodos(userId: string, todosData: Array<{title: string, description: string, priority: string}>): Promise<void> {
    console.log(`Bulk inserting ${todosData.length} todos...`);

    // Build VALUES clause dynamically
    const values = todosData.map(todo => 
      `('${userId}', '${todo.title.replace(/'/g, "''")}', '${todo.description.replace(/'/g, "''")}', '${todo.priority}')`
    ).join(',\n        ');

    await dbConnection.executeQuery(`
      INSERT INTO todos (user_id, title, description, priority)
      VALUES 
        ${values}
    `);

    console.log('✅ Bulk insert completed');
  }

  /**
   * 7. INSERT WITH COMPLEX DATA TYPES
   * MotherDuck supports JSON, arrays, and other complex types
   */
  async insertTodoWithMetadata(): Promise<void> {
    console.log('Creating todo with complex metadata...');

    // First, let's add a metadata column to todos table
    await dbConnection.executeQuery(`
      ALTER TABLE todos 
      ADD COLUMN IF NOT EXISTS metadata JSON
    `);

    // Get a user ID
    const userResult = await dbConnection.executeQuery('SELECT id FROM users LIMIT 1');
    if (userResult.rows.length === 0) {
      throw new Error('No users found. Create a user first.');
    }
    const userId = userResult.rows[0].id;

    // Insert todo with JSON metadata
    await dbConnection.executeQuery(`
      INSERT INTO todos (user_id, title, description, priority, metadata)
      VALUES (
        '${userId}',
        'Complex Todo with Metadata',
        'This todo demonstrates JSON metadata storage',
        'medium',
        '{
          "tags": ["important", "complex", "demo"],
          "estimated_hours": 2.5,
          "dependencies": ["task-1", "task-2"],
          "custom_fields": {
            "client": "Acme Corp",
            "project_code": "PROJ-2025-001"
          }
        }'
      )
    `);

    console.log('✅ Todo with JSON metadata created');
  }

  /**
   * 8. QUERY DATA TO VERIFY INSERTIONS
   */
  async queryInsertedData(): Promise<void> {
    console.log('\n📊 Querying inserted data...');

    // Count records in each table
    const userCount = await dbConnection.executeQuery('SELECT COUNT(*) as count FROM users');
    const todoCount = await dbConnection.executeQuery('SELECT COUNT(*) as count FROM todos');
    const categoryCount = await dbConnection.executeQuery('SELECT COUNT(*) as count FROM categories');

    console.log(`Users: ${userCount.rows[0].count}`);
    console.log(`Todos: ${todoCount.rows[0].count}`);
    console.log(`Categories: ${categoryCount.rows[0].count}`);

    // Get todos with user and category information
    const todosWithDetails = await dbConnection.executeQuery(`
      SELECT 
        t.id,
        t.title,
        t.priority,
        t.completed,
        u.name as user_name,
        u.email as user_email,
        array_agg(c.name) as categories
      FROM todos t
      JOIN users u ON t.user_id = u.id
      LEFT JOIN todo_categories tc ON t.id = tc.todo_id
      LEFT JOIN categories c ON tc.category_id = c.id
      GROUP BY t.id, t.title, t.priority, t.completed, u.name, u.email
      ORDER BY t.created_at DESC
      LIMIT 5
    `);

    console.log('\n📝 Recent todos with details:');
    todosWithDetails.rows.forEach(todo => {
      console.log(`- ${todo.title} (${todo.priority}) - ${todo.user_name} - Categories: ${todo.categories || 'None'}`);
    });
  }

  /**
   * 9. RUN ALL EXAMPLES
   */
  async runAllExamples(): Promise<void> {
    try {
      console.log('🚀 Running MotherDuck Data Insertion Examples\n');

      // Initialize database connection first
      console.log('Initializing database connection...');
      await dbConnection.initialize();
      console.log('✅ Database connection established\n');

      // 1. Create tables
      await this.createTodoTables();

      // 2. Insert sample user
      const userId = await this.insertSampleUser();

      // 3. Insert sample todos
      await this.insertSampleTodos(userId);

      // 4. Insert a todo safely
      await this.insertTodoSafely(userId, "Safe Todo with 'quotes'", "This demonstrates safe insertion", 'high');

      // 5. Insert categories and relationships
      await this.insertCategoriesAndAssignments();

      // 6. Bulk insert example
      const bulkTodos = [
        { title: 'Bulk Todo 1', description: 'First bulk todo', priority: 'low' },
        { title: 'Bulk Todo 2', description: 'Second bulk todo', priority: 'medium' },
        { title: 'Bulk Todo 3', description: 'Third bulk todo', priority: 'high' }
      ];
      await this.bulkInsertTodos(userId, bulkTodos);

      // 7. Insert with complex data
      await this.insertTodoWithMetadata();

      // 8. Query and display results
      await this.queryInsertedData();

      console.log('\n🎉 All MotherDuck data insertion examples completed successfully!');

    } catch (error) {
      console.error('❌ Error running examples:', error);
      throw error;
    }
  }
}

// Export for use in other files
export const motherDuckExamples = new MotherDuckDataExamples();

// Run examples if this file is executed directly
if (require.main === module) {
  motherDuckExamples.runAllExamples()
    .then(() => {
      console.log('Examples completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Examples failed:', error);
      process.exit(1);
    });
}
