import { dbConnection } from './connection';

/**
 * Test Automatic Database Initialization
 * 
 * This script tests the automatic database schema initialization
 * when connecting to MotherDuck for the first time.
 */

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARN' | 'INFO';
  message: string;
  details?: any;
  timestamp: Date;
}

class AutoInitializationTester {
  private results: TestResult[] = [];

  private addResult(name: string, status: 'PASS' | 'FAIL' | 'WARN' | 'INFO', message: string, details?: any) {
    const result: TestResult = {
      name,
      status,
      message,
      details,
      timestamp: new Date()
    };
    this.results.push(result);
    
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'WARN' ? '⚠️' : 'ℹ️';
    console.log(`${icon} ${name}: ${message}`);
    if (details) {
      console.log(`   Details:`, JSON.stringify(details, null, 2));
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Testing Automatic Database Initialization');
    console.log('=============================================\n');

    try {
      await this.testConnectionAndInitialization();
      await this.testSchemaCreation();
      await this.testSeedData();
      await this.testInitializationTracking();
      await this.testReconnectionBehavior();
      
      this.printSummary();
    } catch (error) {
      console.error('❌ Auto-initialization test suite failed:', error);
      this.addResult('Test Suite', 'FAIL', `Critical error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async testConnectionAndInitialization(): Promise<void> {
    console.log('1. Testing Connection and Auto-Initialization...');
    
    try {
      // Initialize database connection (this should trigger auto-initialization)
      await dbConnection.initialize();
      
      const connectionType = dbConnection.getConnectionType();
      const initResult = dbConnection.getInitializationResult();
      
      this.addResult(
        'Connection & Initialization', 
        'PASS', 
        `Connected to ${connectionType} database`,
        { 
          connectionType,
          initializationResult: initResult
        }
      );

      if (connectionType === 'motherduck' && initResult) {
        if (initResult.wasInitialized) {
          this.addResult(
            'Schema Initialization', 
            'PASS', 
            `Database was initialized: ${initResult.message}`,
            {
              tablesCreated: initResult.tablesCreated,
              seedDataInserted: initResult.seedDataInserted,
              initializationTime: initResult.initializationTime
            }
          );
        } else {
          this.addResult(
            'Schema Initialization', 
            'INFO', 
            `Database was already initialized: ${initResult.message}`
          );
        }
      }

    } catch (error) {
      this.addResult(
        'Connection & Initialization', 
        'FAIL', 
        `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testSchemaCreation(): Promise<void> {
    console.log('\n2. Testing Schema Creation...');
    
    try {
      // Check if core tables exist
      const expectedTables = ['users', 'categories', 'todos', 'todo_categories', 'user_sessions'];
      const existingTables: string[] = [];
      
      for (const tableName of expectedTables) {
        try {
          const result = await dbConnection.executeQuery(`
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_name = '${tableName}'
          `);
          
          if (result.rows[0].count > 0) {
            existingTables.push(tableName);
          }
        } catch (error) {
          console.warn(`Failed to check table ${tableName}:`, error);
        }
      }
      
      if (existingTables.length === expectedTables.length) {
        this.addResult(
          'Schema Creation', 
          'PASS', 
          `All ${expectedTables.length} core tables exist`,
          { existingTables }
        );
      } else {
        this.addResult(
          'Schema Creation', 
          'WARN', 
          `Only ${existingTables.length}/${expectedTables.length} tables exist`,
          { 
            existingTables, 
            missingTables: expectedTables.filter(t => !existingTables.includes(t))
          }
        );
      }

      // Test table structure
      if (existingTables.includes('todos')) {
        const todoSchema = await dbConnection.executeQuery('DESCRIBE todos');
        this.addResult(
          'Table Structure', 
          'PASS', 
          'Todos table structure verified',
          { columns: todoSchema.rows.map(row => ({ name: row.column_name, type: row.column_type })) }
        );
      }

    } catch (error) {
      this.addResult(
        'Schema Creation', 
        'FAIL', 
        `Schema verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testSeedData(): Promise<void> {
    console.log('\n3. Testing Seed Data...');
    
    try {
      // Check if seed data exists
      const [userCount, categoryCount, todoCount] = await Promise.all([
        dbConnection.executeQuery('SELECT COUNT(*) as count FROM users'),
        dbConnection.executeQuery('SELECT COUNT(*) as count FROM categories'),
        dbConnection.executeQuery('SELECT COUNT(*) as count FROM todos')
      ]);

      const counts = {
        users: Number(userCount.rows[0].count),
        categories: Number(categoryCount.rows[0].count),
        todos: Number(todoCount.rows[0].count)
      };

      if (counts.users > 0 && counts.categories > 0 && counts.todos > 0) {
        this.addResult(
          'Seed Data', 
          'PASS', 
          'Seed data found in all tables',
          counts
        );

        // Check demo user
        const demoUser = await dbConnection.executeQuery(`
          SELECT email, name FROM users WHERE email = '<EMAIL>'
        `);

        if (demoUser.rows.length > 0) {
          this.addResult(
            'Demo User', 
            'PASS', 
            'Demo user created successfully',
            { user: demoUser.rows[0] }
          );
        }

        // Check sample todos
        const sampleTodos = await dbConnection.executeQuery(`
          SELECT t.title, t.priority, c.name as category
          FROM todos t
          LEFT JOIN todo_categories tc ON t.id = tc.todo_id
          LEFT JOIN categories c ON tc.category_id = c.id
          WHERE t.metadata::json->>'source' = 'seed_data'
          LIMIT 5
        `);

        if (sampleTodos.rows.length > 0) {
          this.addResult(
            'Sample Todos', 
            'PASS', 
            `${sampleTodos.rows.length} sample todos found`,
            { todos: sampleTodos.rows }
          );
        }

      } else {
        this.addResult(
          'Seed Data', 
          'WARN', 
          'Some tables are empty - seed data may not have been inserted',
          counts
        );
      }

    } catch (error) {
      this.addResult(
        'Seed Data', 
        'FAIL', 
        `Seed data verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testInitializationTracking(): Promise<void> {
    console.log('\n4. Testing Initialization Tracking...');
    
    try {
      if (dbConnection.isMotherDuckConnection()) {
        const history = await dbConnection.getInitializationHistory();
        
        if (history.length > 0) {
          const latestInit = history[0];
          this.addResult(
            'Initialization Tracking', 
            'PASS', 
            'Initialization history found',
            { 
              totalInitializations: history.length,
              latestInitialization: latestInit
            }
          );
        } else {
          this.addResult(
            'Initialization Tracking', 
            'WARN', 
            'No initialization history found'
          );
        }
      } else {
        this.addResult(
          'Initialization Tracking', 
          'INFO', 
          'Skipped - not connected to MotherDuck'
        );
      }

    } catch (error) {
      this.addResult(
        'Initialization Tracking', 
        'FAIL', 
        `Tracking verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testReconnectionBehavior(): Promise<void> {
    console.log('\n5. Testing Reconnection Behavior...');
    
    try {
      // Test that reconnection doesn't trigger re-initialization
      console.log('   Testing reconnection...');
      await dbConnection.reconnect();
      
      const newInitResult = dbConnection.getInitializationResult();
      
      if (newInitResult && !newInitResult.wasInitialized) {
        this.addResult(
          'Reconnection Behavior', 
          'PASS', 
          'Reconnection did not trigger re-initialization (correct behavior)',
          { initializationResult: newInitResult }
        );
      } else if (newInitResult && newInitResult.wasInitialized) {
        this.addResult(
          'Reconnection Behavior', 
          'WARN', 
          'Reconnection triggered re-initialization (may indicate issue)',
          { initializationResult: newInitResult }
        );
      } else {
        this.addResult(
          'Reconnection Behavior', 
          'INFO', 
          'No initialization result available after reconnection'
        );
      }

    } catch (error) {
      this.addResult(
        'Reconnection Behavior', 
        'FAIL', 
        `Reconnection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private printSummary(): void {
    console.log('\n📊 Auto-Initialization Test Summary');
    console.log('====================================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warned = this.results.filter(r => r.status === 'WARN').length;
    const info = this.results.filter(r => r.status === 'INFO').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️  Warnings: ${warned}`);
    console.log(`ℹ️  Info: ${info}`);
    console.log(`📝 Total: ${this.results.length}`);
    
    const connectionType = dbConnection.getConnectionType();
    const initResult = dbConnection.getInitializationResult();
    
    if (failed === 0) {
      if (connectionType === 'motherduck') {
        console.log('\n🎉 MotherDuck auto-initialization is working perfectly!');
        if (initResult?.wasInitialized) {
          console.log('🚀 Database was automatically initialized with schema and seed data');
        } else {
          console.log('✅ Database was already initialized - no re-initialization needed');
        }
      } else {
        console.log('\n✅ Local database connection working. Connect to MotherDuck to test auto-initialization.');
      }
    } else {
      console.log('\n⚠️  Some tests failed. Check the details above.');
    }
    
    console.log(`\n🔗 Connection Type: ${connectionType}`);
    if (initResult) {
      console.log(`📋 Initialization Status: ${initResult.message}`);
    }
  }
}

// Run the tests if this file is executed directly
if (require.main === module) {
  const tester = new AutoInitializationTester();
  tester.runAllTests()
    .then(() => {
      console.log('\nAuto-initialization tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Auto-initialization tests failed:', error);
      process.exit(1);
    });
}

export { AutoInitializationTester };
