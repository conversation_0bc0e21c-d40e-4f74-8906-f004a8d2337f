import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { config } from '@main/utils/config';
import { MCPError, MCPToolOptions, ConnectionStatus, QueryResult } from '@shared/types';
import * as fs from 'fs';
import * as path from 'path';

interface MCPServerConfig {
  name: string;
  command: string;
  args: string[];
  env?: Record<string, string>;
}

interface MCPServersConfig {
  mcpServers: Record<string, Omit<MCPServerConfig, 'name'>>;
}

export class MCPService {
  private static instance: MCPService;
  private clients: Map<string, Client> = new Map();
  private transports: Map<string, StdioClientTransport> = new Map();
  private connectionStatus: ConnectionStatus = {
    isConnected: false,
    connectionAttempts: 0,
  };
  private connectionRetryCount = 0;
  private maxRetries = 3;
  private retryDelay = 1000;
  private mcpServers: MCPServerConfig[] = [];

  private constructor() {}

  public static getInstance(): MCPService {
    if (!MCPService.instance) {
      MCPService.instance = new MCPService();
    }
    return MCPService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      const mcpConfig = config.getMCPConfig();
      this.maxRetries = mcpConfig.retryAttempts;
      this.retryDelay = mcpConfig.retryDelay;

      // Load MCP servers configuration
      await this.loadMCPServersConfig();

      // Connect to all configured MCP servers
      await this.connectToAllServers();
      await this.setupEventHandlers();
      console.log('MCP service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize MCP service:', error);
      throw new MCPError('MCP service initialization failed', 'INIT_ERROR', error);
    }
  }

  private async loadMCPServersConfig(): Promise<void> {
    try {
      const configPath = path.join(process.cwd(), '.roo', 'mcp.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        const config: MCPServersConfig = JSON.parse(configData);

        this.mcpServers = Object.entries(config.mcpServers).map(([name, serverConfig]) => ({
          name,
          ...serverConfig
        }));

        console.log(`Loaded ${this.mcpServers.length} MCP server configurations`);
      } else {
        console.warn('No MCP servers configuration found at .roo/mcp.json');
      }
    } catch (error) {
      console.error('Failed to load MCP servers configuration:', error);
      throw new MCPError('Failed to load MCP configuration', 'CONFIG_ERROR', error);
    }
  }

  private async connectToAllServers(): Promise<void> {
    this.connectionStatus.connectionAttempts++;

    const connectionPromises = this.mcpServers.map(server => this.connectToServer(server));
    const results = await Promise.allSettled(connectionPromises);

    const successfulConnections = results.filter(result => result.status === 'fulfilled').length;
    const failedConnections = results.filter(result => result.status === 'rejected');

    if (successfulConnections > 0) {
      this.connectionStatus.isConnected = true;
      this.connectionStatus.lastConnectionAt = new Date();
      this.connectionRetryCount = 0;
      this.connectionStatus.lastError = undefined;
      console.log(`Successfully connected to ${successfulConnections}/${this.mcpServers.length} MCP servers`);
    } else {
      this.connectionStatus.isConnected = false;
      const errors = failedConnections.map(result =>
        result.status === 'rejected' ? result.reason : 'Unknown error'
      );
      this.connectionStatus.lastError = `All connections failed: ${errors.join(', ')}`;
      console.error('Failed to connect to any MCP servers:', errors);
      throw new MCPError('All MCP server connections failed', 'CONNECTION_ERROR');
    }
  }

  private async connectToServer(serverConfig: MCPServerConfig): Promise<void> {
    try {
      console.log(`Connecting to MCP server: ${serverConfig.name}`);

      // Create transport for this specific server
      const transport = new StdioClientTransport({
        command: serverConfig.command,
        args: serverConfig.args,
        env: {
          ...Object.fromEntries(
            Object.entries(process.env).filter(([_, value]) => value !== undefined)
          ) as Record<string, string>,
          ...serverConfig.env
        }
      });

      // Create client for this server
      const client = new Client(
        {
          name: 'modern-todo-app',
          version: '1.0.0'
        },
        {
          capabilities: {
            tools: {},
            resources: {}
          }
        }
      );

      // Actually connect to the real MCP server
      await client.connect(transport);

      // Store the client and transport
      this.clients.set(serverConfig.name, client);
      this.transports.set(serverConfig.name, transport);

      console.log(`Successfully connected to MCP server: ${serverConfig.name}`);
    } catch (error) {
      console.error(`Failed to connect to MCP server ${serverConfig.name}:`, error);
      throw new MCPError(`Connection to ${serverConfig.name} failed`, 'CONNECTION_ERROR', error);
    }
  }

  private async handleConnectionFailure(error: any): Promise<void> {
    if (this.connectionRetryCount < this.maxRetries) {
      this.connectionRetryCount++;
      const delay = this.retryDelay * Math.pow(2, this.connectionRetryCount - 1);
      
      console.log(`Retrying MCP connection in ${delay}ms (attempt ${this.connectionRetryCount}/${this.maxRetries})`);
      
      setTimeout(async () => {
        await this.connectToAllServers();
      }, delay);
    } else {
      throw new MCPError(
        `Failed to connect to MCP server after ${this.maxRetries} attempts: ${error.message}`,
        'CONNECTION_ERROR',
        error
      );
    }
  }

  private async setupEventHandlers(): Promise<void> {
    // Setup event handlers for connection status changes
    // This would be implemented based on the actual MCP SDK events
  }

  public async executeTool(options: MCPToolOptions, serverName?: string): Promise<any> {
    if (!this.connectionStatus.isConnected) {
      throw new MCPError('MCP service not connected', 'NOT_CONNECTED');
    }

    try {
      // If no server specified, try to find the first available server that has the tool
      let targetClient: Client | undefined;
      let targetServerName: string | undefined;

      if (serverName) {
        targetClient = this.clients.get(serverName);
        targetServerName = serverName;
        if (!targetClient) {
          throw new MCPError(`MCP server '${serverName}' not found or not connected`, 'SERVER_NOT_FOUND');
        }
      } else {
        // Try to find a server that has the requested tool
        for (const [name, client] of this.clients.entries()) {
          try {
            const tools = await client.listTools();
            if (tools.tools.some(tool => tool.name === options.name)) {
              targetClient = client;
              targetServerName = name;
              break;
            }
          } catch (error) {
            console.warn(`Failed to list tools for server ${name}:`, error);
          }
        }

        if (!targetClient) {
          // If no server has the tool, use the first available server
          const firstServer = this.clients.entries().next().value;
          if (firstServer) {
            targetClient = firstServer[1];
            targetServerName = firstServer[0];
          }
        }
      }

      if (!targetClient || !targetServerName) {
        throw new MCPError('No MCP servers available', 'NO_SERVERS');
      }

      console.log(`Executing MCP tool '${options.name}' on server '${targetServerName}'`);

      // Execute the tool on the real MCP server
      const result = await targetClient.callTool({
        name: options.name,
        arguments: options.arguments || {}
      });

      console.log(`Tool '${options.name}' executed successfully on server '${targetServerName}'`);
      return result;
    } catch (error) {
      console.error(`Failed to execute MCP tool ${options.name}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new MCPError(`Tool execution failed: ${errorMessage}`, 'TOOL_ERROR', error);
    }
  }

  // Real MCP server interaction methods
  public async listAvailableTools(serverName?: string): Promise<any> {
    if (!this.connectionStatus.isConnected) {
      throw new MCPError('MCP service not connected', 'NOT_CONNECTED');
    }

    try {
      if (serverName) {
        const client = this.clients.get(serverName);
        if (!client) {
          throw new MCPError(`MCP server '${serverName}' not found`, 'SERVER_NOT_FOUND');
        }
        return await client.listTools();
      } else {
        // List tools from all connected servers
        const allTools: any = { servers: {} };
        for (const [name, client] of this.clients.entries()) {
          try {
            const tools = await client.listTools();
            allTools.servers[name] = tools;
          } catch (error) {
            console.warn(`Failed to list tools for server ${name}:`, error);
            allTools.servers[name] = { error: error instanceof Error ? error.message : 'Unknown error' };
          }
        }
        return allTools;
      }
    } catch (error) {
      console.error('Failed to list available tools:', error);
      throw new MCPError('Failed to list tools', 'LIST_TOOLS_ERROR', error);
    }
  }

  public async listAvailableResources(serverName?: string): Promise<any> {
    if (!this.connectionStatus.isConnected) {
      throw new MCPError('MCP service not connected', 'NOT_CONNECTED');
    }

    try {
      if (serverName) {
        const client = this.clients.get(serverName);
        if (!client) {
          throw new MCPError(`MCP server '${serverName}' not found`, 'SERVER_NOT_FOUND');
        }
        return await client.listResources();
      } else {
        // List resources from all connected servers
        const allResources: any = { servers: {} };
        for (const [name, client] of this.clients.entries()) {
          try {
            const resources = await client.listResources();
            allResources.servers[name] = resources;
          } catch (error) {
            console.warn(`Failed to list resources for server ${name}:`, error);
            allResources.servers[name] = { error: error instanceof Error ? error.message : 'Unknown error' };
          }
        }
        return allResources;
      }
    } catch (error) {
      console.error('Failed to list available resources:', error);
      throw new MCPError('Failed to list resources', 'LIST_RESOURCES_ERROR', error);
    }
  }

  public getConnectedServers(): string[] {
    return Array.from(this.clients.keys());
  }

  // Convenience methods for common operations
  public async executeQuery(sql: string, parameters: any[] = []): Promise<QueryResult> {
    return this.executeTool({
      name: 'execute_query',
      arguments: { sql, parameters }
    });
  }

  public async createTable(schema: any): Promise<any> {
    return this.executeTool({
      name: 'create_table',
      arguments: { schema }
    });
  }

  public async insertData(table: string, data: any[]): Promise<any> {
    return this.executeTool({
      name: 'insert_data',
      arguments: { table, data }
    });
  }

  public async updateData(table: string, updates: any, where: any): Promise<any> {
    return this.executeTool({
      name: 'update_data',
      arguments: { table, updates, where }
    });
  }

  public async deleteData(table: string, where: any): Promise<any> {
    return this.executeTool({
      name: 'delete_data',
      arguments: { table, where }
    });
  }

  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  public isConnected(): boolean {
    return this.connectionStatus.isConnected;
  }

  public async disconnect(): Promise<void> {
    try {
      // Disconnect from all servers
      const disconnectPromises: Promise<void>[] = [];

      for (const [serverName, client] of this.clients.entries()) {
        disconnectPromises.push(this.disconnectFromServer(serverName, client));
      }

      await Promise.allSettled(disconnectPromises);

      // Clear all connections
      this.clients.clear();
      this.transports.clear();

      this.connectionStatus.isConnected = false;
      console.log('MCP service disconnected successfully from all servers');
    } catch (error) {
      console.error('Error disconnecting MCP service:', error);
      throw new MCPError('Failed to disconnect MCP service', 'DISCONNECT_ERROR', error);
    }
  }

  private async disconnectFromServer(serverName: string, client: Client): Promise<void> {
    try {
      console.log(`Disconnecting from MCP server: ${serverName}`);
      await client.close();

      const transport = this.transports.get(serverName);
      if (transport) {
        await transport.close();
      }

      console.log(`Successfully disconnected from MCP server: ${serverName}`);
    } catch (error) {
      console.error(`Error disconnecting from server ${serverName}:`, error);
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.connectionStatus.isConnected) {
        return false;
      }

      // Test connection by listing tools from at least one server
      const servers = this.getConnectedServers();
      if (servers.length === 0) {
        return false;
      }

      try {
        await this.listAvailableTools(servers[0]);
        return true;
      } catch (error) {
        console.warn('MCP health check failed:', error);
        return false;
      }
    } catch (error) {
      console.warn('MCP health check failed:', error);
      return false;
    }
  }

  public async reconnect(): Promise<void> {
    await this.disconnect();
    this.connectionRetryCount = 0;
    await this.connectToAllServers();
  }
}

// Export singleton instance
export const mcpService = MCPService.getInstance();