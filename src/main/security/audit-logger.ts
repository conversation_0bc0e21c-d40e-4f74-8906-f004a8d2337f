import * as fs from 'fs/promises';
import * as path from 'path';
import { securityConfig } from './security-config';

export enum AuditEventType {
  // Authentication events
  LOGIN_SUCCESS = 'LOGI<PERSON>_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  REGISTRATION = 'REGISTRATION',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',

  // Authorization events
  ACCESS_GRANTED = 'ACCESS_GRANTED',
  ACCESS_DENIED = 'ACCESS_DENIED',
  PERMISSION_ESCALATION = 'PERMISSION_ESCALATION',

  // Data events
  DATA_CREATE = 'DATA_CREATE',
  DATA_READ = 'DATA_READ',
  DATA_UPDATE = 'DATA_UPDATE',
  DATA_DELETE = 'DATA_DELETE',
  DATA_EXPORT = 'DATA_EXPORT',

  // Security events
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  INTRUSION_ATTEMPT = 'INTRUSION_ATTEMPT',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  ENCRYPTION_ERROR = 'ENCRYPTION_ERROR',

  // System events
  APPLICATION_START = 'APPLICATION_START',
  APPLICATION_STOP = 'APPLICATION_STOP',
  DATABASE_CONNECTION = 'DATABASE_CONNECTION',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',

  // IPC events
  IPC_CALL = 'IPC_CALL',
  IPC_ERROR = 'IPC_ERROR',
  IPC_VALIDATION_ERROR = 'IPC_VALIDATION_ERROR',
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface AuditEvent {
  id: string;
  timestamp: string;
  type: AuditEventType;
  severity: AuditSeverity;
  userId?: string;
  username?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  details: Record<string, any>;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export class AuditLogger {
  private static instance: AuditLogger;
  private logQueue: AuditEvent[] = [];
  private isWriting = false;
  private writeInterval: NodeJS.Timeout;

  private constructor() {
    // Process log queue every 5 seconds
    this.writeInterval = setInterval(() => {
      this.processLogQueue();
    }, 5000);

    // Ensure logs directory exists
    this.ensureLogDirectory();
  }

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  public destroy(): void {
    if (this.writeInterval) {
      clearInterval(this.writeInterval);
    }
    // Process any remaining logs
    this.processLogQueue();
  }

  public async log(event: Partial<AuditEvent>): Promise<void> {
    const config = securityConfig.getSecurityConfig();
    
    if (!config.enableAuditLogging) {
      return;
    }

    const auditEvent: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type: event.type || AuditEventType.IPC_CALL,
      severity: event.severity || AuditSeverity.LOW,
      userId: event.userId,
      username: event.username,
      sessionId: event.sessionId,
      ipAddress: event.ipAddress || 'localhost',
      userAgent: event.userAgent,
      resource: event.resource,
      action: event.action,
      details: event.details || {},
      success: event.success ?? true,
      errorMessage: event.errorMessage,
      metadata: event.metadata || {},
    };

    // Add to queue for batch processing
    this.logQueue.push(auditEvent);

    // If it's a critical event, process immediately
    if (auditEvent.severity === AuditSeverity.CRITICAL) {
      await this.processLogQueue();
    }
  }

  // Convenience methods for common audit events
  public async logAuthentication(
    type: AuditEventType.LOGIN_SUCCESS | AuditEventType.LOGIN_FAILURE | AuditEventType.LOGOUT,
    username: string,
    success: boolean,
    details: Record<string, any> = {},
    sessionId?: string
  ): Promise<void> {
    await this.log({
      type,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      username,
      sessionId,
      success,
      details: {
        ...details,
        timestamp: Date.now(),
      },
    });
  }

  public async logDataAccess(
    action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE',
    resource: string,
    userId: string,
    success: boolean,
    details: Record<string, any> = {}
  ): Promise<void> {
    const typeMap = {
      CREATE: AuditEventType.DATA_CREATE,
      READ: AuditEventType.DATA_READ,
      UPDATE: AuditEventType.DATA_UPDATE,
      DELETE: AuditEventType.DATA_DELETE,
    };

    await this.log({
      type: typeMap[action],
      severity: action === 'DELETE' ? AuditSeverity.MEDIUM : AuditSeverity.LOW,
      userId,
      resource,
      action,
      success,
      details,
    });
  }

  public async logSecurityEvent(
    type: AuditEventType,
    severity: AuditSeverity,
    details: Record<string, any>,
    userId?: string,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      type,
      severity,
      userId,
      success: false,
      errorMessage,
      details: {
        ...details,
        securityEvent: true,
        timestamp: Date.now(),
      },
    });
  }

  public async logIPCCall(
    channel: string,
    userId?: string,
    success: boolean = true,
    details: Record<string, any> = {},
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      type: success ? AuditEventType.IPC_CALL : AuditEventType.IPC_ERROR,
      severity: success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      userId,
      resource: channel,
      action: 'IPC_CALL',
      success,
      errorMessage,
      details: {
        channel,
        ...details,
      },
    });
  }

  private async processLogQueue(): Promise<void> {
    if (this.isWriting || this.logQueue.length === 0) {
      return;
    }

    this.isWriting = true;

    try {
      const config = securityConfig.getSecurityConfig();
      const logsToWrite = [...this.logQueue];
      this.logQueue = [];

      // Write to audit log file
      const auditLogPath = config.auditLogFile;
      const logEntries = logsToWrite.map(event => JSON.stringify(event)).join('\n') + '\n';

      await this.writeToLogFile(auditLogPath, logEntries);

      // Write security events to separate security log
      const securityEvents = logsToWrite.filter(event => 
        event.severity === AuditSeverity.HIGH || 
        event.severity === AuditSeverity.CRITICAL ||
        event.type.includes('SECURITY') ||
        event.type.includes('INTRUSION') ||
        event.type.includes('SUSPICIOUS')
      );

      if (securityEvents.length > 0) {
        const securityLogPath = config.securityLogFile;
        const securityLogEntries = securityEvents.map(event => JSON.stringify(event)).join('\n') + '\n';
        await this.writeToLogFile(securityLogPath, securityLogEntries);
      }

    } catch (error) {
      console.error('Failed to write audit logs:', error);
      // Re-add failed logs to queue for retry
      this.logQueue.unshift(...this.logQueue);
    } finally {
      this.isWriting = false;
    }
  }

  private async writeToLogFile(filePath: string, content: string): Promise<void> {
    try {
      await fs.appendFile(filePath, content, 'utf8');
      
      // Check file size and rotate if necessary
      await this.rotateLogIfNeeded(filePath);
    } catch (error) {
      console.error(`Failed to write to log file ${filePath}:`, error);
      throw error;
    }
  }

  private async rotateLogIfNeeded(filePath: string): Promise<void> {
    try {
      const config = securityConfig.getSecurityConfig();
      const stats = await fs.stat(filePath);
      
      if (stats.size > config.auditLogMaxSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = `${filePath}.${timestamp}`;
        
        await fs.rename(filePath, rotatedPath);
        
        // Clean up old log files
        await this.cleanupOldLogs(path.dirname(filePath), path.basename(filePath));
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private async cleanupOldLogs(logDir: string, baseFileName: string): Promise<void> {
    try {
      const config = securityConfig.getSecurityConfig();
      const files = await fs.readdir(logDir);
      
      const logFiles = files
        .filter(file => file.startsWith(baseFileName) && file !== baseFileName)
        .map(file => ({
          name: file,
          path: path.join(logDir, file),
        }))
        .sort((a, b) => b.name.localeCompare(a.name)); // Sort by name (newest first)

      // Keep only the specified number of files
      const filesToDelete = logFiles.slice(config.auditLogMaxFiles);
      
      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }
    } catch (error) {
      console.error('Failed to cleanup old log files:', error);
    }
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      const config = securityConfig.getSecurityConfig();
      const auditLogDir = path.dirname(config.auditLogFile);
      const securityLogDir = path.dirname(config.securityLogFile);

      await fs.mkdir(auditLogDir, { recursive: true });
      
      if (auditLogDir !== securityLogDir) {
        await fs.mkdir(securityLogDir, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create log directories:', error);
    }
  }

  private generateEventId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${timestamp}-${random}`;
  }

  // Query methods for log analysis
  public async getRecentEvents(
    count: number = 100,
    eventType?: AuditEventType,
    severity?: AuditSeverity
  ): Promise<AuditEvent[]> {
    // This is a simplified implementation
    // In a production system, you might want to use a proper log analysis tool
    return this.logQueue
      .filter(event => {
        if (eventType && event.type !== eventType) return false;
        if (severity && event.severity !== severity) return false;
        return true;
      })
      .slice(-count);
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();
