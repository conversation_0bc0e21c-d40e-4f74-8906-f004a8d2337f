import { z } from 'zod';
import { securityConfig } from './security-config';

export class InputValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'InputValidationError';
  }
}

export class InputValidator {
  private static instance: InputValidator;

  private constructor() {}

  public static getInstance(): InputValidator {
    if (!InputValidator.instance) {
      InputValidator.instance = new InputValidator();
    }
    return InputValidator.instance;
  }

  // Common validation schemas
  public readonly schemas = {
    // User authentication schemas
    username: z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(50, 'Username must not exceed 50 characters')
      .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),

    password: z.string()
      .min(securityConfig.getSecurityConfig().passwordMinLength, 
           `Password must be at least ${securityConfig.getSecurityConfig().passwordMinLength} characters`)
      .max(128, 'Password must not exceed 128 characters')
      .refine(this.validatePasswordPolicy.bind(this), {
        message: 'Password does not meet security requirements'
      }),

    email: z.string()
      .email('Invalid email format')
      .max(254, 'Email must not exceed 254 characters'),

    // Todo-related schemas
    todoTitle: z.string()
      .min(1, 'Todo title is required')
      .max(200, 'Todo title must not exceed 200 characters')
      .transform(this.sanitizeString.bind(this)),

    todoDescription: z.string()
      .max(1000, 'Todo description must not exceed 1000 characters')
      .optional()
      .transform(this.sanitizeString.bind(this)),

    categoryName: z.string()
      .min(1, 'Category name is required')
      .max(100, 'Category name must not exceed 100 characters')
      .transform(this.sanitizeString.bind(this)),

    // ID validation
    id: z.string()
      .uuid('Invalid ID format'),

    // Pagination and filtering
    limit: z.number()
      .int('Limit must be an integer')
      .min(1, 'Limit must be at least 1')
      .max(100, 'Limit must not exceed 100'),

    offset: z.number()
      .int('Offset must be an integer')
      .min(0, 'Offset must be non-negative'),

    // Search and filtering
    searchQuery: z.string()
      .max(100, 'Search query must not exceed 100 characters')
      .transform(this.sanitizeString.bind(this)),

    // Date validation
    date: z.string()
      .datetime('Invalid date format')
      .or(z.date()),

    // Priority validation
    priority: z.enum(['low', 'medium', 'high', 'urgent']),

    // Status validation
    status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']),
  };

  // Composite validation schemas
  public readonly compositeSchemas = {
    loginCredentials: z.object({
      username: this.schemas.username,
      password: z.string().min(1, 'Password is required'),
      deviceInfo: z.object({
        userAgent: z.string().optional(),
        platform: z.string().optional(),
        version: z.string().optional(),
      }).optional(),
      totpCode: z.string().length(6, 'TOTP code must be 6 digits').optional(),
    }),

    registerData: z.object({
      username: this.schemas.username,
      password: this.schemas.password,
      email: this.schemas.email,
      fullName: z.string()
        .min(1, 'Full name is required')
        .max(100, 'Full name must not exceed 100 characters')
        .transform(this.sanitizeString.bind(this)),
    }),

    createTodo: z.object({
      title: this.schemas.todoTitle,
      description: this.schemas.todoDescription,
      priority: this.schemas.priority.optional(),
      dueDate: this.schemas.date.optional(),
      categoryId: this.schemas.id.optional(),
    }),

    updateTodo: z.object({
      id: this.schemas.id,
      title: this.schemas.todoTitle.optional(),
      description: this.schemas.todoDescription,
      priority: this.schemas.priority.optional(),
      status: this.schemas.status.optional(),
      dueDate: this.schemas.date.optional(),
      categoryId: this.schemas.id.optional(),
    }),

    createCategory: z.object({
      name: this.schemas.categoryName,
      color: z.string()
        .regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid color format')
        .optional(),
      description: z.string()
        .max(500, 'Description must not exceed 500 characters')
        .optional()
        .transform(this.sanitizeString.bind(this)),
    }),

    searchTodos: z.object({
      query: this.schemas.searchQuery.optional(),
      status: this.schemas.status.optional(),
      priority: this.schemas.priority.optional(),
      categoryId: this.schemas.id.optional(),
      limit: this.schemas.limit.optional(),
      offset: this.schemas.offset.optional(),
    }),
  };

  public validate<T>(schema: z.ZodSchema<T>, data: unknown): T {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        throw new InputValidationError(
          firstError.message,
          firstError.path.join('.')
        );
      }
      throw new InputValidationError('Validation failed');
    }
  }

  public validateAsync<T>(schema: z.ZodSchema<T>, data: unknown): Promise<T> {
    return new Promise((resolve, reject) => {
      try {
        const result = this.validate(schema, data);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
  }

  private validatePasswordPolicy(password: string): boolean {
    const config = securityConfig.getSecurityConfig();
    
    if (config.passwordRequireUppercase && !/[A-Z]/.test(password)) {
      return false;
    }
    
    if (config.passwordRequireLowercase && !/[a-z]/.test(password)) {
      return false;
    }
    
    if (config.passwordRequireNumbers && !/\d/.test(password)) {
      return false;
    }
    
    if (config.passwordRequireSpecial && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      return false;
    }
    
    return true;
  }

  private sanitizeString(input: string | undefined): string | undefined {
    if (!input) return input;
    
    // Remove potentially dangerous characters and normalize whitespace
    return input
      .replace(/[<>\"'&]/g, '') // Remove HTML/XML special characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  public sanitizeForDatabase(input: string): string {
    if (!input) return '';
    
    // Additional sanitization for database storage
    return input
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/[<>\"'&]/g, '') // Remove HTML/XML special characters
      .trim();
  }

  public validateIPCPayload(channel: string, data: unknown): unknown {
    // Channel-specific validation
    switch (channel) {
      case 'auth:login':
        return this.validate(this.compositeSchemas.loginCredentials, data);
      
      case 'auth:register':
        return this.validate(this.compositeSchemas.registerData, data);
      
      case 'todo:create':
        return this.validate(this.compositeSchemas.createTodo, data);
      
      case 'todo:update':
        return this.validate(this.compositeSchemas.updateTodo, data);
      
      case 'todo:search':
        return this.validate(this.compositeSchemas.searchTodos, data);
      
      case 'category:create':
        return this.validate(this.compositeSchemas.createCategory, data);
      
      case 'todo:get':
      case 'todo:delete':
      case 'category:get':
      case 'category:delete':
        return this.validate(z.object({ id: this.schemas.id }), data);
      
      default:
        // For unknown channels, perform basic sanitization
        if (typeof data === 'object' && data !== null) {
          return this.sanitizeObject(data);
        }
        return data;
    }
  }

  private sanitizeObject(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
          sanitized[key] = this.sanitizeForDatabase(value);
        } else {
          sanitized[key] = this.sanitizeObject(value);
        }
      }
      return sanitized;
    }
    
    return obj;
  }
}

// Export singleton instance
export const inputValidator = InputValidator.getInstance();
