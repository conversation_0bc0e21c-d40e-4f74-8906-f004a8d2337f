import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';
import { securityConfig } from './security-config';

export interface SuspiciousActivity {
  id: string;
  timestamp: number;
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  userId?: string;
  username?: string;
  ipAddress?: string;
  details: Record<string, any>;
  count: number;
}

export interface IntrusionPattern {
  name: string;
  description: string;
  threshold: number;
  timeWindowMs: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  check: (activities: SuspiciousActivity[]) => boolean;
}

export class IntrusionDetectionSystem {
  private static instance: IntrusionDetectionSystem;
  private suspiciousActivities: Map<string, SuspiciousActivity[]> = new Map();
  private patterns: IntrusionPattern[] = [];
  private cleanupInterval: NodeJS.Timeout;

  private constructor() {
    this.initializePatterns();
    
    // Clean up old activities every 10 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldActivities();
    }, 10 * 60 * 1000);
  }

  public static getInstance(): IntrusionDetectionSystem {
    if (!IntrusionDetectionSystem.instance) {
      IntrusionDetectionSystem.instance = new IntrusionDetectionSystem();
    }
    return IntrusionDetectionSystem.instance;
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.suspiciousActivities.clear();
  }

  private initializePatterns(): void {
    this.patterns = [
      // Brute force login attempts
      {
        name: 'BRUTE_FORCE_LOGIN',
        description: 'Multiple failed login attempts',
        threshold: 5,
        timeWindowMs: 15 * 60 * 1000, // 15 minutes
        severity: 'HIGH',
        check: (activities) => {
          const failedLogins = activities.filter(a => a.type === 'FAILED_LOGIN');
          return failedLogins.length >= 5;
        },
      },

      // Rapid successive requests
      {
        name: 'RAPID_REQUESTS',
        description: 'Unusually high number of requests in short time',
        threshold: 50,
        timeWindowMs: 60 * 1000, // 1 minute
        severity: 'MEDIUM',
        check: (activities) => {
          const recentRequests = activities.filter(a => a.type === 'IPC_REQUEST');
          return recentRequests.length >= 50;
        },
      },

      // Multiple validation errors
      {
        name: 'VALIDATION_ERRORS',
        description: 'Multiple input validation errors',
        threshold: 10,
        timeWindowMs: 5 * 60 * 1000, // 5 minutes
        severity: 'MEDIUM',
        check: (activities) => {
          const validationErrors = activities.filter(a => a.type === 'VALIDATION_ERROR');
          return validationErrors.length >= 10;
        },
      },

      // Privilege escalation attempts
      {
        name: 'PRIVILEGE_ESCALATION',
        description: 'Attempts to access unauthorized resources',
        threshold: 3,
        timeWindowMs: 10 * 60 * 1000, // 10 minutes
        severity: 'CRITICAL',
        check: (activities) => {
          const escalationAttempts = activities.filter(a => a.type === 'UNAUTHORIZED_ACCESS');
          return escalationAttempts.length >= 3;
        },
      },

      // Suspicious data access patterns
      {
        name: 'DATA_ENUMERATION',
        description: 'Systematic data access attempts',
        threshold: 20,
        timeWindowMs: 5 * 60 * 1000, // 5 minutes
        severity: 'HIGH',
        check: (activities) => {
          const dataAccess = activities.filter(a => a.type === 'DATA_ACCESS');
          return dataAccess.length >= 20;
        },
      },
    ];
  }

  public async recordActivity(
    type: string,
    identifier: string,
    details: Record<string, any> = {},
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW'
  ): Promise<void> {
    const config = securityConfig.getSecurityConfig();
    
    if (!config.enableIntrusionDetection) {
      return;
    }

    const activity: SuspiciousActivity = {
      id: this.generateActivityId(),
      timestamp: Date.now(),
      type,
      severity,
      userId: details.userId,
      username: details.username,
      ipAddress: details.ipAddress || 'localhost',
      details,
      count: 1,
    };

    // Get existing activities for this identifier
    const activities = this.suspiciousActivities.get(identifier) || [];
    
    // Check if we have a similar recent activity
    const recentSimilar = activities.find(a => 
      a.type === type && 
      Date.now() - a.timestamp < 60000 // Within 1 minute
    );

    if (recentSimilar) {
      // Increment count instead of creating new activity
      recentSimilar.count++;
      recentSimilar.timestamp = Date.now();
    } else {
      // Add new activity
      activities.push(activity);
    }

    this.suspiciousActivities.set(identifier, activities);

    // Check for intrusion patterns
    await this.checkIntrusionPatterns(identifier, activities);

    // Log the activity
    await auditLogger.log({
      type: AuditEventType.SUSPICIOUS_ACTIVITY,
      severity: this.mapSeverity(severity),
      userId: details.userId,
      username: details.username,
      success: false,
      details: {
        activityType: type,
        ...details,
      },
    });
  }

  private async checkIntrusionPatterns(
    identifier: string,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    const now = Date.now();

    for (const pattern of this.patterns) {
      // Filter activities within the time window
      const relevantActivities = activities.filter(
        activity => now - activity.timestamp <= pattern.timeWindowMs
      );

      // Check if pattern matches
      if (pattern.check(relevantActivities)) {
        await this.handleIntrusionDetected(identifier, pattern, relevantActivities);
      }
    }
  }

  private async handleIntrusionDetected(
    identifier: string,
    pattern: IntrusionPattern,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    console.warn(`🚨 INTRUSION DETECTED: ${pattern.name} for ${identifier}`);

    // Log the intrusion attempt
    await auditLogger.log({
      type: AuditEventType.INTRUSION_ATTEMPT,
      severity: this.mapSeverity(pattern.severity),
      userId: activities[0]?.userId,
      username: activities[0]?.username,
      success: false,
      details: {
        pattern: pattern.name,
        description: pattern.description,
        identifier,
        activityCount: activities.length,
        timeWindow: pattern.timeWindowMs,
        activities: activities.map(a => ({
          type: a.type,
          timestamp: a.timestamp,
          count: a.count,
        })),
      },
    });

    // Take appropriate action based on severity
    switch (pattern.severity) {
      case 'CRITICAL':
        await this.handleCriticalIntrusion(identifier, pattern, activities);
        break;
      case 'HIGH':
        await this.handleHighSeverityIntrusion(identifier, pattern, activities);
        break;
      case 'MEDIUM':
        await this.handleMediumSeverityIntrusion(identifier, pattern, activities);
        break;
      case 'LOW':
        await this.handleLowSeverityIntrusion(identifier, pattern, activities);
        break;
    }
  }

  private async handleCriticalIntrusion(
    identifier: string,
    pattern: IntrusionPattern,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    // For critical intrusions, we might want to:
    // 1. Immediately lock the account
    // 2. Send alerts to administrators
    // 3. Increase monitoring
    console.error(`🔴 CRITICAL INTRUSION: ${pattern.name} - Taking immediate action`);
    
    // Clear all activities for this identifier to prevent further actions
    this.suspiciousActivities.delete(identifier);
  }

  private async handleHighSeverityIntrusion(
    identifier: string,
    pattern: IntrusionPattern,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    console.warn(`🟠 HIGH SEVERITY INTRUSION: ${pattern.name} - Increasing monitoring`);
  }

  private async handleMediumSeverityIntrusion(
    identifier: string,
    pattern: IntrusionPattern,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    console.warn(`🟡 MEDIUM SEVERITY INTRUSION: ${pattern.name} - Monitoring closely`);
  }

  private async handleLowSeverityIntrusion(
    identifier: string,
    pattern: IntrusionPattern,
    activities: SuspiciousActivity[]
  ): Promise<void> {
    console.info(`🔵 LOW SEVERITY INTRUSION: ${pattern.name} - Logged for analysis`);
  }

  private mapSeverity(severity: string): AuditSeverity {
    switch (severity) {
      case 'CRITICAL': return AuditSeverity.CRITICAL;
      case 'HIGH': return AuditSeverity.HIGH;
      case 'MEDIUM': return AuditSeverity.MEDIUM;
      default: return AuditSeverity.LOW;
    }
  }

  private cleanupOldActivities(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [identifier, activities] of this.suspiciousActivities.entries()) {
      const recentActivities = activities.filter(
        activity => now - activity.timestamp <= maxAge
      );

      if (recentActivities.length === 0) {
        this.suspiciousActivities.delete(identifier);
      } else {
        this.suspiciousActivities.set(identifier, recentActivities);
      }
    }
  }

  private generateActivityId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  // Public methods for monitoring and analysis
  public getActivitiesForIdentifier(identifier: string): SuspiciousActivity[] {
    return this.suspiciousActivities.get(identifier) || [];
  }

  public getAllSuspiciousActivities(): Map<string, SuspiciousActivity[]> {
    return new Map(this.suspiciousActivities);
  }

  public getActiveIdentifiers(): string[] {
    return Array.from(this.suspiciousActivities.keys());
  }

  public clearActivitiesForIdentifier(identifier: string): void {
    this.suspiciousActivities.delete(identifier);
  }
}

// Export singleton instance
export const intrusionDetection = IntrusionDetectionSystem.getInstance();
