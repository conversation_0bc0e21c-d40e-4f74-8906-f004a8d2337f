import { ipc<PERSON>ain, IpcMainInvokeEvent } from 'electron';
import { inputValidator, InputValidationError } from './input-validator';
import { rateLimiter, RateLimitError } from './rate-limiter';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';
import { intrusionDetection } from './intrusion-detection';
import { securityConfig } from './security-config';

export interface IPCSecurityContext {
  userId?: string;
  username?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface SecureIPCHandler {
  (event: IpcMainInvokeEvent, data: any, context: IPCSecurityContext): Promise<any>;
}

export class IPCSecurityError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'IPCSecurityError';
  }
}

export class IPCSecurity {
  private static instance: IPCSecurity;
  private handlers: Map<string, SecureIPCHandler> = new Map();
  private authenticatedSessions: Set<string> = new Set();

  private constructor() {
    this.setupSecureIPCHandlers();
  }

  public static getInstance(): IPCSecurity {
    if (!IPCSecurity.instance) {
      IPCSecurity.instance = new IPCSecurity();
    }
    return IPCSecurity.instance;
  }

  public registerSecureHandler(
    channel: string,
    handler: SecureIPCHandler,
    requireAuth: boolean = true
  ): void {
    // Wrap the handler with security middleware
    const secureHandler = async (event: IpcMainInvokeEvent, data: any) => {
      const startTime = Date.now();
      let success = false;
      let errorMessage: string | undefined;
      let context: IPCSecurityContext = {};

      try {
        // Extract security context
        context = this.extractSecurityContext(event, data);

        // Rate limiting
        const identifier = context.userId || context.sessionId || 'anonymous';
        await rateLimiter.checkIPCLimit(identifier, channel);

        // Authentication check
        if (requireAuth && !this.isAuthenticated(context.sessionId)) {
          throw new IPCSecurityError('Authentication required', 'AUTH_REQUIRED', 401);
        }

        // Input validation
        const validatedData = inputValidator.validateIPCPayload(channel, data);

        // Call the actual handler
        const result = await handler(event, validatedData, context);
        success = true;

        // Log successful IPC call
        await auditLogger.logIPCCall(channel, context.userId, true, {
          duration: Date.now() - startTime,
          dataSize: JSON.stringify(data).length,
        });

        return result;

      } catch (error) {
        success = false;
        errorMessage = error instanceof Error ? error.message : 'Unknown error';

        // Handle different types of errors
        if (error instanceof RateLimitError) {
          await this.handleRateLimitError(channel, context, error);
        } else if (error instanceof InputValidationError) {
          await this.handleValidationError(channel, context, error);
        } else if (error instanceof IPCSecurityError) {
          await this.handleSecurityError(channel, context, error);
        } else {
          await this.handleGenericError(channel, context, error);
        }

        // Log failed IPC call
        await auditLogger.logIPCCall(channel, context.userId, false, {
          duration: Date.now() - startTime,
          errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        }, errorMessage);

        // Record suspicious activity for intrusion detection
        await intrusionDetection.recordActivity(
          'IPC_ERROR',
          context.userId || context.sessionId || 'anonymous',
          {
            channel,
            errorType: error instanceof Error ? error.constructor.name : 'Unknown',
            ...context,
          },
          this.getErrorSeverity(error)
        );

        throw error;
      }
    };

    // Register the secure handler
    ipcMain.handle(channel, secureHandler);
    this.handlers.set(channel, handler);
  }

  public removeHandler(channel: string): void {
    ipcMain.removeHandler(channel);
    this.handlers.delete(channel);
  }

  public authenticateSession(sessionId: string): void {
    this.authenticatedSessions.add(sessionId);
  }

  public deauthenticateSession(sessionId: string): void {
    this.authenticatedSessions.delete(sessionId);
  }

  public isAuthenticated(sessionId?: string): boolean {
    if (!sessionId) return false;
    return this.authenticatedSessions.has(sessionId);
  }

  private setupSecureIPCHandlers(): void {
    // Set up global error handler for unhandled IPC errors
    process.on('uncaughtException', async (error) => {
      await auditLogger.log({
        type: AuditEventType.IPC_ERROR,
        severity: AuditSeverity.CRITICAL,
        success: false,
        errorMessage: error.message,
        details: {
          stack: error.stack,
          uncaught: true,
        },
      });
    });

    // Set up global promise rejection handler
    process.on('unhandledRejection', async (reason, promise) => {
      await auditLogger.log({
        type: AuditEventType.IPC_ERROR,
        severity: AuditSeverity.HIGH,
        success: false,
        errorMessage: reason instanceof Error ? reason.message : String(reason),
        details: {
          unhandledRejection: true,
          reason: String(reason),
        },
      });
    });
  }

  private extractSecurityContext(event: IpcMainInvokeEvent, data: any): IPCSecurityContext {
    // Extract security context from the event and data
    const context: IPCSecurityContext = {
      ipAddress: 'localhost', // Electron apps are local
    };

    // Try to extract session information from data
    if (data && typeof data === 'object') {
      if (data.sessionId) context.sessionId = data.sessionId;
      if (data.userId) context.userId = data.userId;
      if (data.username) context.username = data.username;
    }

    return context;
  }

  private async handleRateLimitError(
    channel: string,
    context: IPCSecurityContext,
    error: RateLimitError
  ): Promise<void> {
    await intrusionDetection.recordActivity(
      'RATE_LIMIT_EXCEEDED',
      context.userId || context.sessionId || 'anonymous',
      {
        channel,
        retryAfter: error.retryAfter,
        ...context,
      },
      'MEDIUM'
    );

    await auditLogger.logSecurityEvent(
      AuditEventType.RATE_LIMIT_EXCEEDED,
      AuditSeverity.MEDIUM,
      {
        channel,
        retryAfter: error.retryAfter,
      },
      context.userId,
      error.message
    );
  }

  private async handleValidationError(
    channel: string,
    context: IPCSecurityContext,
    error: InputValidationError
  ): Promise<void> {
    await intrusionDetection.recordActivity(
      'VALIDATION_ERROR',
      context.userId || context.sessionId || 'anonymous',
      {
        channel,
        field: error.field,
        ...context,
      },
      'LOW'
    );

    await auditLogger.log({
      type: AuditEventType.IPC_VALIDATION_ERROR,
      severity: AuditSeverity.LOW,
      userId: context.userId,
      username: context.username,
      success: false,
      errorMessage: error.message,
      details: {
        channel,
        field: error.field,
      },
    });
  }

  private async handleSecurityError(
    channel: string,
    context: IPCSecurityContext,
    error: IPCSecurityError
  ): Promise<void> {
    const severity = error.statusCode >= 500 ? 'HIGH' : 'MEDIUM';

    await intrusionDetection.recordActivity(
      'SECURITY_VIOLATION',
      context.userId || context.sessionId || 'anonymous',
      {
        channel,
        errorCode: error.code,
        statusCode: error.statusCode,
        ...context,
      },
      severity
    );

    await auditLogger.logSecurityEvent(
      AuditEventType.SECURITY_VIOLATION,
      severity === 'HIGH' ? AuditSeverity.HIGH : AuditSeverity.MEDIUM,
      {
        channel,
        errorCode: error.code,
        statusCode: error.statusCode,
      },
      context.userId,
      error.message
    );
  }

  private async handleGenericError(
    channel: string,
    context: IPCSecurityContext,
    error: any
  ): Promise<void> {
    await auditLogger.log({
      type: AuditEventType.IPC_ERROR,
      severity: AuditSeverity.MEDIUM,
      userId: context.userId,
      username: context.username,
      success: false,
      errorMessage: error instanceof Error ? error.message : String(error),
      details: {
        channel,
        errorType: error.constructor?.name || 'Unknown',
      },
    });
  }

  private getErrorSeverity(error: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (error instanceof RateLimitError) return 'MEDIUM';
    if (error instanceof InputValidationError) return 'LOW';
    if (error instanceof IPCSecurityError) {
      return error.statusCode >= 500 ? 'HIGH' : 'MEDIUM';
    }
    return 'MEDIUM';
  }

  // Utility methods for monitoring
  public getRegisteredChannels(): string[] {
    return Array.from(this.handlers.keys());
  }

  public getAuthenticatedSessions(): string[] {
    return Array.from(this.authenticatedSessions);
  }

  public getSecurityStats(): {
    registeredHandlers: number;
    authenticatedSessions: number;
    rateLimitEntries: number;
  } {
    return {
      registeredHandlers: this.handlers.size,
      authenticatedSessions: this.authenticatedSessions.size,
      rateLimitEntries: rateLimiter.getActiveEntries(),
    };
  }

  // Method to perform security health check
  public performSecurityHealthCheck(): {
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    const config = securityConfig.getSecurityConfig();

    // Check if security features are enabled
    if (!config.enableAuditLogging) {
      issues.push('Audit logging is disabled');
      recommendations.push('Enable audit logging for security monitoring');
    }

    if (!config.enableIntrusionDetection) {
      issues.push('Intrusion detection is disabled');
      recommendations.push('Enable intrusion detection for threat monitoring');
    }

    // Check session security
    if (this.authenticatedSessions.size > 100) {
      issues.push('High number of authenticated sessions');
      recommendations.push('Review session management and cleanup policies');
    }

    // Determine overall status
    let status: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
    if (issues.length > 0) {
      status = issues.length > 3 ? 'CRITICAL' : 'WARNING';
    }

    return { status, issues, recommendations };
  }
}

// Export singleton instance
export const ipcSecurity = IPCSecurity.getInstance();
