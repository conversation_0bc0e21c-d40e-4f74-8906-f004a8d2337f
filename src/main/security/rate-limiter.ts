import { securityConfig } from './security-config';

export class RateLimitError extends Error {
  constructor(message: string, public retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstAttempt: number;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
}

export class RateLimiter {
  private static instance: RateLimiter;
  private limits: Map<string, RateLimitEntry> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  private constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  public static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.limits.clear();
  }

  // Default rate limit configurations
  private readonly configs: Record<string, RateLimitConfig> = {
    default: {
      windowMs: securityConfig.getSecurityConfig().rateLimitWindowMs,
      maxRequests: securityConfig.getSecurityConfig().rateLimitMaxRequests,
    },
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: securityConfig.getSecurityConfig().authRateLimitMaxRequests,
      skipSuccessfulRequests: false,
    },
    'auth:login': {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      skipSuccessfulRequests: false,
    },
    'auth:register': {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3,
      skipSuccessfulRequests: false,
    },
    'todo:create': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    },
    'todo:update': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20,
    },
    'todo:delete': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10,
    },
    'todo:search': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30,
    },
    'category:create': {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5,
    },
  };

  public async checkLimit(
    identifier: string, 
    channel: string, 
    success: boolean = true
  ): Promise<void> {
    const key = `${identifier}:${channel}`;
    const config = this.getConfigForChannel(channel);
    const now = Date.now();

    let entry = this.limits.get(key);

    if (!entry || now >= entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: now + config.windowMs,
        firstAttempt: now,
      };
    }

    // Check if we should count this request
    const shouldCount = !config.skipSuccessfulRequests || !success;

    if (shouldCount) {
      entry.count++;
    }

    // Update the entry
    this.limits.set(key, entry);

    // Check if limit is exceeded
    if (entry.count > config.maxRequests) {
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      throw new RateLimitError(
        `Rate limit exceeded for ${channel}. Try again in ${retryAfter} seconds.`,
        retryAfter
      );
    }
  }

  public async checkAuthLimit(identifier: string, success: boolean = false): Promise<void> {
    await this.checkLimit(identifier, 'auth:login', success);
  }

  public async checkIPCLimit(identifier: string, channel: string): Promise<void> {
    await this.checkLimit(identifier, channel);
  }

  public getRemainingRequests(identifier: string, channel: string): number {
    const key = `${identifier}:${channel}`;
    const config = this.getConfigForChannel(channel);
    const entry = this.limits.get(key);

    if (!entry || Date.now() >= entry.resetTime) {
      return config.maxRequests;
    }

    return Math.max(0, config.maxRequests - entry.count);
  }

  public getResetTime(identifier: string, channel: string): number {
    const key = `${identifier}:${channel}`;
    const entry = this.limits.get(key);

    if (!entry || Date.now() >= entry.resetTime) {
      return 0;
    }

    return entry.resetTime;
  }

  public resetLimit(identifier: string, channel?: string): void {
    if (channel) {
      const key = `${identifier}:${channel}`;
      this.limits.delete(key);
    } else {
      // Reset all limits for the identifier
      const keysToDelete = Array.from(this.limits.keys())
        .filter(key => key.startsWith(`${identifier}:`));
      
      keysToDelete.forEach(key => this.limits.delete(key));
    }
  }

  public getLimitStatus(identifier: string, channel: string): {
    remaining: number;
    resetTime: number;
    total: number;
  } {
    const config = this.getConfigForChannel(channel);
    
    return {
      remaining: this.getRemainingRequests(identifier, channel),
      resetTime: this.getResetTime(identifier, channel),
      total: config.maxRequests,
    };
  }

  private getConfigForChannel(channel: string): RateLimitConfig {
    // Try exact match first
    if (this.configs[channel]) {
      return this.configs[channel];
    }

    // Try prefix match (e.g., 'auth' for 'auth:login')
    const prefix = channel.split(':')[0];
    if (this.configs[prefix]) {
      return this.configs[prefix];
    }

    // Fall back to default
    return this.configs.default;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.limits.entries()) {
      if (now >= entry.resetTime) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.limits.delete(key));

    if (keysToDelete.length > 0) {
      console.log(`Rate limiter cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  // Utility methods for monitoring
  public getActiveEntries(): number {
    return this.limits.size;
  }

  public getEntriesForIdentifier(identifier: string): Array<{
    channel: string;
    count: number;
    resetTime: number;
    remaining: number;
  }> {
    const entries: Array<{
      channel: string;
      count: number;
      resetTime: number;
      remaining: number;
    }> = [];

    for (const [key, entry] of this.limits.entries()) {
      if (key.startsWith(`${identifier}:`)) {
        const channel = key.substring(identifier.length + 1);
        const config = this.getConfigForChannel(channel);
        
        entries.push({
          channel,
          count: entry.count,
          resetTime: entry.resetTime,
          remaining: Math.max(0, config.maxRequests - entry.count),
        });
      }
    }

    return entries;
  }

  // Method to temporarily increase limits for trusted operations
  public createTemporaryBypass(
    identifier: string, 
    channel: string, 
    durationMs: number = 60000
  ): () => void {
    const key = `${identifier}:${channel}`;
    const originalEntry = this.limits.get(key);
    
    // Remove the limit temporarily
    this.limits.delete(key);
    
    // Return a function to restore the original limit
    return () => {
      if (originalEntry) {
        this.limits.set(key, originalEntry);
      }
    };
  }
}

// Export singleton instance
export const rateLimiter = RateLimiter.getInstance();
