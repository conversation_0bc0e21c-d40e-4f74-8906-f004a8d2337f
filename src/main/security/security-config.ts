import { app } from 'electron';
import { config } from '../utils/config';

export interface SecurityConfig {
  // Authentication & Session Security
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  requireTwoFactor: boolean;
  enableBiometric: boolean;
  sessionSecret: string;

  // Password Policy
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSpecial: boolean;

  // Encryption
  encryptionEnabled: boolean;
  encryptionAlgorithm: string;
  encryptionKey: string;
  saltRounds: number;

  // Rate Limiting
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  authRateLimitMaxRequests: number;

  // Security Features
  enableAuditLogging: boolean;
  enableIntrusionDetection: boolean;
  enableSecurityHeaders: boolean;
  enableCSRFProtection: boolean;

  // Content Security Policy
  cspEnabled: boolean;
  cspReportOnly: boolean;
  allowedOrigins: string[];

  // Electron Security
  disableNodeIntegration: boolean;
  enableContextIsolation: boolean;
  enableSandbox: boolean;
  enableDevtools: boolean;

  // Monitoring
  auditLogLevel: string;
  auditLogFile: string;
  auditLogMaxSize: number;
  auditLogMaxFiles: number;
  securityLogFile: string;
  failedLoginThreshold: number;
  suspiciousActivityThreshold: number;

  // Production Settings
  exposeStackTraces: boolean;
  detailedErrorMessages: boolean;
  enableAutoUpdates: boolean;
}

export class SecurityConfigService {
  private static instance: SecurityConfigService;
  private securityConfig: SecurityConfig;

  private constructor() {
    this.securityConfig = this.loadSecurityConfig();
  }

  public static getInstance(): SecurityConfigService {
    if (!SecurityConfigService.instance) {
      SecurityConfigService.instance = new SecurityConfigService();
    }
    return SecurityConfigService.instance;
  }

  public getSecurityConfig(): SecurityConfig {
    return { ...this.securityConfig };
  }

  private loadSecurityConfig(): SecurityConfig {
    const isDevelopment = config.isDevelopment();
    
    return {
      // Authentication & Session Security
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
      maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
      lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
      requireTwoFactor: process.env.REQUIRE_2FA === 'true',
      enableBiometric: process.env.ENABLE_BIOMETRIC === 'true',
      sessionSecret: process.env.SESSION_SECRET || this.generateSecureSecret(),

      // Password Policy
      passwordMinLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '12'),
      passwordRequireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
      passwordRequireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
      passwordRequireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
      passwordRequireSpecial: process.env.PASSWORD_REQUIRE_SPECIAL !== 'false',

      // Encryption
      encryptionEnabled: process.env.ENCRYPTION_ENABLED === 'true',
      encryptionAlgorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
      encryptionKey: process.env.ENCRYPTION_KEY || this.generateEncryptionKey(),
      saltRounds: parseInt(process.env.SALT_ROUNDS || '12'),

      // Rate Limiting
      rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
      rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      authRateLimitMaxRequests: parseInt(process.env.AUTH_RATE_LIMIT_MAX_REQUESTS || '5'),

      // Security Features
      enableAuditLogging: process.env.ENABLE_AUDIT_LOGGING !== 'false',
      enableIntrusionDetection: process.env.ENABLE_INTRUSION_DETECTION !== 'false',
      enableSecurityHeaders: process.env.ENABLE_SECURITY_HEADERS !== 'false',
      enableCSRFProtection: process.env.ENABLE_CSRF_PROTECTION !== 'false',

      // Content Security Policy
      cspEnabled: process.env.CSP_ENABLED !== 'false',
      cspReportOnly: process.env.CSP_REPORT_ONLY === 'true' || isDevelopment,
      allowedOrigins: this.parseAllowedOrigins(),

      // Electron Security
      disableNodeIntegration: process.env.DISABLE_NODE_INTEGRATION !== 'false',
      enableContextIsolation: process.env.ENABLE_CONTEXT_ISOLATION !== 'false',
      enableSandbox: process.env.ENABLE_SANDBOX === 'true',
      enableDevtools: process.env.ENABLE_DEVTOOLS === 'true' || isDevelopment,

      // Monitoring
      auditLogLevel: process.env.AUDIT_LOG_LEVEL || 'info',
      auditLogFile: process.env.AUDIT_LOG_FILE || './logs/audit.log',
      auditLogMaxSize: parseInt(process.env.AUDIT_LOG_MAX_SIZE || '10485760'),
      auditLogMaxFiles: parseInt(process.env.AUDIT_LOG_MAX_FILES || '5'),
      securityLogFile: process.env.SECURITY_LOG_FILE || './logs/security.log',
      failedLoginThreshold: parseInt(process.env.FAILED_LOGIN_THRESHOLD || '3'),
      suspiciousActivityThreshold: parseInt(process.env.SUSPICIOUS_ACTIVITY_THRESHOLD || '10'),

      // Production Settings
      exposeStackTraces: process.env.EXPOSE_STACK_TRACES === 'true' || isDevelopment,
      detailedErrorMessages: process.env.DETAILED_ERROR_MESSAGES === 'true' || isDevelopment,
      enableAutoUpdates: process.env.ENABLE_AUTO_UPDATES !== 'false',
    };
  }

  private parseAllowedOrigins(): string[] {
    const origins = process.env.ALLOWED_ORIGINS || 'https://api.motherduck.com';
    const baseOrigins = origins.split(',').map(origin => origin.trim());
    
    // Add development origins if in development mode
    if (config.isDevelopment()) {
      baseOrigins.push('http://localhost:*', 'ws://localhost:*');
    }
    
    return baseOrigins;
  }

  private generateSecureSecret(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  private generateEncryptionKey(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  public validateSecurityConfig(): void {
    const errors: string[] = [];

    // Validate required security settings
    if (!this.securityConfig.sessionSecret || this.securityConfig.sessionSecret.length < 32) {
      errors.push('SESSION_SECRET must be at least 32 characters long');
    }

    if (this.securityConfig.encryptionEnabled && 
        (!this.securityConfig.encryptionKey || this.securityConfig.encryptionKey.length < 32)) {
      errors.push('ENCRYPTION_KEY must be at least 32 characters long when encryption is enabled');
    }

    if (this.securityConfig.passwordMinLength < 8) {
      errors.push('PASSWORD_MIN_LENGTH must be at least 8 characters');
    }

    if (this.securityConfig.maxLoginAttempts < 1) {
      errors.push('MAX_LOGIN_ATTEMPTS must be at least 1');
    }

    if (this.securityConfig.lockoutDuration < 60000) {
      errors.push('LOCKOUT_DURATION must be at least 60000ms (1 minute)');
    }

    if (errors.length > 0) {
      throw new Error(`Security configuration validation failed:\n${errors.join('\n')}`);
    }
  }

  public isProductionSecure(): boolean {
    if (this.isDevelopment()) {
      return true; // Skip production checks in development
    }

    const checks = [
      this.securityConfig.disableNodeIntegration,
      this.securityConfig.enableContextIsolation,
      this.securityConfig.enableAuditLogging,
      this.securityConfig.enableSecurityHeaders,
      !this.securityConfig.exposeStackTraces,
      !this.securityConfig.detailedErrorMessages,
      this.securityConfig.sessionSecret.length >= 32,
    ];

    return checks.every(check => check === true);
  }

  public isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  public isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }
}

// Export singleton instance
export const securityConfig = SecurityConfigService.getInstance();
