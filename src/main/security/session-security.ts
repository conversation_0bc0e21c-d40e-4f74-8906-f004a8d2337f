import * as crypto from 'crypto';
import { securityConfig } from './security-config';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';

export interface SecureSession {
  id: string;
  userId: string;
  username: string;
  createdAt: number;
  lastAccessedAt: number;
  expiresAt: number;
  ipAddress: string;
  userAgent?: string;
  isActive: boolean;
  deviceFingerprint?: string;
  encryptedData?: string;
}

export interface SessionData {
  userId: string;
  username: string;
  permissions?: string[];
  preferences?: Record<string, any>;
  metadata?: Record<string, any>;
}

export class SessionSecurityError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'SessionSecurityError';
  }
}

export class SessionSecurity {
  private static instance: SessionSecurity;
  private sessions: Map<string, SecureSession> = new Map();
  private cleanupInterval: NodeJS.Timeout;
  private encryptionKey: Buffer;

  private constructor() {
    const config = securityConfig.getSecurityConfig();
    this.encryptionKey = Buffer.from(config.encryptionKey, 'hex');
    
    // Clean up expired sessions every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000);
  }

  public static getInstance(): SessionSecurity {
    if (!SessionSecurity.instance) {
      SessionSecurity.instance = new SessionSecurity();
    }
    return SessionSecurity.instance;
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.sessions.clear();
  }

  public async createSession(
    sessionData: SessionData,
    ipAddress: string = 'localhost',
    userAgent?: string
  ): Promise<SecureSession> {
    const config = securityConfig.getSecurityConfig();
    const now = Date.now();
    
    const session: SecureSession = {
      id: this.generateSecureSessionId(),
      userId: sessionData.userId,
      username: sessionData.username,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt: now + config.sessionTimeout,
      ipAddress,
      userAgent,
      isActive: true,
      deviceFingerprint: this.generateDeviceFingerprint(userAgent, ipAddress),
      encryptedData: this.encryptSessionData(sessionData),
    };

    this.sessions.set(session.id, session);

    // Log session creation
    await auditLogger.log({
      type: AuditEventType.LOGIN_SUCCESS,
      severity: AuditSeverity.LOW,
      userId: sessionData.userId,
      username: sessionData.username,
      sessionId: session.id,
      success: true,
      details: {
        sessionCreated: true,
        expiresAt: session.expiresAt,
        deviceFingerprint: session.deviceFingerprint,
      },
    });

    return session;
  }

  public async validateSession(sessionId: string): Promise<SecureSession | null> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      return null;
    }

    const now = Date.now();

    // Check if session is expired
    if (now > session.expiresAt || !session.isActive) {
      await this.invalidateSession(sessionId, 'EXPIRED');
      return null;
    }

    // Update last accessed time
    session.lastAccessedAt = now;
    
    // Extend session if it's close to expiring (within 10% of timeout)
    const config = securityConfig.getSecurityConfig();
    const timeUntilExpiry = session.expiresAt - now;
    const extensionThreshold = config.sessionTimeout * 0.1;
    
    if (timeUntilExpiry < extensionThreshold) {
      session.expiresAt = now + config.sessionTimeout;
      
      await auditLogger.log({
        type: AuditEventType.ACCESS_GRANTED,
        severity: AuditSeverity.LOW,
        userId: session.userId,
        username: session.username,
        sessionId: session.id,
        success: true,
        details: {
          sessionExtended: true,
          newExpiresAt: session.expiresAt,
        },
      });
    }

    return session;
  }

  public async invalidateSession(
    sessionId: string,
    reason: 'LOGOUT' | 'EXPIRED' | 'SECURITY' | 'ADMIN' = 'LOGOUT'
  ): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (session) {
      session.isActive = false;
      this.sessions.delete(sessionId);

      await auditLogger.log({
        type: reason === 'LOGOUT' ? AuditEventType.LOGOUT : AuditEventType.ACCESS_DENIED,
        severity: reason === 'SECURITY' ? AuditSeverity.HIGH : AuditSeverity.LOW,
        userId: session.userId,
        username: session.username,
        sessionId: session.id,
        success: true,
        details: {
          invalidationReason: reason,
          sessionDuration: Date.now() - session.createdAt,
        },
      });
    }
  }

  public async invalidateAllUserSessions(
    userId: string,
    reason: 'SECURITY' | 'ADMIN' = 'SECURITY'
  ): Promise<number> {
    let invalidatedCount = 0;
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId && session.isActive) {
        await this.invalidateSession(sessionId, reason);
        invalidatedCount++;
      }
    }

    if (invalidatedCount > 0) {
      await auditLogger.log({
        type: AuditEventType.SECURITY_VIOLATION,
        severity: AuditSeverity.MEDIUM,
        userId,
        success: true,
        details: {
          action: 'INVALIDATE_ALL_SESSIONS',
          reason,
          sessionsInvalidated: invalidatedCount,
        },
      });
    }

    return invalidatedCount;
  }

  public getSessionData(sessionId: string): SessionData | null {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.isActive) {
      return null;
    }

    try {
      return this.decryptSessionData(session.encryptedData!);
    } catch (error) {
      console.error('Failed to decrypt session data:', error);
      return null;
    }
  }

  public async updateSessionData(sessionId: string, data: Partial<SessionData>): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.isActive) {
      return false;
    }

    try {
      const currentData = this.decryptSessionData(session.encryptedData!);
      const updatedData = { ...currentData, ...data };
      session.encryptedData = this.encryptSessionData(updatedData);
      
      await auditLogger.log({
        type: AuditEventType.DATA_UPDATE,
        severity: AuditSeverity.LOW,
        userId: session.userId,
        username: session.username,
        sessionId: session.id,
        success: true,
        details: {
          action: 'UPDATE_SESSION_DATA',
          updatedFields: Object.keys(data),
        },
      });

      return true;
    } catch (error) {
      console.error('Failed to update session data:', error);
      return false;
    }
  }

  public getUserSessions(userId: string): SecureSession[] {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId && session.isActive);
  }

  public getActiveSessions(): SecureSession[] {
    return Array.from(this.sessions.values())
      .filter(session => session.isActive);
  }

  public getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    averageSessionDuration: number;
  } {
    const allSessions = Array.from(this.sessions.values());
    const activeSessions = allSessions.filter(s => s.isActive);
    const now = Date.now();
    const expiredSessions = allSessions.filter(s => s.expiresAt < now);
    
    const totalDuration = allSessions.reduce((sum, session) => {
      const duration = session.isActive 
        ? now - session.createdAt 
        : session.lastAccessedAt - session.createdAt;
      return sum + duration;
    }, 0);

    return {
      totalSessions: allSessions.length,
      activeSessions: activeSessions.length,
      expiredSessions: expiredSessions.length,
      averageSessionDuration: allSessions.length > 0 ? totalDuration / allSessions.length : 0,
    };
  }

  private generateSecureSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private generateDeviceFingerprint(userAgent?: string, ipAddress?: string): string {
    const data = `${userAgent || 'unknown'}:${ipAddress || 'localhost'}:${Date.now()}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }

  private encryptSessionData(data: SessionData): string {
    const config = securityConfig.getSecurityConfig();
    
    if (!config.encryptionEnabled) {
      return JSON.stringify(data);
    }

    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(config.encryptionAlgorithm, this.encryptionKey, iv);

      let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      console.error('Failed to encrypt session data:', error);
      throw new SessionSecurityError('Encryption failed', 'ENCRYPTION_ERROR');
    }
  }

  private decryptSessionData(encryptedData: string): SessionData {
    const config = securityConfig.getSecurityConfig();
    
    if (!config.encryptionEnabled) {
      return JSON.parse(encryptedData);
    }

    try {
      const [ivHex, encrypted] = encryptedData.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv(config.encryptionAlgorithm, this.encryptionKey, iv);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Failed to decrypt session data:', error);
      throw new SessionSecurityError('Decryption failed', 'DECRYPTION_ERROR');
    }
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt || !session.isActive) {
        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  // Security monitoring methods
  public detectSuspiciousSessionActivity(): Array<{
    userId: string;
    suspiciousActivity: string;
    sessionCount: number;
  }> {
    const userSessionCounts = new Map<string, number>();
    const suspiciousUsers: Array<{
      userId: string;
      suspiciousActivity: string;
      sessionCount: number;
    }> = [];

    // Count sessions per user
    for (const session of this.sessions.values()) {
      if (session.isActive) {
        userSessionCounts.set(session.userId, (userSessionCounts.get(session.userId) || 0) + 1);
      }
    }

    // Check for users with too many concurrent sessions
    for (const [userId, count] of userSessionCounts.entries()) {
      if (count > 5) { // Configurable threshold
        suspiciousUsers.push({
          userId,
          suspiciousActivity: 'MULTIPLE_CONCURRENT_SESSIONS',
          sessionCount: count,
        });
      }
    }

    return suspiciousUsers;
  }
}

// Export singleton instance
export const sessionSecurity = SessionSecurity.getInstance();
