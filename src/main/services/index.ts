export { ConnectionPoolService, connectionPool } from './connection-pool.service';
export type { PooledConnection, PoolStats } from './connection-pool.service';

// Sync services
export { SyncEngine } from './sync-engine.service';
export type {
  SyncConfiguration,
  SyncOperation,
  Vector<PERSON>lock,
  ConflictInfo,
  SyncResult,
  SyncQueueStatus
} from './sync-engine.service';

export { ConflictResolver } from './conflict-resolver.service';
export type { ConflictResolution, ConflictResolverFunction } from './conflict-resolver.service';

export { NetworkDetector } from './network-detector.service';
export type { NetworkStatus, NetworkConfiguration } from './network-detector.service';

export { SyncManager, syncManager } from './sync-manager.service';
export type { SyncStatus, SyncManagerConfiguration } from './sync-manager.service';