import { EventEmitter } from 'events';
import * as dns from 'dns';
import * as https from 'https';

export interface NetworkStatus {
  isOnline: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  latency: number;
  lastChecked: Date;
  consecutiveFailures: number;
}

export interface NetworkConfiguration {
  checkInterval: number; // milliseconds
  timeout: number; // milliseconds
  maxRetries: number;
  testUrls: string[];
  latencyThresholds: {
    excellent: number;
    good: number;
    poor: number;
  };
}

export class NetworkDetector extends EventEmitter {
  private status: NetworkStatus = {
    isOnline: false,
    connectionQuality: 'offline',
    latency: 0,
    lastChecked: new Date(),
    consecutiveFailures: 0
  };

  private checkTimer: NodeJS.Timeout | null = null;
  private isChecking = false;

  private readonly defaultConfig: NetworkConfiguration = {
    checkInterval: 30000, // 30 seconds
    timeout: 5000, // 5 seconds
    maxRetries: 3,
    testUrls: [
      'https://www.google.com',
      'https://www.cloudflare.com',
      'https://www.github.com'
    ],
    latencyThresholds: {
      excellent: 100, // < 100ms
      good: 500,      // < 500ms
      poor: 2000      // < 2000ms
    }
  };

  constructor(private config: NetworkConfiguration = {} as NetworkConfiguration) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.startMonitoring();
  }

  public startMonitoring(): void {
    if (this.checkTimer) {
      return; // Already monitoring
    }

    // Initial check
    this.checkConnectivity();

    // Set up periodic checks
    this.checkTimer = setInterval(() => {
      this.checkConnectivity();
    }, this.config.checkInterval);

    console.log('Network monitoring started');
  }

  public stopMonitoring(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
    console.log('Network monitoring stopped');
  }

  public isOnline(): boolean {
    return this.status.isOnline;
  }

  public getStatus(): NetworkStatus {
    return { ...this.status };
  }

  public getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
    return this.status.connectionQuality;
  }

  public getLatency(): number {
    return this.status.latency;
  }

  private async checkConnectivity(): Promise<void> {
    if (this.isChecking) {
      return; // Avoid concurrent checks
    }

    this.isChecking = true;

    try {
      const startTime = Date.now();
      const isConnected = await this.performConnectivityTest();
      const endTime = Date.now();
      const latency = endTime - startTime;

      const wasOnline = this.status.isOnline;
      const previousQuality = this.status.connectionQuality;

      this.status.isOnline = isConnected;
      this.status.latency = latency;
      this.status.lastChecked = new Date();

      if (isConnected) {
        this.status.consecutiveFailures = 0;
        this.status.connectionQuality = this.calculateConnectionQuality(latency);
      } else {
        this.status.consecutiveFailures++;
        this.status.connectionQuality = 'offline';
      }

      // Emit events for status changes
      if (!wasOnline && isConnected) {
        console.log('Network connection established');
        this.emit('online', this.status);
      } else if (wasOnline && !isConnected) {
        console.log('Network connection lost');
        this.emit('offline', this.status);
      }

      if (previousQuality !== this.status.connectionQuality) {
        console.log(`Network quality changed: ${previousQuality} -> ${this.status.connectionQuality}`);
        this.emit('qualityChanged', this.status.connectionQuality, previousQuality);
      }

      this.emit('statusUpdate', this.status);

    } catch (error) {
      console.error('Network connectivity check failed:', error);
      this.status.consecutiveFailures++;
      this.status.isOnline = false;
      this.status.connectionQuality = 'offline';
      this.status.lastChecked = new Date();
    } finally {
      this.isChecking = false;
    }
  }

  private async performConnectivityTest(): Promise<boolean> {
    // Try multiple methods to determine connectivity
    const tests = [
      this.testDNSResolution(),
      this.testHTTPSConnection(),
      this.testMultipleEndpoints()
    ];

    try {
      // If any test passes, consider online
      const results = await Promise.allSettled(tests);
      return results.some(result => result.status === 'fulfilled' && result.value === true);
    } catch (error) {
      return false;
    }
  }

  private async testDNSResolution(): Promise<boolean> {
    return new Promise((resolve) => {
      dns.resolve('google.com', (err) => {
        resolve(!err);
      });
    });
  }

  private async testHTTPSConnection(): Promise<boolean> {
    return new Promise((resolve) => {
      const req = https.request({
        hostname: 'www.google.com',
        port: 443,
        path: '/',
        method: 'HEAD',
        timeout: this.config.timeout
      }, (res) => {
        resolve(res.statusCode !== undefined && res.statusCode < 400);
      });

      req.on('error', () => resolve(false));
      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });

      req.end();
    });
  }

  private async testMultipleEndpoints(): Promise<boolean> {
    const testPromises = this.config.testUrls.map(url => this.testSingleEndpoint(url));
    
    try {
      const results = await Promise.allSettled(testPromises);
      // Consider online if at least one endpoint is reachable
      return results.some(result => result.status === 'fulfilled' && result.value === true);
    } catch (error) {
      return false;
    }
  }

  private async testSingleEndpoint(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      const urlObj = new URL(url);
      
      const req = https.request({
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname,
        method: 'HEAD',
        timeout: this.config.timeout
      }, (res) => {
        resolve(res.statusCode !== undefined && res.statusCode < 400);
      });

      req.on('error', () => resolve(false));
      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });

      req.end();
    });
  }

  private calculateConnectionQuality(latency: number): 'excellent' | 'good' | 'poor' {
    if (latency < this.config.latencyThresholds.excellent) {
      return 'excellent';
    } else if (latency < this.config.latencyThresholds.good) {
      return 'good';
    } else {
      return 'poor';
    }
  }

  // Force an immediate connectivity check
  public async forceCheck(): Promise<NetworkStatus> {
    await this.checkConnectivity();
    return this.getStatus();
  }

  // Wait for network to come online
  public async waitForOnline(timeoutMs: number = 30000): Promise<boolean> {
    if (this.isOnline()) {
      return true;
    }

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.off('online', onlineHandler);
        resolve(false);
      }, timeoutMs);

      const onlineHandler = () => {
        clearTimeout(timeout);
        resolve(true);
      };

      this.once('online', onlineHandler);
    });
  }

  // Get network statistics
  public getStatistics(): {
    uptime: number;
    downtime: number;
    averageLatency: number;
    consecutiveFailures: number;
  } {
    // This would require tracking historical data
    // For now, return basic current stats
    return {
      uptime: this.status.isOnline ? 100 : 0,
      downtime: this.status.isOnline ? 0 : 100,
      averageLatency: this.status.latency,
      consecutiveFailures: this.status.consecutiveFailures
    };
  }

  // Check if connection is stable (no recent failures)
  public isStable(): boolean {
    return this.status.isOnline && this.status.consecutiveFailures === 0;
  }

  // Check if connection quality is good enough for sync operations
  public isGoodForSync(): boolean {
    return this.status.isOnline && 
           this.status.connectionQuality !== 'poor' && 
           this.status.consecutiveFailures < 2;
  }

  // Adaptive retry logic based on network quality
  public getRetryDelay(): number {
    const baseDelay = 1000; // 1 second

    switch (this.status.connectionQuality) {
      case 'excellent':
        return baseDelay;
      case 'good':
        return baseDelay * 2;
      case 'poor':
        return baseDelay * 5;
      case 'offline':
        return baseDelay * 10;
      default:
        return baseDelay;
    }
  }

  // Get recommended batch size based on connection quality
  public getRecommendedBatchSize(): number {
    switch (this.status.connectionQuality) {
      case 'excellent':
        return 50;
      case 'good':
        return 25;
      case 'poor':
        return 10;
      case 'offline':
        return 0;
      default:
        return 10;
    }
  }

  public destroy(): void {
    this.stopMonitoring();
    this.removeAllListeners();
  }
}
