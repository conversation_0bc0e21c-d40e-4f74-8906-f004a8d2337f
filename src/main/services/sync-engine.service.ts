import { EventEmitter } from 'events';
import { dbConnection } from '@main/database/connection';
import { mcpService } from '@main/mcp/service';
import { DatabaseError, QueryResult } from '@shared/types';
import { ConflictResolver } from './conflict-resolver.service';
import { NetworkDetector } from './network-detector.service';

export interface SyncConfiguration {
  syncInterval: number;
  batchSize: number;
  maxRetries: number;
  conflictResolutionStrategy: 'last-write-wins' | 'manual' | 'merge';
  enableRealTime: boolean;
  compressionEnabled: boolean;
  deltaSync: boolean;
}

export interface SyncOperation {
  id: string;
  operation: 'CREATE' | 'UPDATE' | 'DELETE';
  table: string;
  recordId: string;
  data: any;
  localTimestamp: Date;
  vectorClock: VectorClock;
  userId: string;
  dependencies: string[];
  priority: 'high' | 'medium' | 'low';
  retryCount: number;
  status: 'pending' | 'syncing' | 'synced' | 'failed' | 'conflict';
}

export interface VectorClock {
  [deviceId: string]: number;
}

export interface ConflictInfo {
  operation: SyncOperation | null;
  remoteData: any;
  reason: string;
  localData?: any;
  conflictType?: string;
  recommendations?: string[];
  tableName?: string;
  recordId?: string;
}

export interface SyncResult {
  success: boolean;
  processedOperations: number;
  conflicts: ConflictInfo[];
  errors: string[];
}

export interface SyncQueueStatus {
  totalOperations: number;
  pendingOperations: number;
  failedOperations: number;
  conflictOperations: number;
  isOnline: boolean;
  isSyncing: boolean;
}

export class SyncEngine {
  private syncTimer: NodeJS.Timeout | null = null;
  private operationQueue: SyncOperation[] = [];
  private syncInProgress = false;
  private deviceId: string;
  private vectorClock: VectorClock = {};
  private eventEmitter: EventEmitter;

  constructor(
    private config: SyncConfiguration,
    private conflictResolver: ConflictResolver,
    private networkDetector: NetworkDetector
  ) {
    this.eventEmitter = new EventEmitter();
    this.deviceId = this.generateDeviceId();
    this.initializeVectorClock();
    this.setupSyncScheduler();
    this.setupNetworkListeners();
  }

  private generateDeviceId(): string {
    // Generate a unique device ID based on machine characteristics
    const crypto = require('crypto');
    const os = require('os');
    const machineId = `${os.hostname()}-${os.platform()}-${os.arch()}`;
    return crypto.createHash('sha256').update(machineId).digest('hex').substring(0, 16);
  }

  private async initializeVectorClock(): Promise<void> {
    try {
      // Load vector clock from database
      const result = await dbConnection.executeQuery<{ vector_clock: string }>(
        'SELECT vector_clock FROM sync_state WHERE device_id = $1',
        [this.deviceId]
      );

      if (result.rows.length > 0) {
        this.vectorClock = JSON.parse(result.rows[0].vector_clock);
      } else {
        // Initialize new vector clock
        this.vectorClock[this.deviceId] = 0;
        await this.persistVectorClock();
      }
    } catch (error) {
      console.warn('Failed to load vector clock, using default:', error);
      this.vectorClock[this.deviceId] = 0;
    }
  }

  private async persistVectorClock(): Promise<void> {
    try {
      await dbConnection.executeQuery(
        `INSERT INTO sync_state (device_id, vector_clock, updated_at)
         VALUES ($1, $2, $3)
         ON CONFLICT (device_id)
         DO UPDATE SET vector_clock = $2, updated_at = $3`,
        [this.deviceId, JSON.stringify(this.vectorClock), new Date().toISOString()]
      );
    } catch (error) {
      console.error('Failed to persist vector clock:', error);
    }
  }

  private setupSyncScheduler(): void {
    if (this.config.syncInterval > 0) {
      this.syncTimer = setInterval(async () => {
        if (this.networkDetector.isOnline() && !this.syncInProgress) {
          await this.startSync();
        }
      }, this.config.syncInterval);
    }
  }

  private setupNetworkListeners(): void {
    this.networkDetector.on('online', () => {
      console.log('Network came online, triggering sync');
      this.scheduleSyncAttempt();
    });

    this.networkDetector.on('offline', () => {
      console.log('Network went offline, pausing sync');
    });
  }

  public async queueOperation(operation: Omit<SyncOperation, 'id' | 'vectorClock' | 'retryCount' | 'status'>): Promise<void> {
    // Increment vector clock for this device
    this.vectorClock[this.deviceId] = (this.vectorClock[this.deviceId] || 0) + 1;

    const syncOperation: SyncOperation = {
      ...operation,
      id: crypto.randomUUID(),
      vectorClock: { ...this.vectorClock },
      retryCount: 0,
      status: 'pending',
    };

    // Add to queue
    this.operationQueue.push(syncOperation);

    // Persist queue to database
    await this.persistOperationQueue();

    // Persist vector clock
    await this.persistVectorClock();

    // Emit event for UI updates
    this.eventEmitter.emit('sync:operationQueued', syncOperation);

    // Trigger sync if online
    if (this.networkDetector.isOnline()) {
      this.scheduleSyncAttempt();
    }
  }

  private async persistOperationQueue(): Promise<void> {
    try {
      // Clear existing queue for this device
      await dbConnection.executeQuery(
        'DELETE FROM sync_queue WHERE device_id = $1',
        [this.deviceId]
      );

      // Insert current queue
      for (const operation of this.operationQueue) {
        await dbConnection.executeQuery(
          `INSERT INTO sync_queue (
            device_id, operation_id, operation_type, table_name, record_id, 
            data, vector_clock, user_id, priority, retry_count, status, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)`,
          [
            this.deviceId,
            operation.id,
            operation.operation,
            operation.table,
            operation.recordId,
            JSON.stringify(operation.data),
            JSON.stringify(operation.vectorClock),
            operation.userId,
            operation.priority,
            operation.retryCount,
            operation.status
          ]
        );
      }
    } catch (error) {
      console.error('Failed to persist operation queue:', error);
    }
  }

  private async loadOperationQueue(): Promise<void> {
    try {
      const result = await dbConnection.executeQuery<any>(
        'SELECT * FROM sync_queue WHERE device_id = $1 ORDER BY created_at ASC',
        [this.deviceId]
      );

      this.operationQueue = result.rows.map(row => ({
        id: row.operation_id,
        operation: row.operation_type,
        table: row.table_name,
        recordId: row.record_id,
        data: JSON.parse(row.data),
        localTimestamp: new Date(row.created_at),
        vectorClock: JSON.parse(row.vector_clock),
        userId: row.user_id,
        dependencies: [], // TODO: Implement dependencies
        priority: row.priority,
        retryCount: row.retry_count,
        status: row.status
      }));
    } catch (error) {
      console.warn('Failed to load operation queue:', error);
      this.operationQueue = [];
    }
  }

  public async startSync(): Promise<SyncResult> {
    if (this.syncInProgress) {
      throw new Error('Sync already in progress');
    }

    if (!this.networkDetector.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    this.syncInProgress = true;
    this.eventEmitter.emit('sync:started');

    try {
      // Load operation queue from database
      await this.loadOperationQueue();

      // Push local changes to cloud
      const pushResult = await this.pushChangesToCloud();
      
      // Pull remote changes from cloud
      const pullResult = await this.pullChangesFromCloud();

      // Combine results
      const result: SyncResult = {
        success: pushResult.success && pullResult.success,
        processedOperations: pushResult.processedOperations + pullResult.processedOperations,
        conflicts: [...pushResult.conflicts, ...pullResult.conflicts],
        errors: [...pushResult.errors, ...pullResult.errors]
      };

      // Resolve conflicts if any
      if (result.conflicts.length > 0) {
        const conflictResult = await this.resolveConflicts(result.conflicts);
        result.errors.push(...conflictResult.errors);
      }

      this.eventEmitter.emit('sync:completed', result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';
      this.eventEmitter.emit('sync:error', error);
      
      return {
        success: false,
        processedOperations: 0,
        conflicts: [],
        errors: [errorMessage]
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  private async pushChangesToCloud(): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      processedOperations: 0,
      conflicts: [],
      errors: []
    };

    const pendingOperations = this.operationQueue.filter(op => op.status === 'pending');
    const batches = this.createBatches(pendingOperations, this.config.batchSize);

    for (const batch of batches) {
      try {
        await this.processBatch(batch);
        result.processedOperations += batch.length;
      } catch (error) {
        result.success = false;
        result.errors.push(`Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processBatch(operations: SyncOperation[]): Promise<void> {
    for (const operation of operations) {
      try {
        operation.status = 'syncing';
        await this.pushOperationToCloud(operation);
        operation.status = 'synced';
        
        // Update sync metadata
        await this.updateSyncMetadata(operation.table, operation.recordId, 'synced');
        
      } catch (error) {
        operation.retryCount++;
        if (operation.retryCount >= this.config.maxRetries) {
          operation.status = 'failed';
        } else {
          operation.status = 'pending';
        }
        throw error;
      }
    }

    // Remove synced operations from queue
    this.operationQueue = this.operationQueue.filter(op => op.status !== 'synced');
    await this.persistOperationQueue();
  }

  private async pushOperationToCloud(operation: SyncOperation): Promise<void> {
    try {
      switch (operation.operation) {
        case 'CREATE':
          await mcpService.insertData(operation.table, [operation.data]);
          break;
        case 'UPDATE':
          await mcpService.updateData(operation.table, operation.data, { id: operation.recordId });
          break;
        case 'DELETE':
          await mcpService.deleteData(operation.table, { id: operation.recordId });
          break;
      }
    } catch (error) {
      console.error(`Failed to push ${operation.operation} operation for ${operation.table}:`, error);
      throw error;
    }
  }

  private async pullChangesFromCloud(): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      processedOperations: 0,
      conflicts: [],
      errors: []
    };

    try {
      // Get last sync timestamp
      const lastSyncTimestamp = await this.getLastSyncTimestamp();
      
      // Pull changes for each table
      const tables = ['todos', 'categories', 'user_profiles'];
      
      for (const table of tables) {
        try {
          const remoteChanges = await this.getRemoteChanges(table, lastSyncTimestamp);
          
          for (const change of remoteChanges) {
            const conflictDetected = await this.detectConflict(change, table);
            
            if (conflictDetected) {
              const conflictInfo = await this.createDetailedConflictInfo(table, change);
              result.conflicts.push(conflictInfo);

              // Store conflict in database for UI resolution
              await this.storeConflictForManualResolution(conflictInfo);
            } else {
              await this.applyRemoteChange(table, change);
              result.processedOperations++;
            }
          }
        } catch (error) {
          result.errors.push(`Failed to pull changes for ${table}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Update last sync timestamp
      await this.updateLastSyncTimestamp(new Date());

    } catch (error) {
      result.success = false;
      result.errors.push(`Pull changes failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  private async getLastSyncTimestamp(): Promise<string> {
    try {
      const result = await dbConnection.executeQuery<{ last_sync_at: string }>(
        'SELECT last_sync_at FROM sync_state WHERE device_id = $1',
        [this.deviceId]
      );
      
      return result.rows[0]?.last_sync_at || '1970-01-01T00:00:00Z';
    } catch (error) {
      console.warn('Failed to get last sync timestamp:', error);
      return '1970-01-01T00:00:00Z';
    }
  }

  private async updateLastSyncTimestamp(timestamp: Date): Promise<void> {
    try {
      await dbConnection.executeQuery(
        `INSERT INTO sync_state (device_id, last_sync_at, updated_at)
         VALUES ($1, $2, $3)
         ON CONFLICT (device_id)
         DO UPDATE SET last_sync_at = $2, updated_at = $3`,
        [this.deviceId, timestamp.toISOString(), new Date().toISOString()]
      );
    } catch (error) {
      console.error('Failed to update last sync timestamp:', error);
    }
  }

  private async getRemoteChanges(table: string, since: string): Promise<any[]> {
    try {
      const query = `
        SELECT * FROM ${table} 
        WHERE updated_at > ? 
        ORDER BY updated_at ASC
      `;
      
      const result = await mcpService.executeQuery(query, [since]);
      return result.rows || [];
    } catch (error) {
      console.error(`Failed to get remote changes for ${table}:`, error);
      return [];
    }
  }

  private async detectConflict(remoteData: any, table: string): Promise<boolean> {
    try {
      // Check if there are local changes for this record
      const result = await dbConnection.executeQuery<{ sync_status: string, local_version: number, remote_version: number }>(
        'SELECT sync_status, local_version, remote_version FROM sync_metadata WHERE table_name = $1 AND record_id = $2',
        [table, remoteData.id]
      );

      if (result.rows.length === 0) {
        return false; // No local metadata, no conflict
      }

      const metadata = result.rows[0];

      // Check for pending local changes
      if (metadata.sync_status === 'pending') {
        return true;
      }

      // Check version conflicts
      if (remoteData.version && metadata.remote_version && remoteData.version <= metadata.remote_version) {
        return false; // Remote data is not newer
      }

      // Check for concurrent modifications using vector clocks if available
      if (remoteData.vector_clock && metadata.local_version) {
        // This would require more sophisticated vector clock comparison
        // For now, assume conflict if both have been modified
        return true;
      }

      return false;
    } catch (error) {
      console.warn('Failed to detect conflict:', error);
      return false;
    }
  }

  private async applyRemoteChange(table: string, change: any): Promise<void> {
    try {
      // Apply the change to local database
      await dbConnection.executeQuery(
        `INSERT INTO ${table} (${Object.keys(change).join(', ')}) 
         VALUES (${Object.keys(change).map((_, i) => `$${i + 1}`).join(', ')})
         ON CONFLICT (id) DO UPDATE SET 
         ${Object.keys(change).filter(k => k !== 'id').map((k, i) => `${k} = $${i + 2}`).join(', ')}`,
        Object.values(change)
      );

      // Update sync metadata
      await this.updateSyncMetadata(table, change.id, 'synced');
    } catch (error) {
      console.error(`Failed to apply remote change for ${table}:`, error);
      throw error;
    }
  }

  private async updateSyncMetadata(table: string, recordId: string, status: string): Promise<void> {
    try {
      await dbConnection.executeQuery(
        `INSERT INTO sync_metadata (table_name, record_id, sync_status, last_synced_at, updated_at)
         VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         ON CONFLICT (table_name, record_id)
         DO UPDATE SET sync_status = $3, last_synced_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP`,
        [table, recordId, status]
      );
    } catch (error) {
      console.error('Failed to update sync metadata:', error);
    }
  }

  private async resolveConflicts(conflicts: ConflictInfo[]): Promise<{ errors: string[] }> {
    const errors: string[] = [];

    for (const conflict of conflicts) {
      try {
        const resolution = await this.conflictResolver.resolve(conflict);
        
        if (resolution.strategy === 'manual') {
          // Store conflict for manual resolution
          await this.storeConflictForManualResolution(conflict);
          this.eventEmitter.emit('sync:conflictDetected', conflict);
        } else {
          // Apply automatic resolution
          await this.applyConflictResolution(resolution);
        }
      } catch (error) {
        errors.push(`Failed to resolve conflict: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { errors };
  }

  private async createDetailedConflictInfo(table: string, remoteData: any): Promise<ConflictInfo> {
    try {
      // Get local data for comparison
      const localResult = await dbConnection.executeQuery<any>(
        `SELECT * FROM ${table} WHERE id = $1`,
        [remoteData.id]
      );

      const localData = localResult.rows[0] || null;

      // Get conflict resolver recommendations
      const conflictType = this.conflictResolver.detectConflictType({
        operation: null,
        remoteData,
        reason: 'Remote change conflicts with local data'
      });

      const recommendations = this.conflictResolver.getResolutionRecommendations({
        operation: null,
        remoteData,
        reason: 'Remote change conflicts with local data'
      });

      return {
        operation: null,
        remoteData,
        reason: 'Local changes conflict with remote',
        localData,
        conflictType,
        recommendations,
        tableName: table,
        recordId: remoteData.id
      };
    } catch (error) {
      console.error('Failed to create detailed conflict info:', error);
      return {
        operation: null,
        remoteData,
        reason: 'Local changes conflict with remote'
      };
    }
  }

  private async storeConflictForManualResolution(conflict: ConflictInfo): Promise<void> {
    try {
      const table = conflict.tableName || conflict.operation?.table || conflict.remoteData.table;
      const recordId = conflict.recordId || conflict.operation?.recordId || conflict.remoteData.id;

      await dbConnection.executeQuery(
        `INSERT INTO sync_metadata (table_name, record_id, sync_status, conflict_data, created_at, updated_at)
         VALUES ($1, $2, 'conflict', $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         ON CONFLICT (table_name, record_id)
         DO UPDATE SET sync_status = 'conflict', conflict_data = $3, updated_at = CURRENT_TIMESTAMP`,
        [table, recordId, JSON.stringify(conflict)]
      );
    } catch (error) {
      console.error('Failed to store conflict:', error);
    }
  }

  private async applyConflictResolution(resolution: any): Promise<void> {
    // Implementation depends on resolution strategy
    console.log('Applying conflict resolution:', resolution);
  }

  private scheduleSyncAttempt(): void {
    // Debounce sync attempts
    setTimeout(() => {
      if (this.networkDetector.isOnline() && !this.syncInProgress) {
        this.startSync().catch(console.error);
      }
    }, 1000);
  }

  public async getQueueStatus(): Promise<SyncQueueStatus> {
    await this.loadOperationQueue();
    
    return {
      totalOperations: this.operationQueue.length,
      pendingOperations: this.operationQueue.filter(op => op.status === 'pending').length,
      failedOperations: this.operationQueue.filter(op => op.status === 'failed').length,
      conflictOperations: this.operationQueue.filter(op => op.status === 'conflict').length,
      isOnline: this.networkDetector.isOnline(),
      isSyncing: this.syncInProgress,
    };
  }

  public async clearQueue(): Promise<void> {
    this.operationQueue = [];
    await this.persistOperationQueue();
  }

  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  public async stop(): Promise<void> {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.eventEmitter.removeAllListeners();
  }
}
