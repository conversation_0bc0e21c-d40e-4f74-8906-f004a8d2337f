import { EventEmitter } from 'events';
import { SyncEngine, SyncConfiguration, SyncResult, SyncQueueStatus } from './sync-engine.service';
import { ConflictResolver } from './conflict-resolver.service';
import { NetworkDetector } from './network-detector.service';
import { mcpService } from '@main/mcp/service';
import { dbConnection } from '@main/database/connection';

export interface SyncStatus {
  status: 'idle' | 'syncing' | 'error' | 'offline';
  lastSyncAt: Date | null;
  pendingChanges: number;
  errorMessage?: string;
  syncProgress?: {
    current: number;
    total: number;
    operation: string;
  };
}

export interface SyncManagerConfiguration {
  autoSyncEnabled: boolean;
  syncInterval: number; // milliseconds
  conflictResolutionStrategy: 'last-write-wins' | 'manual' | 'merge';
  batchSize: number;
  maxRetries: number;
  enableRealTimeSync: boolean;
}

export class SyncManager extends EventEmitter {
  private syncEngine!: SyncEngine;
  private conflictResolver!: ConflictResolver;
  private networkDetector!: NetworkDetector;
  
  private syncStatus: SyncStatus = {
    status: 'idle',
    lastSyncAt: null,
    pendingChanges: 0
  };

  private autoSyncTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;

  private readonly defaultConfig: SyncManagerConfiguration = {
    autoSyncEnabled: true,
    syncInterval: 30000, // 30 seconds
    conflictResolutionStrategy: 'last-write-wins',
    batchSize: 25,
    maxRetries: 3,
    enableRealTimeSync: false
  };

  constructor(private config: SyncManagerConfiguration = {} as SyncManagerConfiguration) {
    super();
    this.config = { ...this.defaultConfig, ...config };
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing Sync Manager...');

      // Initialize network detector
      this.networkDetector = new NetworkDetector({
        checkInterval: 15000, // Check every 15 seconds
        timeout: 5000,
        maxRetries: 3,
        testUrls: ['https://www.google.com', 'https://api.motherduck.com'],
        latencyThresholds: {
          excellent: 100,
          good: 500,
          poor: 2000
        }
      });

      // Initialize conflict resolver
      this.conflictResolver = new ConflictResolver(this.config.conflictResolutionStrategy);

      // Initialize sync engine
      const syncConfig: SyncConfiguration = {
        syncInterval: this.config.syncInterval,
        batchSize: this.config.batchSize,
        maxRetries: this.config.maxRetries,
        conflictResolutionStrategy: this.config.conflictResolutionStrategy,
        enableRealTime: this.config.enableRealTimeSync,
        compressionEnabled: true,
        deltaSync: true
      };

      this.syncEngine = new SyncEngine(syncConfig, this.conflictResolver, this.networkDetector);

      // Set up event listeners
      this.setupEventListeners();

      // Create necessary database tables for sync
      await this.ensureSyncTables();

      // Start auto-sync if enabled
      if (this.config.autoSyncEnabled) {
        await this.startAutoSync();
      }

      // Update initial status
      await this.refreshSyncStatus();

      this.isInitialized = true;
      console.log('Sync Manager initialized successfully');

    } catch (error) {
      console.error('Failed to initialize Sync Manager:', error);
      throw error;
    }
  }

  private async ensureSyncTables(): Promise<void> {
    try {
      // Create sync_state table for device tracking
      await dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_state (
          device_id VARCHAR(32) PRIMARY KEY,
          vector_clock TEXT NOT NULL DEFAULT '{}',
          last_sync_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create sync_queue table for operation queuing
      await dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_queue (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          device_id VARCHAR(32) NOT NULL,
          operation_id VARCHAR(64) NOT NULL,
          operation_type VARCHAR(20) NOT NULL CHECK (operation_type IN ('CREATE', 'UPDATE', 'DELETE')),
          table_name VARCHAR(50) NOT NULL,
          record_id VARCHAR(64) NOT NULL,
          data TEXT NOT NULL,
          vector_clock TEXT NOT NULL,
          user_id VARCHAR(64) NOT NULL,
          priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
          retry_count INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'syncing', 'synced', 'failed', 'conflict')),
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create sync_metadata table for conflict tracking (if it doesn't exist from schema)
      await dbConnection.executeQuery(`
        CREATE TABLE IF NOT EXISTS sync_metadata (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          table_name VARCHAR(50) NOT NULL,
          record_id VARCHAR(64) NOT NULL,
          local_version INTEGER DEFAULT 1,
          remote_version INTEGER DEFAULT 0,
          sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error')),
          last_synced_at TIMESTAMPTZ,
          conflict_data TEXT,
          created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(table_name, record_id)
        )
      `);

      console.log('Sync tables ensured');
    } catch (error) {
      console.error('Failed to create sync tables:', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    // Network events
    this.networkDetector.on('online', () => {
      console.log('Network online - triggering sync');
      this.updateSyncStatus({ status: 'idle' });
      if (this.isInitialized) {
        this.performSync().catch(console.error);
      }
    });

    this.networkDetector.on('offline', () => {
      console.log('Network offline - pausing sync');
      this.updateSyncStatus({ status: 'offline' });
    });

    // Sync engine events
    this.syncEngine.on('sync:started', () => {
      this.updateSyncStatus({ status: 'syncing' });
      this.emit('syncStarted');
    });

    this.syncEngine.on('sync:completed', (result: SyncResult) => {
      this.updateSyncStatus({
        status: result.success ? 'idle' : 'error',
        lastSyncAt: new Date(),
        errorMessage: result.errors.length > 0 ? result.errors.join('; ') : undefined
      });
      this.emit('syncCompleted', result);
    });

    this.syncEngine.on('sync:error', (error: Error) => {
      this.updateSyncStatus({
        status: 'error',
        errorMessage: error.message
      });
      this.emit('syncError', error);
    });

    this.syncEngine.on('sync:conflictDetected', (conflict: any) => {
      this.emit('conflictDetected', conflict);
    });

    this.syncEngine.on('sync:operationQueued', (operation: any) => {
      this.updatePendingChangesCount();
      this.emit('operationQueued', operation);
    });
  }

  public async startAutoSync(): Promise<void> {
    if (this.autoSyncTimer) {
      return; // Already running
    }

    console.log(`Starting auto-sync with interval: ${this.config.syncInterval}ms`);
    
    this.autoSyncTimer = setInterval(async () => {
      if (this.networkDetector.isOnline() && this.syncStatus.status === 'idle') {
        await this.performSync();
      }
    }, this.config.syncInterval);

    // Perform initial sync after a delay to ensure everything is initialized
    if (this.networkDetector.isOnline()) {
      setTimeout(() => {
        if (this.isInitialized) {
          this.performSync().catch(console.error);
        }
      }, 2000);
    }
  }

  public stopAutoSync(): void {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer);
      this.autoSyncTimer = null;
      console.log('Auto-sync stopped');
    }
  }

  public async performSync(): Promise<SyncResult> {
    if (!this.isInitialized) {
      throw new Error('Sync Manager not initialized');
    }

    if (!this.networkDetector.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    if (!mcpService.isConnected()) {
      throw new Error('MCP service not connected');
    }

    try {
      console.log('Starting manual sync...');
      return await this.syncEngine.startSync();
    } catch (error) {
      console.error('Manual sync failed:', error);
      throw error;
    }
  }

  public async queueOperation(
    operation: 'CREATE' | 'UPDATE' | 'DELETE',
    table: string,
    recordId: string,
    data: any,
    userId: string,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Sync Manager not initialized');
    }

    await this.syncEngine.queueOperation({
      operation,
      table,
      recordId,
      data,
      localTimestamp: new Date(),
      userId,
      dependencies: [],
      priority
    });

    await this.updatePendingChangesCount();
  }

  public getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  public async getQueueStatus(): Promise<SyncQueueStatus> {
    if (!this.isInitialized) {
      return {
        totalOperations: 0,
        pendingOperations: 0,
        failedOperations: 0,
        conflictOperations: 0,
        isOnline: false,
        isSyncing: false
      };
    }

    return await this.syncEngine.getQueueStatus();
  }

  public isOnline(): boolean {
    return this.networkDetector?.isOnline() || false;
  }

  public getNetworkStatus() {
    return this.networkDetector?.getStatus();
  }

  private updateSyncStatus(updates: Partial<SyncStatus>): void {
    this.syncStatus = { ...this.syncStatus, ...updates };
    this.emit('statusChanged', this.syncStatus);
  }

  private async updatePendingChangesCount(): Promise<void> {
    try {
      const queueStatus = await this.getQueueStatus();
      this.updateSyncStatus({ pendingChanges: queueStatus.pendingOperations });
    } catch (error) {
      console.warn('Failed to update pending changes count:', error);
    }
  }

  private async refreshSyncStatus(): Promise<void> {
    try {
      const queueStatus = await this.getQueueStatus();
      const networkStatus = this.networkDetector.getStatus();

      this.updateSyncStatus({
        status: networkStatus.isOnline ? 'idle' : 'offline',
        pendingChanges: queueStatus.pendingOperations
      });
    } catch (error) {
      console.warn('Failed to update sync status:', error);
    }
  }

  // Force push all pending changes
  public async forcePushChanges(): Promise<SyncResult> {
    console.log('Force pushing all pending changes...');
    return await this.performSync();
  }

  // Force pull all remote changes
  public async forcePullChanges(): Promise<SyncResult> {
    console.log('Force pulling all remote changes...');
    // This would be implemented by the sync engine
    return await this.performSync();
  }

  // Clear all pending operations (use with caution)
  public async clearQueue(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    await this.syncEngine.clearQueue();
    await this.updatePendingChangesCount();
    console.log('Sync queue cleared');
  }

  // Get conflicts that require manual resolution
  public async getConflicts(): Promise<any[]> {
    try {
      const result = await dbConnection.executeQuery<any>(
        'SELECT * FROM sync_metadata WHERE sync_status = $1',
        ['conflict']
      );
      return result.rows;
    } catch (error) {
      console.error('Failed to get conflicts:', error);
      return [];
    }
  }

  // Resolve a specific conflict
  public async resolveConflict(conflictId: string, resolution: 'local' | 'remote' | 'merge', mergedData?: any): Promise<void> {
    try {
      // Implementation would depend on the specific conflict resolution logic
      console.log(`Resolving conflict ${conflictId} with strategy: ${resolution}`);
      
      // Update sync metadata to mark conflict as resolved
      await dbConnection.executeQuery(
        'UPDATE sync_metadata SET sync_status = $1, conflict_data = NULL WHERE id = $2',
        ['synced', conflictId]
      );

      this.emit('conflictResolved', { conflictId, resolution });
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      throw error;
    }
  }

  public async destroy(): Promise<void> {
    console.log('Destroying Sync Manager...');
    
    this.stopAutoSync();
    
    if (this.syncEngine) {
      await this.syncEngine.stop();
    }
    
    if (this.networkDetector) {
      this.networkDetector.destroy();
    }
    
    this.removeAllListeners();
    this.isInitialized = false;
    
    console.log('Sync Manager destroyed');
  }
}

// Export singleton instance
export const syncManager = new SyncManager();
