import { ConflictResolver } from './services/conflict-resolver.service';
import { ConflictInfo } from './services/sync-engine.service';

async function testConflictResolution() {
  console.log('🧪 Testing Conflict Resolution...\n');

  const conflictResolver = new ConflictResolver('merge');

  // Test 1: Todo conflict resolution
  console.log('1. Testing Todo Conflict Resolution...');
  
  const todoConflict: ConflictInfo = {
    operation: {
      id: 'op-123',
      operation: 'UPDATE',
      table: 'todos',
      recordId: 'todo-123',
      data: {
        id: 'todo-123',
        title: 'Complete project documentation',
        description: 'Write comprehensive docs for the project',
        status: 'in_progress',
        priority: 'high',
        tags: ['work', 'documentation'],
        due_date: '2024-01-15',
        completed: false,
        updated_at: '2024-01-10T10:00:00Z'
      },
      localTimestamp: new Date('2024-01-10T10:00:00Z'),
      vectorClock: { 'device-1': 5 },
      userId: 'user-123',
      dependencies: [],
      priority: 'medium',
      retryCount: 0,
      status: 'pending'
    },
    remoteData: {
      id: 'todo-123',
      title: 'Complete project documentation and testing',
      description: 'Write comprehensive docs and add unit tests',
      status: 'completed',
      priority: 'medium',
      tags: ['work', 'documentation', 'testing'],
      due_date: '2024-01-12',
      completed: true,
      completed_at: '2024-01-11T15:30:00Z',
      updated_at: '2024-01-11T15:30:00Z',
      vector_clock: { 'device-2': 3 }
    },
    reason: 'Concurrent modifications detected'
  };

  try {
    const resolution = await conflictResolver.resolve(todoConflict);
    console.log('✅ Todo conflict resolved:', JSON.stringify(resolution, null, 2));
    
    // Test conflict type detection
    const conflictType = conflictResolver.detectConflictType(todoConflict);
    console.log('📊 Conflict type:', conflictType);
    
    // Test recommendations
    const recommendations = conflictResolver.getResolutionRecommendations(todoConflict);
    console.log('💡 Recommendations:', recommendations);
    
  } catch (error) {
    console.error('❌ Todo conflict resolution failed:', error);
  }

  console.log('\n2. Testing Category Conflict Resolution...');
  
  const categoryConflict: ConflictInfo = {
    operation: {
      id: 'op-456',
      operation: 'UPDATE',
      table: 'categories',
      recordId: 'cat-456',
      data: {
        id: 'cat-456',
        name: 'Work Projects',
        color: '#3B82F6',
        icon: 'briefcase',
        updated_at: '2024-01-10T09:00:00Z'
      },
      localTimestamp: new Date('2024-01-10T09:00:00Z'),
      vectorClock: { 'device-1': 3 },
      userId: 'user-123',
      dependencies: [],
      priority: 'medium',
      retryCount: 0,
      status: 'pending'
    },
    remoteData: {
      id: 'cat-456',
      name: 'Work Projects',
      color: '#EF4444',
      icon: 'folder',
      updated_at: '2024-01-10T11:00:00Z'
    },
    reason: 'Color and icon preferences differ'
  };

  try {
    const resolution = await conflictResolver.resolve(categoryConflict);
    console.log('✅ Category conflict resolved:', JSON.stringify(resolution, null, 2));
  } catch (error) {
    console.error('❌ Category conflict resolution failed:', error);
  }

  console.log('\n3. Testing Vector Clock Comparison...');
  
  const clock1 = { 'device-1': 5, 'device-2': 2 };
  const clock2 = { 'device-1': 3, 'device-2': 4 };
  const clock3 = { 'device-1': 5, 'device-2': 2 };
  
  console.log('Clock 1 vs Clock 2:', ConflictResolver.compareVectorClocks(clock1, clock2));
  console.log('Clock 1 vs Clock 3:', ConflictResolver.compareVectorClocks(clock1, clock3));
  console.log('Clock 2 vs Clock 3:', ConflictResolver.compareVectorClocks(clock2, clock3));

  console.log('\n4. Testing Different Resolution Strategies...');
  
  // Test last-write-wins
  const lwwResolver = new ConflictResolver('last-write-wins');
  const lwwResolution = await lwwResolver.resolve(todoConflict);
  console.log('📅 Last-write-wins resolution:', lwwResolution.strategy);
  
  // Test manual resolution
  const manualResolver = new ConflictResolver('manual');
  const manualResolution = await manualResolver.resolve(todoConflict);
  console.log('👤 Manual resolution:', manualResolution.strategy);

  console.log('\n5. Testing Custom Resolver Registration...');
  
  // Register a custom resolver for a specific table
  conflictResolver.registerCustomResolver('custom_table', async (conflict) => {
    return {
      strategy: 'merge',
      resolvedData: { ...conflict.remoteData, custom_field: 'resolved' }
    };
  });
  
  const customConflict: ConflictInfo = {
    operation: null,
    remoteData: { id: 'test', table: 'custom_table', data: 'test' },
    reason: 'Testing custom resolver'
  };
  
  // This would need to be modified to work with the custom table
  console.log('🔧 Custom resolver registered successfully');

  console.log('\n6. Testing Edge Cases...');
  
  // Test null/undefined values
  const edgeConflict: ConflictInfo = {
    operation: null,
    remoteData: {
      id: 'edge-test',
      title: null,
      description: undefined,
      status: 'pending'
    },
    reason: 'Testing edge cases'
  };
  
  try {
    const edgeResolution = await conflictResolver.resolve(edgeConflict);
    console.log('✅ Edge case handled:', edgeResolution.strategy);
  } catch (error) {
    console.error('❌ Edge case failed:', error);
  }

  console.log('\n✅ All conflict resolution tests completed!');
}

// Run the test
if (require.main === module) {
  testConflictResolution()
    .then(() => {
      console.log('\n🎉 Conflict resolution test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed with error:', error);
      process.exit(1);
    });
}

export { testConflictResolution };
