#!/usr/bin/env node

/**
 * Test script to verify offline-first data strategy implementation
 * This script tests the complete offline-first functionality
 */

import { offlineFirstDataService } from './services/offline-first-data.service';
import { todoDAO } from './dao/todo.dao';
import { DatabaseConnection } from './database/connection';
import { MCPService } from './mcp/service';
import { NetworkDetector } from './services/network-detector.service';
import log from 'electron-log';

// Configure logging for testing
log.transports.console.level = 'debug';
log.transports.file.level = 'debug';

class OfflineFirstStrategyTest {
  private databaseService: DatabaseConnection | null = null;
  private mcpService: MCPService | null = null;
  private networkDetector: NetworkDetector | null = null;

  async initialize(): Promise<void> {
    try {
      console.log('🔧 Initializing offline-first test environment...');

      // Initialize database
      this.databaseService = DatabaseConnection.getInstance();
      await this.databaseService.initialize();
      console.log('✅ Database initialized');

      // Initialize MCP service
      this.mcpService = MCPService.getInstance();
      await this.mcpService.initialize();
      console.log('✅ MCP service initialized');

      // Initialize network detector
      this.networkDetector = new NetworkDetector();
      console.log('✅ Network detector initialized');

      // Initialize offline-first data service
      await offlineFirstDataService.initialize();
      console.log('✅ Offline-first data service initialized');

    } catch (error) {
      console.error('❌ Failed to initialize test environment:', error);
      throw error;
    }
  }

  async testOfflineCapabilities(): Promise<void> {
    console.log('\n🧪 Testing offline capabilities...');

    // Test offline status
    const offlineStatus = offlineFirstDataService.getOfflineStatus();
    console.log('📊 Offline status:', offlineStatus);

    if (!offlineStatus.isOfflineCapable) {
      console.log('⚠️  Application is not offline capable');
      return;
    }

    console.log('✅ Application is offline capable');

    // Test cache functionality
    console.log('\n📦 Testing cache functionality...');
    const cacheStats = offlineFirstDataService.getCacheStats();
    console.log('Cache stats:', cacheStats);

    // Clear cache and test
    offlineFirstDataService.clearCache();
    const clearedCacheStats = offlineFirstDataService.getCacheStats();
    console.log('Cache stats after clear:', clearedCacheStats);

    if (clearedCacheStats.totalEntries === 0) {
      console.log('✅ Cache clearing works correctly');
    }
  }

  async testOfflineFirstDataOperations(): Promise<void> {
    console.log('\n🗄️  Testing offline-first data operations...');

    const testUserId = 'test-user-offline-first';

    try {
      // Test data retrieval with caching
      console.log('📖 Testing data retrieval with caching...');
      
      const query = 'SELECT * FROM todos WHERE user_id = $1 LIMIT 5';
      const params = [testUserId];

      // First call - should hit database
      const result1 = await offlineFirstDataService.getData(
        'todos',
        query,
        params,
        { useCache: true }
      );
      console.log(`First call returned ${result1.rows.length} rows`);

      // Second call - should hit cache
      const result2 = await offlineFirstDataService.getData(
        'todos',
        query,
        params,
        { useCache: true }
      );
      console.log(`Second call returned ${result2.rows.length} rows`);

      console.log('✅ Data retrieval with caching works');

    } catch (error) {
      console.log('⚠️  Data retrieval test failed (expected in test mode):', (error as Error).message);
    }
  }

  async testOfflineFirstTodoOperations(): Promise<void> {
    console.log('\n📝 Testing offline-first todo operations...');

    const testUserId = 'test-user-offline-first';
    const testTodoData = {
      title: 'Test Offline-First Todo',
      description: 'Testing offline-first data strategy',
      priority: 'high' as const,
      status: 'pending' as const
    };

    try {
      // Test offline-first todo creation
      console.log('➕ Testing offline-first todo creation...');
      
      const createdTodo = await todoDAO.createTodoOfflineFirst(
        testTodoData,
        testUserId,
        { enableOptimisticUpdates: true, priority: 'high' }
      );
      
      console.log('✅ Todo created with offline-first strategy:', createdTodo.id);

      // Test offline-first todo retrieval
      console.log('🔍 Testing offline-first todo retrieval...');
      
      const retrievedTodo = await todoDAO.findByIdForUserOfflineFirst(
        createdTodo.id,
        testUserId,
        { useCache: true }
      );
      
      if (retrievedTodo) {
        console.log('✅ Todo retrieved with offline-first strategy');
      } else {
        console.log('⚠️  Todo not found');
      }

      // Test offline-first todo update
      console.log('✏️  Testing offline-first todo update...');
      
      const updatedTodo = await todoDAO.updateOfflineFirst(
        createdTodo.id,
        { title: 'Updated Offline-First Todo' },
        testUserId,
        { enableOptimisticUpdates: true }
      );
      
      console.log('✅ Todo updated with offline-first strategy');

      // Test offline-first status update
      console.log('🔄 Testing offline-first status update...');
      
      const statusUpdatedTodo = await todoDAO.updateStatusOfflineFirst(
        createdTodo.id,
        'completed',
        testUserId,
        { enableOptimisticUpdates: true, priority: 'high' }
      );
      
      console.log('✅ Todo status updated with offline-first strategy');

      // Test offline-first todo deletion
      console.log('🗑️  Testing offline-first todo deletion...');
      
      const deletedTodo = await todoDAO.softDeleteTodoOfflineFirst(
        createdTodo.id,
        testUserId,
        { enableOptimisticUpdates: true, priority: 'high' }
      );
      
      console.log('✅ Todo deleted with offline-first strategy');

    } catch (error) {
      console.log('⚠️  Offline-first todo operations test failed:', (error as Error).message);
    }
  }

  async testOfflineFirstPagination(): Promise<void> {
    console.log('\n📄 Testing offline-first pagination...');

    const testUserId = 'test-user-offline-first';

    try {
      const result = await todoDAO.findByUserIdOfflineFirst(
        testUserId,
        { page: 1, limit: 10 },
        { status: 'pending' },
        { useCache: true }
      );

      console.log(`📊 Pagination result: ${result.todos.length} todos, total: ${result.total}, hasMore: ${result.hasMore}`);
      console.log('✅ Offline-first pagination works');

    } catch (error) {
      console.log('⚠️  Pagination test failed:', (error as Error).message);
    }
  }

  async testNetworkStateHandling(): Promise<void> {
    console.log('\n🌐 Testing network state handling...');

    if (!this.networkDetector) {
      console.log('⚠️  Network detector not available');
      return;
    }

    const networkStatus = this.networkDetector.getStatus();
    console.log('📡 Network status:', networkStatus);

    console.log(`📶 Connection quality: ${networkStatus.connectionQuality}`);
    console.log(`⚡ Latency: ${networkStatus.latency}ms`);
    console.log(`🌐 Online: ${networkStatus.isOnline}`);

    if (networkStatus.isOnline) {
      console.log('✅ Network state handling works - currently online');
    } else {
      console.log('📴 Network state handling works - currently offline');
    }
  }

  async testPendingOperationsQueue(): Promise<void> {
    console.log('\n📋 Testing pending operations queue...');

    try {
      // Get current queue status
      const queueStatus = offlineFirstDataService.getOfflineStatus();
      console.log(`📊 Current pending operations: ${queueStatus.pendingOperations}`);

      // Test force sync (if online)
      if (queueStatus.isOnline && queueStatus.pendingOperations > 0) {
        console.log('🔄 Testing force sync of pending operations...');
        await offlineFirstDataService.forceSyncPendingOperations();
        console.log('✅ Force sync completed');
      } else {
        console.log('⚠️  No pending operations to sync or offline');
      }

    } catch (error) {
      console.log('⚠️  Pending operations test failed:', (error as Error).message);
    }
  }

  async testOfflineFirstStrategy(): Promise<void> {
    console.log('\n🎯 Testing complete offline-first strategy...');

    // Test the core offline-first principles:
    // 1. Local-first data access
    // 2. Optimistic updates
    // 3. Background synchronization
    // 4. Graceful degradation

    const principles = [
      {
        name: 'Local-first data access',
        test: () => offlineFirstDataService.isOfflineCapable(),
        expected: true
      },
      {
        name: 'Cache functionality',
        test: () => {
          const stats = offlineFirstDataService.getCacheStats();
          return typeof stats.totalEntries === 'number';
        },
        expected: true
      },
      {
        name: 'Offline status monitoring',
        test: () => {
          const status = offlineFirstDataService.getOfflineStatus();
          return typeof status.isOfflineCapable === 'boolean';
        },
        expected: true
      }
    ];

    let passedTests = 0;
    for (const principle of principles) {
      try {
        const result = principle.test();
        if (result === principle.expected) {
          console.log(`✅ ${principle.name}: PASSED`);
          passedTests++;
        } else {
          console.log(`❌ ${principle.name}: FAILED (expected ${principle.expected}, got ${result})`);
        }
      } catch (error) {
        console.log(`❌ ${principle.name}: ERROR - ${(error as Error).message}`);
      }
    }

    console.log(`\n📊 Offline-first strategy test results: ${passedTests}/${principles.length} tests passed`);

    if (passedTests === principles.length) {
      console.log('🎉 All offline-first strategy tests passed!');
    } else {
      console.log('⚠️  Some offline-first strategy tests failed');
    }
  }

  async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up test environment...');
    
    try {
      await offlineFirstDataService.destroy();
      this.networkDetector?.stopMonitoring();
      await this.databaseService?.close();
      await this.mcpService?.disconnect();
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup error:', error);
    }
  }

  async run(): Promise<void> {
    try {
      console.log('🚀 Starting Offline-First Data Strategy Test\n');
      
      await this.initialize();
      await this.testOfflineCapabilities();
      await this.testOfflineFirstDataOperations();
      await this.testOfflineFirstTodoOperations();
      await this.testOfflineFirstPagination();
      await this.testNetworkStateHandling();
      await this.testPendingOperationsQueue();
      await this.testOfflineFirstStrategy();
      
      console.log('\n🎉 All offline-first tests completed!');
      
    } catch (error) {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new OfflineFirstStrategyTest();
  test.run().then(() => {
    console.log('\n✨ Offline-first strategy test execution finished');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Test execution failed:', error);
    process.exit(1);
  });
}

export { OfflineFirstStrategyTest };
