#!/usr/bin/env node

/**
 * Test script to verify sync status monitoring functionality
 * This script tests the real-time event forwarding and sync status updates
 */

import { syncManager } from './services/sync-manager.service';
import { DatabaseConnection } from './database/connection';
import { MCPService } from './mcp/service';
import log from 'electron-log';

// Configure logging for testing
log.transports.console.level = 'debug';
log.transports.file.level = 'debug';

class SyncStatusMonitoringTest {
  private databaseService: DatabaseConnection | null = null;
  private mcpService: MCPService | null = null;

  async initialize(): Promise<void> {
    try {
      console.log('🔧 Initializing test environment...');

      // Initialize database
      this.databaseService = DatabaseConnection.getInstance();
      await this.databaseService.initialize();
      console.log('✅ Database initialized');

      // Initialize MCP service
      this.mcpService = MCPService.getInstance();
      await this.mcpService.initialize();
      console.log('✅ MCP service initialized');

      // Initialize sync manager
      if (this.mcpService.isConnected()) {
        await syncManager.initialize();
        console.log('✅ Sync manager initialized');
      } else {
        console.log('⚠️  MCP service not connected, using mock mode');
        await syncManager.initialize();
      }

    } catch (error) {
      console.error('❌ Failed to initialize test environment:', error);
      throw error;
    }
  }

  async testEventEmission(): Promise<void> {
    console.log('\n🧪 Testing sync event emission...');

    // Set up event listeners to verify events are emitted
    const events: Array<{ type: string; data?: any }> = [];

    syncManager.on('statusChanged', (status) => {
      events.push({ type: 'statusChanged', data: status });
      console.log('📡 Received statusChanged event:', status);
    });

    syncManager.on('syncStarted', () => {
      events.push({ type: 'syncStarted' });
      console.log('📡 Received syncStarted event');
    });

    syncManager.on('syncCompleted', (result) => {
      events.push({ type: 'syncCompleted', data: result });
      console.log('📡 Received syncCompleted event:', result);
    });

    syncManager.on('syncError', (error) => {
      events.push({ type: 'syncError', data: error });
      console.log('📡 Received syncError event:', error.message);
    });

    syncManager.on('operationQueued', (operation) => {
      events.push({ type: 'operationQueued', data: operation });
      console.log('📡 Received operationQueued event:', operation);
    });

    // Test sync status retrieval
    console.log('\n📊 Testing sync status retrieval...');
    const syncStatus = syncManager.getSyncStatus();
    console.log('Current sync status:', syncStatus);

    const queueStatus = await syncManager.getQueueStatus();
    console.log('Current queue status:', queueStatus);

    const networkStatus = syncManager.getNetworkStatus();
    console.log('Current network status:', networkStatus);

    // Test operation queuing (this should trigger events)
    console.log('\n⚡ Testing operation queuing...');
    try {
      await syncManager.queueOperation(
        'CREATE',
        'todos',
        'test-todo-' + Date.now(),
        { title: 'Test Todo', description: 'Test sync monitoring' },
        'test-user-id',
        'high'
      );
      console.log('✅ Operation queued successfully');
    } catch (error) {
      console.log('⚠️  Operation queuing failed (expected in test mode):', (error as Error).message);
    }

    // Wait a moment for events to be processed
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test manual sync (this should trigger sync events)
    console.log('\n🔄 Testing manual sync...');
    try {
      const syncResult = await syncManager.performSync();
      console.log('✅ Manual sync completed:', syncResult);
    } catch (error) {
      console.log('⚠️  Manual sync failed (expected in test mode):', (error as Error).message);
    }

    // Wait for events to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n📈 Event Summary:');
    console.log(`Total events received: ${events.length}`);
    events.forEach((event, index) => {
      console.log(`  ${index + 1}. ${event.type}${event.data ? ' (with data)' : ''}`);
    });

    if (events.length > 0) {
      console.log('✅ Event emission test passed - events are being emitted correctly');
    } else {
      console.log('⚠️  Event emission test warning - no events received (may be expected in test mode)');
    }
  }

  async testStatusMonitoring(): Promise<void> {
    console.log('\n🔍 Testing status monitoring capabilities...');

    // Test all status retrieval methods
    const tests = [
      { name: 'Sync Status', method: () => syncManager.getSyncStatus() },
      { name: 'Queue Status', method: () => syncManager.getQueueStatus() },
      { name: 'Network Status', method: () => syncManager.getNetworkStatus() },
      { name: 'Online Status', method: () => syncManager.isOnline() },
    ];

    for (const test of tests) {
      try {
        const result = await test.method();
        console.log(`✅ ${test.name}:`, result);
      } catch (error) {
        console.log(`❌ ${test.name} failed:`, (error as Error).message);
      }
    }
  }

  async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up test environment...');
    
    try {
      await syncManager.destroy();
      await this.databaseService?.close();
      await this.mcpService?.disconnect();
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup error:', error);
    }
  }

  async run(): Promise<void> {
    try {
      console.log('🚀 Starting Sync Status Monitoring Test\n');
      
      await this.initialize();
      await this.testEventEmission();
      await this.testStatusMonitoring();
      
      console.log('\n🎉 All tests completed successfully!');
      
    } catch (error) {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new SyncStatusMonitoringTest();
  test.run().then(() => {
    console.log('\n✨ Test execution finished');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 Test execution failed:', error);
    process.exit(1);
  });
}

export { SyncStatusMonitoringTest };
