// Import app from electron
import { app } from 'electron';

export const isDev = (): boolean => {
  return process.env.NODE_ENV === 'development' || !app.isPackaged;
};

export const getAppPath = (): string => {
  return app.getAppPath();
};

export const getUserDataPath = (): string => {
  return app.getPath('userData');
};

export const getLogsPath = (): string => {
  return app.getPath('logs');
};