import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Calendar, 
  Clock, 
  Award,
  Settings,
  X,
  ChevronDown,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { 
  AnalyticsData, 
  AnalyticsTimeRange, 
  ProductivityInsight,
  analyticsService 
} from '@renderer/services/analytics.service';
import { Todo } from '@shared/types';

export interface AnalyticsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
  todos: Todo[];
  className?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  isOpen,
  onClose,
  todos,
  className = '',
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<AnalyticsTimeRange | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  // Get time ranges
  const timeRanges = analyticsService.getTimeRanges();

  // Generate analytics data
  const analytics = useMemo(() => {
    return analyticsService.generateAnalytics(todos, selectedTimeRange || undefined);
  }, [todos, selectedTimeRange]);

  // Generate insights
  const insights = useMemo(() => {
    return analyticsService.generateInsights(todos);
  }, [todos]);

  // Get insight icon
  const getInsightIcon = (type: ProductivityInsight['type']) => {
    switch (type) {
      case 'positive':
        return <CheckCircle className="w-4 h-4 text-fa-green-600" />;
      case 'negative':
        return <AlertTriangle className="w-4 h-4 text-fa-red-600" />;
      case 'neutral':
        return <Info className="w-4 h-4 text-fa-blue-600" />;
    }
  };

  // Get insight color
  const getInsightColor = (type: ProductivityInsight['type']) => {
    switch (type) {
      case 'positive':
        return 'bg-fa-green-50 border-fa-green-200';
      case 'negative':
        return 'bg-fa-red-50 border-fa-red-200';
      case 'neutral':
        return 'bg-fa-blue-50 border-fa-blue-200';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-6xl h-[90vh] fa-glass-panel-frosted border border-fa-gray-200 rounded-lg shadow-xl overflow-hidden ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BarChart3 className="w-6 h-6 text-fa-purple-500" />
                <h2 className="fa-heading-2">Analytics Dashboard</h2>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* Time Range Selector */}
                <div className="relative">
                  <select
                    value={selectedTimeRange?.label || 'All Time'}
                    onChange={(e) => {
                      const range = timeRanges.find(r => r.label === e.target.value);
                      setSelectedTimeRange(range || null);
                    }}
                    className="appearance-none bg-fa-white border border-fa-gray-300 rounded-lg px-3 py-2 pr-8 text-sm focus:ring-2 focus:ring-fa-purple-500 focus:border-fa-purple-500"
                  >
                    {timeRanges.map((range) => (
                      <option key={range.label} value={range.label}>
                        {range.label}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-fa-gray-400 pointer-events-none" />
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <Settings className="w-5 h-5" />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
          </div>

          <div className="overflow-y-auto h-full p-6 space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Total Todos */}
              <div className="fa-glass-panel p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-fa-gray-600">Total Todos</p>
                    <p className="text-2xl font-bold text-fa-gray-800">{analytics.totalTodos}</p>
                  </div>
                  <Target className="w-8 h-8 text-fa-blue-500" />
                </div>
              </div>

              {/* Completion Rate */}
              <div className="fa-glass-panel p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-fa-gray-600">Completion Rate</p>
                    <p className="text-2xl font-bold text-fa-green-600">
                      {analytics.completionRate.toFixed(1)}%
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-fa-green-500" />
                </div>
              </div>

              {/* Current Streak */}
              <div className="fa-glass-panel p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-fa-gray-600">Current Streak</p>
                    <p className="text-2xl font-bold text-fa-orange-600">
                      {analytics.currentStreak} days
                    </p>
                  </div>
                  <Zap className="w-8 h-8 text-fa-orange-500" />
                </div>
              </div>

              {/* Productivity Score */}
              <div className="fa-glass-panel p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-fa-gray-600">Productivity Score</p>
                    <p className="text-2xl font-bold text-fa-purple-600">
                      {analytics.productivityScore.toFixed(0)}/100
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-fa-purple-500" />
                </div>
              </div>
            </div>

            {/* Goals Progress */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Daily Goal */}
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3 flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-fa-blue-500" />
                  Daily Goal
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{analytics.todosCompletedToday} / {analytics.dailyGoal}</span>
                  </div>
                  <div className="w-full bg-fa-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min(100, analytics.dailyProgress)}%` }}
                      transition={{ duration: 1, ease: 'easeOut' }}
                      className="bg-fa-blue-500 h-2 rounded-full"
                    />
                  </div>
                  <p className="text-xs text-fa-gray-600">
                    {analytics.dailyProgress >= 100 ? 'Goal achieved!' : 
                     `${(analytics.dailyGoal - analytics.todosCompletedToday)} more to go`}
                  </p>
                </div>
              </div>

              {/* Weekly Goal */}
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3 flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-fa-green-500" />
                  Weekly Goal
                </h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(analytics.weeklyProgress * analytics.weeklyGoal / 100)} / {analytics.weeklyGoal}</span>
                  </div>
                  <div className="w-full bg-fa-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min(100, analytics.weeklyProgress)}%` }}
                      transition={{ duration: 1, ease: 'easeOut' }}
                      className="bg-fa-green-500 h-2 rounded-full"
                    />
                  </div>
                  <p className="text-xs text-fa-gray-600">
                    {analytics.weeklyProgress >= 100 ? 'Goal achieved!' : 
                     `${analytics.weeklyProgress.toFixed(1)}% complete`}
                  </p>
                </div>
              </div>
            </div>

            {/* Insights */}
            {insights.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4 flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-fa-purple-500" />
                  Productivity Insights
                </h3>
                <div className="space-y-3">
                  {insights.slice(0, 5).map((insight, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-3 rounded-lg border ${getInsightColor(insight.type)}`}
                    >
                      <div className="flex items-start space-x-3">
                        {getInsightIcon(insight.type)}
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{insight.title}</h4>
                          <p className="text-sm text-fa-gray-700 mt-1">{insight.description}</p>
                          {insight.suggestion && (
                            <p className="text-xs text-fa-gray-600 mt-2 italic">
                              💡 {insight.suggestion}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Distribution Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Priority Distribution */}
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4">Priority Distribution</h3>
                <div className="space-y-3">
                  {analytics.priorityDistribution.map((item, index) => (
                    <div key={item.priority} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded"
                          style={{ 
                            backgroundColor: ['#EF4444', '#F59E0B', '#3B82F6', '#10B981', '#8B5CF6'][index] 
                          }}
                        />
                        <span className="text-sm capitalize">{item.priority.replace('_', ' ')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-fa-gray-200 rounded-full h-2">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${item.percentage}%` }}
                            transition={{ delay: index * 0.1 }}
                            className="h-2 rounded-full"
                            style={{ 
                              backgroundColor: ['#EF4444', '#F59E0B', '#3B82F6', '#10B981', '#8B5CF6'][index] 
                            }}
                          />
                        </div>
                        <span className="text-sm text-fa-gray-600 w-8 text-right">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Time Patterns */}
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4 flex items-center">
                  <Clock className="w-4 h-4 mr-2" />
                  Activity by Hour
                </h3>
                <div className="grid grid-cols-6 gap-1">
                  {analytics.hourlyActivity.map((item) => {
                    const maxCount = Math.max(...analytics.hourlyActivity.map(h => h.count));
                    const intensity = maxCount > 0 ? item.count / maxCount : 0;
                    
                    return (
                      <motion.div
                        key={item.hour}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: item.hour * 0.02 }}
                        className="relative group"
                      >
                        <div
                          className="w-full h-8 rounded border border-fa-gray-200 flex items-center justify-center text-xs"
                          style={{
                            backgroundColor: `rgba(59, 130, 246, ${intensity * 0.8 + 0.1})`,
                          }}
                        >
                          {item.hour}
                        </div>
                        
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                          <div className="bg-fa-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                            {item.hour}:00 - {item.count} todos
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Achievements */}
            {analytics.achievements.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4 flex items-center">
                  <Award className="w-4 h-4 mr-2 text-fa-yellow-500" />
                  Recent Achievements
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {analytics.achievements.slice(-6).map((achievement, index) => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-3 rounded-lg border-2 ${
                        achievement.rarity === 'legendary' ? 'border-fa-purple-300 bg-fa-purple-50' :
                        achievement.rarity === 'epic' ? 'border-fa-blue-300 bg-fa-blue-50' :
                        achievement.rarity === 'rare' ? 'border-fa-green-300 bg-fa-green-50' :
                        'border-fa-gray-300 bg-fa-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{achievement.icon}</div>
                        <div>
                          <h4 className="font-medium text-sm">{achievement.title}</h4>
                          <p className="text-xs text-fa-gray-600">{achievement.description}</p>
                          <p className="text-xs text-fa-gray-500 mt-1 capitalize">
                            {achievement.rarity} • {achievement.type}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Completion Trend */}
            <div className="fa-glass-panel p-4">
              <h3 className="fa-heading-3 mb-4">Completion Trend (Last 30 Days)</h3>
              <div className="h-48 flex items-end space-x-1">
                {analytics.completionTrend.map((item, index) => {
                  const maxValue = Math.max(...analytics.completionTrend.map(d => Math.max(d.created, d.completed)));
                  const createdHeight = maxValue > 0 ? (item.created / maxValue) * 100 : 0;
                  const completedHeight = maxValue > 0 ? (item.completed / maxValue) * 100 : 0;
                  
                  return (
                    <div key={item.date} className="flex-1 flex flex-col items-center space-y-1 group relative">
                      <div className="w-full flex flex-col justify-end h-40">
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: `${createdHeight}%` }}
                          transition={{ delay: index * 0.02 }}
                          className="w-full bg-fa-blue-300 rounded-t"
                        />
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: `${completedHeight}%` }}
                          transition={{ delay: index * 0.02 + 0.1 }}
                          className="w-full bg-fa-green-500 rounded-t"
                        />
                      </div>
                      
                      {/* Tooltip */}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                        <div className="bg-fa-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                          <div>{new Date(item.date).toLocaleDateString()}</div>
                          <div>Created: {item.created}</div>
                          <div>Completed: {item.completed}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="flex justify-center space-x-4 mt-4 text-xs">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-fa-blue-300 rounded" />
                  <span>Created</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-fa-green-500 rounded" />
                  <span>Completed</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
