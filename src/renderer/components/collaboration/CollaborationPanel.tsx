import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>ting<PERSON>, 
  Wif<PERSON>, 
  WifiOff, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Edit3,
  Eye
} from 'lucide-react';
import { 
  CollaborationUser, 
  CollaborationSettings,
  ConflictData,
  collaborationService 
} from '@renderer/services/collaboration.service';
import { PresenceIndicator } from './PresenceIndicator';
import { formatDistanceToNow } from 'date-fns';

export interface CollaborationPanelProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  isOpen,
  onClose,
  className = '',
}) => {
  const [users, setUsers] = useState<CollaborationUser[]>([]);
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [settings, setSettings] = useState<CollaborationSettings | null>(null);
  const [conflicts, setConflicts] = useState<ConflictData[]>([]);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // Initialize collaboration service
    const initializeCollaboration = async () => {
      try {
        await collaborationService.connect();
        setCurrentUser(collaborationService.getCurrentUser());
        setUsers(collaborationService.getUsers());
        setIsConnected(collaborationService.isConnectedToCollaboration());
        setSettings(collaborationService.getSettings());
      } catch (error) {
        console.error('Failed to initialize collaboration:', error);
      }
    };

    if (isOpen) {
      initializeCollaboration();
    }

    // Set up event listeners
    const handleUsersUpdated = (updatedUsers: CollaborationUser[]) => {
      setUsers(updatedUsers);
    };

    const handleUserPresenceChanged = ({ user }: { user: CollaborationUser }) => {
      setUsers(prev => prev.map(u => u.id === user.id ? user : u));
    };

    const handleConnected = () => {
      setIsConnected(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleConflictDetected = (conflict: ConflictData) => {
      setConflicts(prev => [...prev, conflict]);
    };

    const handleSettingsUpdated = (newSettings: CollaborationSettings) => {
      setSettings(newSettings);
    };

    collaborationService.on('usersUpdated', handleUsersUpdated);
    collaborationService.on('userPresenceChanged', handleUserPresenceChanged);
    collaborationService.on('connected', handleConnected);
    collaborationService.on('disconnected', handleDisconnected);
    collaborationService.on('conflictDetected', handleConflictDetected);
    collaborationService.on('settingsUpdated', handleSettingsUpdated);

    return () => {
      collaborationService.off('usersUpdated', handleUsersUpdated);
      collaborationService.off('userPresenceChanged', handleUserPresenceChanged);
      collaborationService.off('connected', handleConnected);
      collaborationService.off('disconnected', handleDisconnected);
      collaborationService.off('conflictDetected', handleConflictDetected);
      collaborationService.off('settingsUpdated', handleSettingsUpdated);
    };
  }, [isOpen]);

  const handleConnect = async () => {
    try {
      await collaborationService.connect();
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  };

  const handleDisconnect = () => {
    collaborationService.disconnect();
  };

  const handleSettingsChange = (key: keyof CollaborationSettings, value: any) => {
    if (settings) {
      collaborationService.updateSettings({ [key]: value });
    }
  };

  const resolveConflict = (conflictIndex: number, resolution: 'accept' | 'reject') => {
    // Remove the conflict from the list
    setConflicts(prev => prev.filter((_, index) => index !== conflictIndex));
    
    // In a real implementation, you would apply the resolution
    console.log(`Conflict resolved: ${resolution}`);
  };

  const onlineUsers = users.filter(user => user.isOnline);
  const offlineUsers = users.filter(user => !user.isOnline);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`absolute right-0 top-0 h-full w-96 fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Users className="w-6 h-6 text-fa-blue-500" />
                <h2 className="fa-heading-2">Collaboration</h2>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                  isConnected 
                    ? 'bg-fa-green-100 text-fa-green-700' 
                    : 'bg-fa-red-100 text-fa-red-700'
                }`}>
                  {isConnected ? (
                    <>
                      <Wifi className="w-3 h-3" />
                      <span>Connected</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="w-3 h-3" />
                      <span>Offline</span>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <Settings className="w-4 h-4" />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Connection Controls */}
            {!isConnected && (
              <div className="text-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleConnect}
                  className="px-4 py-2 bg-fa-blue-600 text-white rounded-lg hover:bg-fa-blue-700 transition-colors"
                >
                  Connect to Collaboration
                </motion.button>
              </div>
            )}

            {/* Current User */}
            {currentUser && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3">You</h3>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium"
                    style={{ backgroundColor: currentUser.color }}
                  >
                    {currentUser.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <div className="font-medium">{currentUser.name}</div>
                    <div className="text-sm text-fa-gray-600">{currentUser.email}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Conflicts */}
            {conflicts.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3 flex items-center text-fa-orange-700">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Conflicts ({conflicts.length})
                </h3>
                <div className="space-y-3">
                  {conflicts.map((conflict, index) => (
                    <div key={index} className="p-3 bg-fa-orange-50 border border-fa-orange-200 rounded-lg">
                      <div className="text-sm">
                        <p className="font-medium text-fa-orange-800 mb-1">
                          Multiple users editing the same todo
                        </p>
                        <p className="text-fa-orange-700 mb-2">
                          Users: {conflict.conflictingUsers.join(', ')}
                        </p>
                        <div className="flex space-x-2">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => resolveConflict(index, 'accept')}
                            className="px-2 py-1 bg-fa-green-100 text-fa-green-700 rounded text-xs hover:bg-fa-green-200"
                          >
                            Accept Changes
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => resolveConflict(index, 'reject')}
                            className="px-2 py-1 bg-fa-red-100 text-fa-red-700 rounded text-xs hover:bg-fa-red-200"
                          >
                            Reject Changes
                          </motion.button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Online Users */}
            {onlineUsers.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3 flex items-center">
                  <div className="w-2 h-2 bg-fa-green-500 rounded-full mr-2" />
                  Online ({onlineUsers.length})
                </h3>
                <div className="space-y-3">
                  {onlineUsers.map((user) => (
                    <motion.div
                      key={user.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm relative"
                          style={{ backgroundColor: user.color }}
                        >
                          {user.name.charAt(0).toUpperCase()}
                          {user.currentTodo && (
                            <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-0.5">
                              {user.cursor ? (
                                <Edit3 className="w-2 h-2 text-fa-green-600" />
                              ) : (
                                <Eye className="w-2 h-2 text-fa-blue-600" />
                              )}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{user.name}</div>
                          <div className="text-xs text-fa-gray-600">
                            {user.currentTodo ? (
                              user.cursor ? 'Editing a todo' : 'Viewing a todo'
                            ) : (
                              'Active'
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-fa-gray-500">
                        {formatDistanceToNow(user.lastSeen, { addSuffix: true })}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Offline Users */}
            {offlineUsers.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-3 flex items-center">
                  <div className="w-2 h-2 bg-fa-gray-400 rounded-full mr-2" />
                  Offline ({offlineUsers.length})
                </h3>
                <div className="space-y-3">
                  {offlineUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between opacity-60">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm"
                          style={{ backgroundColor: user.color }}
                        >
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{user.name}</div>
                          <div className="text-xs text-fa-gray-600">Offline</div>
                        </div>
                      </div>
                      <div className="text-xs text-fa-gray-500">
                        {formatDistanceToNow(user.lastSeen, { addSuffix: true })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Settings */}
            <AnimatePresence>
              {showSettings && settings && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="fa-glass-panel p-4"
                >
                  <h3 className="fa-heading-3 mb-3 flex items-center">
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center justify-between">
                      <span className="text-sm">Presence Indicators</span>
                      <input
                        type="checkbox"
                        checked={settings.enablePresenceIndicators}
                        onChange={(e) => handleSettingsChange('enablePresenceIndicators', e.target.checked)}
                        className="rounded border-fa-gray-300 text-fa-blue-600 focus:ring-fa-blue-500"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between">
                      <span className="text-sm">Live Updates</span>
                      <input
                        type="checkbox"
                        checked={settings.enableLiveUpdates}
                        onChange={(e) => handleSettingsChange('enableLiveUpdates', e.target.checked)}
                        className="rounded border-fa-gray-300 text-fa-blue-600 focus:ring-fa-blue-500"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between">
                      <span className="text-sm">Typing Indicators</span>
                      <input
                        type="checkbox"
                        checked={settings.enableTypingIndicators}
                        onChange={(e) => handleSettingsChange('enableTypingIndicators', e.target.checked)}
                        className="rounded border-fa-gray-300 text-fa-blue-600 focus:ring-fa-blue-500"
                      />
                    </label>
                    
                    <div>
                      <label className="block text-sm font-medium text-fa-gray-700 mb-1">
                        Conflict Resolution
                      </label>
                      <select
                        value={settings.conflictResolutionStrategy}
                        onChange={(e) => handleSettingsChange('conflictResolutionStrategy', e.target.value)}
                        className="w-full px-3 py-2 border border-fa-gray-300 rounded-lg focus:ring-2 focus:ring-fa-blue-500 focus:border-fa-blue-500 text-sm"
                      >
                        <option value="latest_wins">Latest Wins</option>
                        <option value="merge">Auto Merge</option>
                        <option value="manual">Manual Resolution</option>
                      </select>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Connection Actions */}
            {isConnected && (
              <div className="text-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleDisconnect}
                  className="px-4 py-2 bg-fa-red-600 text-white rounded-lg hover:bg-fa-red-700 transition-colors"
                >
                  Disconnect
                </motion.button>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
