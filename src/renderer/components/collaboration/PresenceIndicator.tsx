import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Edit3, Eye } from 'lucide-react';
import { CollaborationUser } from '@renderer/services/collaboration.service';
import { formatDistanceToNow } from 'date-fns';

export interface PresenceIndicatorProps {
  users: CollaborationUser[];
  todoId?: string;
  field?: string;
  showNames?: boolean;
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({
  users,
  todoId,
  field,
  showNames = false,
  maxVisible = 3,
  size = 'md',
  className = '',
}) => {
  // Filter users based on context
  const relevantUsers = users.filter(user => {
    if (!user.isOnline) return false;
    
    if (todoId) {
      // Show users editing this specific todo
      return user.currentTodo === todoId;
    }
    
    // Show all online users
    return true;
  });

  // Further filter by field if specified
  const fieldUsers = field 
    ? relevantUsers.filter(user => user.cursor?.field === field)
    : relevantUsers;

  const visibleUsers = fieldUsers.slice(0, maxVisible);
  const hiddenCount = Math.max(0, fieldUsers.length - maxVisible);

  // Size configurations
  const sizeConfig = {
    sm: {
      avatar: 'w-6 h-6',
      text: 'text-xs',
      spacing: '-space-x-1',
      icon: 'w-3 h-3',
    },
    md: {
      avatar: 'w-8 h-8',
      text: 'text-sm',
      spacing: '-space-x-2',
      icon: 'w-4 h-4',
    },
    lg: {
      avatar: 'w-10 h-10',
      text: 'text-base',
      spacing: '-space-x-3',
      icon: 'w-5 h-5',
    },
  };

  const config = sizeConfig[size];

  if (visibleUsers.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center ${className}`}>
      {/* User Avatars */}
      <div className={`flex ${config.spacing}`}>
        <AnimatePresence>
          {visibleUsers.map((user, index) => (
            <motion.div
              key={user.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ delay: index * 0.1 }}
              className="relative group"
            >
              {/* Avatar */}
              <div
                className={`${config.avatar} rounded-full border-2 border-white shadow-sm flex items-center justify-center text-white font-medium ${config.text} relative z-10`}
                style={{ backgroundColor: user.color }}
              >
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span>{user.name.charAt(0).toUpperCase()}</span>
                )}
              </div>

              {/* Activity Indicator */}
              {user.currentTodo && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -bottom-1 -right-1 bg-white rounded-full p-1 shadow-sm"
                >
                  {user.cursor ? (
                    <Edit3 className={`${config.icon} text-fa-green-600`} />
                  ) : (
                    <Eye className={`${config.icon} text-fa-blue-600`} />
                  )}
                </motion.div>
              )}

              {/* Online Status */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-fa-green-500 border-2 border-white rounded-full" />

              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-20">
                <div className="bg-fa-gray-800 text-white text-xs rounded-lg px-2 py-1 whitespace-nowrap">
                  <div className="font-medium">{user.name}</div>
                  {user.currentTodo && (
                    <div className="text-fa-gray-300">
                      {user.cursor ? 'Editing' : 'Viewing'}
                      {field && user.cursor?.field === field && ' this field'}
                    </div>
                  )}
                  <div className="text-fa-gray-400">
                    Active {formatDistanceToNow(user.lastSeen, { addSuffix: true })}
                  </div>
                  {/* Tooltip Arrow */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-fa-gray-800" />
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Hidden Count Indicator */}
        {hiddenCount > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`${config.avatar} rounded-full bg-fa-gray-200 border-2 border-white shadow-sm flex items-center justify-center text-fa-gray-600 font-medium ${config.text} relative z-10`}
          >
            +{hiddenCount}
          </motion.div>
        )}
      </div>

      {/* Names List */}
      {showNames && visibleUsers.length > 0 && (
        <div className="ml-3">
          <div className={`${config.text} text-fa-gray-700`}>
            {visibleUsers.length === 1 ? (
              <span>
                <span className="font-medium">{visibleUsers[0].name}</span>
                {visibleUsers[0].currentTodo && (
                  <span className="text-fa-gray-500 ml-1">
                    {visibleUsers[0].cursor ? 'is editing' : 'is viewing'}
                  </span>
                )}
              </span>
            ) : visibleUsers.length === 2 ? (
              <span>
                <span className="font-medium">{visibleUsers[0].name}</span> and{' '}
                <span className="font-medium">{visibleUsers[1].name}</span>
                {todoId && <span className="text-fa-gray-500 ml-1">are here</span>}
              </span>
            ) : (
              <span>
                <span className="font-medium">{visibleUsers[0].name}</span> and{' '}
                <span className="font-medium">{visibleUsers.length - 1} others</span>
                {todoId && <span className="text-fa-gray-500 ml-1">are here</span>}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Typing Indicator Component
export interface TypingIndicatorProps {
  users: CollaborationUser[];
  todoId: string;
  field?: string;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  users,
  todoId,
  field,
  className = '',
}) => {
  // Filter to users who are typing in this context
  const typingUsers = users.filter(user => 
    user.isOnline && 
    user.currentTodo === todoId &&
    (!field || user.cursor?.field === field)
  );

  if (typingUsers.length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      className={`flex items-center space-x-2 text-xs text-fa-gray-500 ${className}`}
    >
      {/* Typing Animation */}
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.2,
            }}
            className="w-1 h-1 bg-fa-gray-400 rounded-full"
          />
        ))}
      </div>

      {/* Typing Text */}
      <span>
        {typingUsers.length === 1 ? (
          <span>
            <span className="font-medium" style={{ color: typingUsers[0].color }}>
              {typingUsers[0].name}
            </span>{' '}
            is typing...
          </span>
        ) : typingUsers.length === 2 ? (
          <span>
            <span className="font-medium" style={{ color: typingUsers[0].color }}>
              {typingUsers[0].name}
            </span>{' '}
            and{' '}
            <span className="font-medium" style={{ color: typingUsers[1].color }}>
              {typingUsers[1].name}
            </span>{' '}
            are typing...
          </span>
        ) : (
          <span>
            <span className="font-medium" style={{ color: typingUsers[0].color }}>
              {typingUsers[0].name}
            </span>{' '}
            and {typingUsers.length - 1} others are typing...
          </span>
        )}
      </span>
    </motion.div>
  );
};

// Live Update Indicator
export interface LiveUpdateIndicatorProps {
  hasUpdates: boolean;
  updateCount: number;
  onViewUpdates?: () => void;
  className?: string;
}

export const LiveUpdateIndicator: React.FC<LiveUpdateIndicatorProps> = ({
  hasUpdates,
  updateCount,
  onViewUpdates,
  className = '',
}) => {
  if (!hasUpdates) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={`fixed bottom-4 right-4 ${className}`}
    >
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onViewUpdates}
        className="fa-glass-panel p-3 rounded-lg shadow-lg border border-fa-blue-200 flex items-center space-x-2"
      >
        <div className="flex items-center space-x-2">
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-2 h-2 bg-fa-blue-500 rounded-full"
          />
          <span className="text-sm font-medium text-fa-blue-700">
            {updateCount} new update{updateCount !== 1 ? 's' : ''}
          </span>
        </div>
      </motion.button>
    </motion.div>
  );
};
