import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Todo } from '@shared/types';
import { TodoItem } from '@renderer/components/todos/TodoItem';
import { getOverdueInfo } from '@renderer/utils/overdueUtils';
import { overdueNotificationService } from '@renderer/services/overdueNotification.service';

export const OverdueDemo: React.FC = () => {
  const [demoTodos, setDemoTodos] = useState<Todo[]>([]);

  useEffect(() => {
    // Create demo todos with different overdue states
    const now = new Date();
    const demos: Todo[] = [
      {
        id: 'demo-1',
        user_id: 'demo-user',
        title: 'Slightly Overdue Task',
        description: 'This task is just a few hours overdue',
        status: 'pending',
        priority: 'medium',
        due_date: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
        tags: ['demo'],
        position: 1,
        metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
      {
        id: 'demo-2',
        user_id: 'demo-user',
        title: 'Moderately Overdue Task',
        description: 'This task is a few days overdue',
        status: 'pending',
        priority: 'high',
        due_date: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        tags: ['demo', 'urgent'],
        position: 2,
        metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
      {
        id: 'demo-3',
        user_id: 'demo-user',
        title: 'Severely Overdue Task',
        description: 'This task is severely overdue and needs immediate attention',
        status: 'pending',
        priority: 'very_high',
        due_date: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        tags: ['demo', 'critical'],
        position: 3,
        metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
      {
        id: 'demo-4',
        user_id: 'demo-user',
        title: 'Normal Task (Not Overdue)',
        description: 'This task is due tomorrow',
        status: 'pending',
        priority: 'medium',
        due_date: new Date(now.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
        tags: ['demo'],
        position: 4,
        metadata: {},
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
    ];

    setDemoTodos(demos);
  }, []);

  const triggerNotificationTest = () => {
    // Manually trigger a notification for demo purposes
    const uiStore = require('@renderer/stores/uiStore').useUIStore.getState();
    
    uiStore.addNotification({
      type: 'warning',
      title: 'Demo Overdue Notification',
      message: 'This is a test notification for overdue tasks',
      duration: 5000,
      action: {
        label: 'View Task',
        onClick: () => console.log('Navigate to task')
      }
    });
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-fa-gray-800 mb-2">
          Overdue Highlighting & Notifications Demo
        </h2>
        <p className="text-fa-gray-600 mb-4">
          This demo shows the enhanced overdue highlighting with different severity levels and notification system.
        </p>
        
        <button
          onClick={triggerNotificationTest}
          className="fa-button-glass px-4 py-2 mb-4"
        >
          Test Notification
        </button>
      </div>

      <div className="space-y-4">
        {demoTodos.map((todo) => {
          const overdueInfo = getOverdueInfo(todo);
          
          return (
            <motion.div
              key={todo.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              {/* Overdue Info Display */}
              <div className="mb-2 text-sm text-fa-gray-600">
                <strong>Overdue Status:</strong> {overdueInfo.isOverdue ? (
                  <span className={`font-medium ${
                    overdueInfo.severity === 'slight' ? 'text-orange-600' :
                    overdueInfo.severity === 'moderate' ? 'text-red-600' :
                    overdueInfo.severity === 'severe' ? 'text-red-800' : 'text-gray-600'
                  }`}>
                    {overdueInfo.severity} ({overdueInfo.formattedDuration})
                  </span>
                ) : (
                  <span className="text-green-600">Not overdue</span>
                )}
              </div>
              
              {/* Todo Item */}
              <TodoItem 
                todo={todo}
                onUpdate={() => {}}
                onDelete={() => {}}
              />
            </motion.div>
          );
        })}
      </div>

      <div className="mt-8 p-4 bg-fa-white-glass rounded-lg">
        <h3 className="text-lg font-semibold text-fa-gray-800 mb-2">
          Overdue Severity Levels
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-orange-400 rounded"></div>
            <span><strong>Slight:</strong> Less than 1 day overdue (orange)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded"></div>
            <span><strong>Moderate:</strong> 1-7 days overdue (red)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-600 rounded animate-pulse"></div>
            <span><strong>Severe:</strong> More than 7 days overdue (dark red, pulsing)</span>
          </div>
        </div>
      </div>

      <div className="mt-4 p-4 bg-fa-white-glass rounded-lg">
        <h3 className="text-lg font-semibold text-fa-gray-800 mb-2">
          Notification Features
        </h3>
        <ul className="text-sm space-y-1 text-fa-gray-600">
          <li>• Automatic notifications for newly overdue tasks</li>
          <li>• Different notification frequencies based on severity</li>
          <li>• Prevents duplicate notifications</li>
          <li>• Integrates with existing toast notification system</li>
          <li>• Runs background checks every 15 minutes</li>
        </ul>
      </div>
    </div>
  );
};
