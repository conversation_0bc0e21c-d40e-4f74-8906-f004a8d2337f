import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  Download, 
  X, 
  FileText, 
  FileSpreadsheet, 
  File, 
  Calendar,
  Code,
  Settings,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { 
  ExportFormat, 
  ImportFormat, 
  ExportOptions, 
  ImportOptions,
  ExportResult,
  ImportResult,
  importExportService 
} from '@renderer/services/import-export.service';
import { Todo } from '@shared/types';

export interface ImportExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  todos: Todo[];
  onImportComplete?: (result: ImportResult) => void;
  onExportComplete?: (result: ExportResult) => void;
  className?: string;
}

type DialogMode = 'export' | 'import';

export const ImportExportDialog: React.FC<ImportExportDialogProps> = ({
  isOpen,
  onClose,
  todos,
  onImportComplete,
  onExportComplete,
  className = '',
}) => {
  const [mode, setMode] = useState<DialogMode>('export');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<ExportResult | ImportResult | null>(null);
  
  // Export state
  const [exportFormat, setExportFormat] = useState<ExportFormat>('json');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'json',
    includeCompleted: true,
    includeArchived: false,
  });

  // Import state
  const [importFormat, setImportFormat] = useState<ImportFormat>('json');
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    format: 'json',
    validateData: true,
    mergeStrategy: 'merge',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Format configurations
  const exportFormats: { value: ExportFormat; label: string; icon: React.ReactNode; description: string }[] = [
    { value: 'json', label: 'JSON', icon: <Code className="w-4 h-4" />, description: 'Structured data format' },
    { value: 'csv', label: 'CSV', icon: <FileSpreadsheet className="w-4 h-4" />, description: 'Spreadsheet compatible' },
    { value: 'markdown', label: 'Markdown', icon: <FileText className="w-4 h-4" />, description: 'Human-readable format' },
    { value: 'pdf', label: 'PDF', icon: <File className="w-4 h-4" />, description: 'Printable document' },
    { value: 'ics', label: 'Calendar (ICS)', icon: <Calendar className="w-4 h-4" />, description: 'Calendar events' },
    { value: 'xml', label: 'XML', icon: <Code className="w-4 h-4" />, description: 'Structured markup' },
  ];

  const importFormats: { value: ImportFormat; label: string; icon: React.ReactNode; description: string }[] = [
    { value: 'json', label: 'JSON', icon: <Code className="w-4 h-4" />, description: 'Structured data format' },
    { value: 'csv', label: 'CSV', icon: <FileSpreadsheet className="w-4 h-4" />, description: 'Spreadsheet data' },
    { value: 'xml', label: 'XML', icon: <Code className="w-4 h-4" />, description: 'Structured markup' },
  ];

  // Handle export
  const handleExport = async () => {
    setIsProcessing(true);
    setResult(null);

    try {
      const options: ExportOptions = {
        ...exportOptions,
        format: exportFormat,
      };

      const exportResult = await importExportService.exportTodos(todos, options);
      setResult(exportResult);

      if (exportResult.success && exportResult.data) {
        // Download the file
        const blob = exportResult.data instanceof Blob 
          ? exportResult.data 
          : new Blob([exportResult.data], { type: getContentType(exportFormat) });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = exportResult.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        if (onExportComplete) {
          onExportComplete(exportResult);
        }
      }
    } catch (error) {
      setResult({
        success: false,
        filename: '',
        format: exportFormat,
        recordCount: 0,
        errors: [error instanceof Error ? error.message : 'Export failed'],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setResult(null);

    try {
      const options: ImportOptions = {
        ...importOptions,
        format: importFormat,
      };

      const importResult = await importExportService.importTodos(selectedFile, options);
      setResult(importResult);

      if (importResult.success && onImportComplete) {
        onImportComplete(importResult);
      }
    } catch (error) {
      setResult({
        success: false,
        importedCount: 0,
        skippedCount: 0,
        errorCount: 1,
        todos: [],
        errors: [error instanceof Error ? error.message : 'Import failed'],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Auto-detect format based on file extension
      const extension = file.name.split('.').pop()?.toLowerCase();
      if (extension === 'json') setImportFormat('json');
      else if (extension === 'csv') setImportFormat('csv');
      else if (extension === 'xml') setImportFormat('xml');
    }
  };

  // Get content type for download
  const getContentType = (format: ExportFormat): string => {
    const contentTypes: Record<ExportFormat, string> = {
      json: 'application/json',
      csv: 'text/csv',
      markdown: 'text/markdown',
      pdf: 'application/pdf',
      ics: 'text/calendar',
      xml: 'application/xml',
    };
    return contentTypes[format];
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl fa-glass-panel-frosted border border-fa-gray-200 rounded-lg shadow-xl max-h-[90vh] overflow-y-auto ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {mode === 'export' ? (
                  <Download className="w-6 h-6 text-fa-blue-500" />
                ) : (
                  <Upload className="w-6 h-6 text-fa-green-500" />
                )}
                <h2 className="fa-heading-2">
                  {mode === 'export' ? 'Export Todos' : 'Import Todos'}
                </h2>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Mode Toggle */}
                <div className="flex bg-fa-gray-100 rounded-lg p-1">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setMode('export')}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      mode === 'export'
                        ? 'bg-fa-white text-fa-blue-600 shadow-sm'
                        : 'text-fa-gray-600 hover:text-fa-gray-800'
                    }`}
                  >
                    Export
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setMode('import')}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      mode === 'import'
                        ? 'bg-fa-white text-fa-green-600 shadow-sm'
                        : 'text-fa-gray-600 hover:text-fa-gray-800'
                    }`}
                  >
                    Import
                  </motion.button>
                </div>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {mode === 'export' ? (
              <>
                {/* Export Format Selection */}
                <div>
                  <h3 className="fa-heading-3 mb-3">Export Format</h3>
                  <div className="grid grid-cols-2 gap-3">
                    {exportFormats.map((format) => (
                      <motion.button
                        key={format.value}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setExportFormat(format.value)}
                        className={`p-3 rounded-lg border transition-all duration-200 text-left ${
                          exportFormat === format.value
                            ? 'border-fa-blue-300 bg-fa-blue-50'
                            : 'border-fa-gray-200 hover:border-fa-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`${exportFormat === format.value ? 'text-fa-blue-600' : 'text-fa-gray-400'}`}>
                            {format.icon}
                          </div>
                          <div>
                            <div className="font-medium">{format.label}</div>
                            <div className="text-sm text-fa-gray-600">{format.description}</div>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Export Options */}
                <div>
                  <h3 className="fa-heading-3 mb-3 flex items-center">
                    <Settings className="w-4 h-4 mr-2" />
                    Export Options
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeCompleted}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, includeCompleted: e.target.checked }))}
                        className="rounded border-fa-gray-300 text-fa-blue-600 focus:ring-fa-blue-500"
                      />
                      <span className="text-sm">Include completed todos</span>
                    </label>
                    
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={exportOptions.includeArchived}
                        onChange={(e) => setExportOptions(prev => ({ ...prev, includeArchived: e.target.checked }))}
                        className="rounded border-fa-gray-300 text-fa-blue-600 focus:ring-fa-blue-500"
                      />
                      <span className="text-sm">Include archived todos</span>
                    </label>
                  </div>
                </div>

                {/* Export Summary */}
                <div className="p-4 bg-fa-blue-50 rounded-lg">
                  <h4 className="font-medium text-fa-blue-800 mb-2">Export Summary</h4>
                  <div className="text-sm text-fa-blue-700">
                    <p>Total todos: {todos.length}</p>
                    <p>Format: {exportFormats.find(f => f.value === exportFormat)?.label}</p>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Import Format Selection */}
                <div>
                  <h3 className="fa-heading-3 mb-3">Import Format</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {importFormats.map((format) => (
                      <motion.button
                        key={format.value}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setImportFormat(format.value)}
                        className={`p-3 rounded-lg border transition-all duration-200 text-left ${
                          importFormat === format.value
                            ? 'border-fa-green-300 bg-fa-green-50'
                            : 'border-fa-gray-200 hover:border-fa-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`${importFormat === format.value ? 'text-fa-green-600' : 'text-fa-gray-400'}`}>
                            {format.icon}
                          </div>
                          <div>
                            <div className="font-medium">{format.label}</div>
                            <div className="text-sm text-fa-gray-600">{format.description}</div>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* File Selection */}
                <div>
                  <h3 className="fa-heading-3 mb-3">Select File</h3>
                  <div
                    onClick={() => fileInputRef.current?.click()}
                    className="border-2 border-dashed border-fa-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-fa-green-400 transition-colors"
                  >
                    <Upload className="w-8 h-8 text-fa-gray-400 mx-auto mb-2" />
                    {selectedFile ? (
                      <div>
                        <p className="font-medium text-fa-gray-800">{selectedFile.name}</p>
                        <p className="text-sm text-fa-gray-600">{(selectedFile.size / 1024).toFixed(1)} KB</p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-fa-gray-600">Click to select a file</p>
                        <p className="text-sm text-fa-gray-500">Supports JSON, CSV, and XML files</p>
                      </div>
                    )}
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json,.csv,.xml"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>

                {/* Import Options */}
                <div>
                  <h3 className="fa-heading-3 mb-3 flex items-center">
                    <Settings className="w-4 h-4 mr-2" />
                    Import Options
                  </h3>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={importOptions.validateData}
                        onChange={(e) => setImportOptions(prev => ({ ...prev, validateData: e.target.checked }))}
                        className="rounded border-fa-gray-300 text-fa-green-600 focus:ring-fa-green-500"
                      />
                      <span className="text-sm">Validate imported data</span>
                    </label>
                    
                    <div>
                      <label className="block text-sm font-medium text-fa-gray-700 mb-1">
                        Merge Strategy
                      </label>
                      <select
                        value={importOptions.mergeStrategy}
                        onChange={(e) => setImportOptions(prev => ({ ...prev, mergeStrategy: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-fa-gray-300 rounded-lg focus:ring-2 focus:ring-fa-green-500 focus:border-fa-green-500"
                      >
                        <option value="merge">Merge with existing</option>
                        <option value="replace">Replace existing</option>
                        <option value="skip">Skip duplicates</option>
                      </select>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Result Display */}
            {result && (
              <div className={`p-4 rounded-lg ${result.success ? 'bg-fa-green-50 border border-fa-green-200' : 'bg-fa-red-50 border border-fa-red-200'}`}>
                <div className="flex items-start space-x-3">
                  {result.success ? (
                    <CheckCircle className="w-5 h-5 text-fa-green-600 mt-0.5" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-fa-red-600 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <h4 className={`font-medium ${result.success ? 'text-fa-green-800' : 'text-fa-red-800'}`}>
                      {result.success ? 'Success!' : 'Error'}
                    </h4>
                    {mode === 'export' && 'recordCount' in result && (
                      <p className="text-sm text-fa-green-700">
                        Exported {result.recordCount} todos
                      </p>
                    )}
                    {mode === 'import' && 'importedCount' in result && (
                      <p className="text-sm text-fa-green-700">
                        Imported {result.importedCount} todos
                        {result.skippedCount > 0 && `, skipped ${result.skippedCount}`}
                        {result.errorCount > 0 && `, ${result.errorCount} errors`}
                      </p>
                    )}
                    {result.errors && result.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-fa-red-800">Errors:</p>
                        <ul className="text-sm text-fa-red-700 list-disc list-inside">
                          {result.errors.slice(0, 5).map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                          {result.errors.length > 5 && (
                            <li>... and {result.errors.length - 5} more</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-fa-gray-200">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onClose}
                className="px-4 py-2 text-fa-gray-600 hover:text-fa-gray-800 transition-colors"
              >
                Cancel
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={mode === 'export' ? handleExport : handleImport}
                disabled={isProcessing || (mode === 'import' && !selectedFile)}
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                  mode === 'export'
                    ? 'bg-fa-blue-600 text-white hover:bg-fa-blue-700 disabled:bg-fa-gray-400'
                    : 'bg-fa-green-600 text-white hover:bg-fa-green-700 disabled:bg-fa-gray-400'
                } disabled:cursor-not-allowed`}
              >
                {isProcessing ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    {mode === 'export' ? (
                      <Download className="w-4 h-4" />
                    ) : (
                      <Upload className="w-4 h-4" />
                    )}
                    <span>{mode === 'export' ? 'Export' : 'Import'}</span>
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
