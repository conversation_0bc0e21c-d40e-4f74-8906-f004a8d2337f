import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X } from 'lucide-react';
import { useTheme } from '../../providers/ThemeProvider';
import { TitleBar } from './TitleBar';
import { Sidebar } from './Sidebar';
import { TodoList } from '../todos/TodoList';
import { TodoInput } from '../todos/TodoInput';
import { SyncStatusIndicator, SyncPanel, ConflictResolutionDialog } from '../sync';
import { slideInLeft, overlayVariants } from '@renderer/utils/animations';
import { useHoverAnimation } from '@renderer/hooks/useAnimations';
import { useResponsive } from '@renderer/hooks/useResponsive';

interface MainLayoutProps {
  systemInfo?: any;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ systemInfo }) => {
  const { theme, toggleTheme } = useTheme();
  const responsive = useResponsive();
  const [sidebarOpen, setSidebarOpen] = useState(!responsive.isMobile); // Start open on desktop
  const { hoverProps } = useHoverAnimation();

  // Auto-manage sidebar based on screen size
  useEffect(() => {
    if (!responsive.isMobile && !sidebarOpen) {
      setSidebarOpen(true); // Auto-open on desktop
    }
  }, [responsive.isMobile, sidebarOpen]);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  return (
    <div className="w-full h-full flex flex-col bg-transparent">
      {/* Title Bar */}
      <TitleBar
        systemInfo={systemInfo}
        onToggleTheme={toggleTheme}
        theme={theme}
        onToggleSidebar={toggleSidebar}
        sidebarOpen={sidebarOpen}
        isMobile={responsive.isMobile}
      />

      <div className="flex flex-1 overflow-hidden relative">
        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {responsive.isMobile && sidebarOpen && (
            <motion.div
              variants={overlayVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              className="fixed inset-0 bg-black bg-opacity-40 backdrop-blur-sm z-40"
              onClick={() => setSidebarOpen(false)}
            />
          )}
        </AnimatePresence>

        {/* Sidebar */}
        <AnimatePresence>
          {(sidebarOpen || !responsive.isMobile) && (
            <motion.div
              variants={slideInLeft}
              initial={responsive.isMobile ? "initial" : false}
              animate="animate"
              exit="exit"
              className={`
                h-full bg-transparent z-50
                ${responsive.isMobile
                  ? 'fixed left-0 top-12 bottom-0'
                  : 'relative'
                }
                ${responsive.isMobile && responsive.isLandscape
                  ? 'w-72'
                  : responsive.isMobile
                    ? 'w-80'
                    : 'w-64'
                }
              `}
            >
              <Sidebar onClose={() => responsive.isMobile && setSidebarOpen(false)} />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className={`
          flex-1 flex flex-col overflow-hidden fa-transition-all
          ${responsive.isMobile ? 'p-4' : responsive.isTablet ? 'p-5' : 'p-6'}
        `}>
          <div className="fa-glass-panel-mobile flex-1 flex flex-col">
            {/* Header */}
            <div className={`
              mb-6 flex items-center justify-between
              ${responsive.isMobile ? 'flex-col space-y-4' : 'flex-row'}
            `}>
              <div className={responsive.isMobile ? 'text-center w-full' : ''}>
                <div className={`
                  flex items-center space-x-3
                  ${responsive.isMobile ? 'justify-center' : ''}
                `}>
                  {responsive.isMobile && (
                    <motion.button
                      {...hoverProps}
                      onClick={toggleSidebar}
                      className="fa-button-ghost p-2 fa-touch-target"
                    >
                      <Menu className="w-5 h-5" />
                    </motion.button>
                  )}
                  <h1 className="fa-heading-1 mb-2">
                    My Tasks
                  </h1>
                </div>
                <p className="fa-body text-fa-gray-600">Stay organized and productive</p>
              </div>
              <div className={responsive.isMobile ? 'w-full flex justify-center' : ''}>
                <SyncStatusIndicator />
              </div>
            </div>

            {/* Todo Input */}
            <div className="mb-6">
              <TodoInput />
            </div>

            {/* Todo List */}
            <div className="flex-1 overflow-hidden">
              <TodoList />
            </div>
          </div>
        </div>
      </div>

      {/* Sync Components */}
      <SyncPanel />
      <ConflictResolutionDialog />
    </div>
  );
};