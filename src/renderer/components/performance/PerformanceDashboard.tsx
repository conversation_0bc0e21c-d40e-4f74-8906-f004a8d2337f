import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  Zap,
  Database,
  Search,
  Monitor,
  X,
  RefreshCw
} from 'lucide-react';
import { 
  PerformanceMetric, 
  PerformanceReport, 
  PerformanceAlert 
} from '@renderer/services/performance-monitor.service';
import { usePerformanceMonitor } from '@renderer/hooks/usePerformanceMonitor';
import { formatDistanceToNow } from 'date-fns';

export interface PerformanceDashboardProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  isOpen,
  onClose,
  className = '',
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '6h' | '24h' | 'all'>('1h');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'search' | 'ui' | 'db' | 'sync'>('all');

  const {
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    metrics,
    alerts,
    report,
    clearMetrics,
    generateReport,
  } = usePerformanceMonitor();

  // Filter metrics based on selected time range and category
  const filteredMetrics = useMemo(() => {
    let filtered = metrics;

    // Filter by time range
    if (selectedTimeRange !== 'all') {
      const now = new Date();
      const hours = selectedTimeRange === '1h' ? 1 : selectedTimeRange === '6h' ? 6 : 24;
      const cutoff = new Date(now.getTime() - hours * 60 * 60 * 1000);
      filtered = filtered.filter(metric => metric.timestamp >= cutoff);
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(metric => {
        switch (selectedCategory) {
          case 'search':
            return metric.name.includes('search') || metric.name.includes('index');
          case 'ui':
            return metric.name.includes('render') || metric.name.includes('component') || metric.name.includes('layout');
          case 'db':
            return metric.name.includes('db') || metric.name.includes('query') || metric.name.includes('transaction');
          case 'sync':
            return metric.name.includes('sync') || metric.name.includes('upload') || metric.name.includes('download');
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [metrics, selectedTimeRange, selectedCategory]);

  // Generate filtered report
  const filteredReport = useMemo(() => {
    if (filteredMetrics.length === 0) return null;
    
    const timeRange = selectedTimeRange === 'all' ? undefined : {
      start: new Date(Date.now() - (selectedTimeRange === '1h' ? 1 : selectedTimeRange === '6h' ? 6 : 24) * 60 * 60 * 1000),
      end: new Date(),
    };

    return generateReport(timeRange);
  }, [filteredMetrics, selectedTimeRange, generateReport]);

  // Get status color
  const getStatusColor = (status: 'good' | 'warning' | 'critical') => {
    switch (status) {
      case 'good':
        return 'text-fa-green-600 bg-fa-green-100';
      case 'warning':
        return 'text-fa-orange-600 bg-fa-orange-100';
      case 'critical':
        return 'text-fa-red-600 bg-fa-red-100';
      default:
        return 'text-fa-gray-600 bg-fa-gray-100';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'search':
        return <Search className="w-4 h-4" />;
      case 'ui':
        return <Monitor className="w-4 h-4" />;
      case 'db':
        return <Database className="w-4 h-4" />;
      case 'sync':
        return <RefreshCw className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className={`absolute right-0 top-0 h-full w-full max-w-4xl fa-glass-panel-frosted border-l border-fa-gray-200 overflow-y-auto ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 bg-fa-white-glass backdrop-blur-md border-b border-fa-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Activity className="w-6 h-6 text-fa-blue-500" />
                <h2 className="fa-heading-2">Performance Dashboard</h2>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                  isMonitoring ? 'bg-fa-green-100 text-fa-green-700' : 'bg-fa-gray-100 text-fa-gray-700'
                }`}>
                  {isMonitoring ? 'Monitoring' : 'Stopped'}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={isMonitoring ? stopMonitoring : startMonitoring}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    isMonitoring
                      ? 'bg-fa-red-100 text-fa-red-700 hover:bg-fa-red-200'
                      : 'bg-fa-green-100 text-fa-green-700 hover:bg-fa-green-200'
                  }`}
                >
                  {isMonitoring ? 'Stop' : 'Start'}
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={clearMetrics}
                  className="px-3 py-1 bg-fa-gray-100 text-fa-gray-700 rounded-lg hover:bg-fa-gray-200 text-sm font-medium"
                >
                  Clear
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4 mt-4">
              {/* Time Range Filter */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-fa-gray-600">Time:</span>
                <div className="flex space-x-1">
                  {(['1h', '6h', '24h', 'all'] as const).map((range) => (
                    <motion.button
                      key={range}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedTimeRange(range)}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                        selectedTimeRange === range
                          ? 'bg-fa-blue-100 text-fa-blue-700'
                          : 'bg-fa-gray-100 text-fa-gray-600 hover:bg-fa-gray-200'
                      }`}
                    >
                      {range.toUpperCase()}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-fa-gray-600">Category:</span>
                <div className="flex space-x-1">
                  {(['all', 'search', 'ui', 'db', 'sync'] as const).map((category) => (
                    <motion.button
                      key={category}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-2 py-1 rounded text-xs font-medium transition-colors flex items-center space-x-1 ${
                        selectedCategory === category
                          ? 'bg-fa-blue-100 text-fa-blue-700'
                          : 'bg-fa-gray-100 text-fa-gray-600 hover:bg-fa-gray-200'
                      }`}
                    >
                      {getCategoryIcon(category)}
                      <span className="capitalize">{category}</span>
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            {/* Summary Cards */}
            {filteredReport && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="fa-glass-panel p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-fa-gray-600">Total Operations</p>
                      <p className="text-2xl font-bold text-fa-gray-800">
                        {filteredReport.summary.totalOperations}
                      </p>
                    </div>
                    <Activity className="w-8 h-8 text-fa-blue-500" />
                  </div>
                </div>

                <div className="fa-glass-panel p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-fa-gray-600">Avg Duration</p>
                      <p className="text-2xl font-bold text-fa-gray-800">
                        {filteredReport.summary.averageDuration.toFixed(1)}ms
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-fa-green-500" />
                  </div>
                </div>

                <div className="fa-glass-panel p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-fa-gray-600">Warnings</p>
                      <p className="text-2xl font-bold text-fa-orange-600">
                        {filteredReport.summary.warningCount}
                      </p>
                    </div>
                    <AlertTriangle className="w-8 h-8 text-fa-orange-500" />
                  </div>
                </div>

                <div className="fa-glass-panel p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-fa-gray-600">Critical Issues</p>
                      <p className="text-2xl font-bold text-fa-red-600">
                        {filteredReport.summary.criticalCount}
                      </p>
                    </div>
                    <AlertTriangle className="w-8 h-8 text-fa-red-500" />
                  </div>
                </div>
              </div>
            )}

            {/* Recent Alerts */}
            {alerts.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4 flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2 text-fa-orange-500" />
                  Recent Alerts
                </h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {alerts.slice(-10).reverse().map((alert, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-3 rounded-lg border-l-4 ${
                        alert.type === 'critical'
                          ? 'bg-fa-red-50 border-fa-red-400'
                          : 'bg-fa-orange-50 border-fa-orange-400'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={`text-sm font-medium ${
                            alert.type === 'critical' ? 'text-fa-red-800' : 'text-fa-orange-800'
                          }`}>
                            {alert.message}
                          </p>
                          <p className="text-xs text-fa-gray-600 mt-1">
                            {formatDistanceToNow(alert.timestamp, { addSuffix: true })}
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          alert.type === 'critical'
                            ? 'bg-fa-red-100 text-fa-red-700'
                            : 'bg-fa-orange-100 text-fa-orange-700'
                        }`}>
                          {alert.type}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Metrics */}
            <div className="fa-glass-panel p-4">
              <h3 className="fa-heading-3 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-fa-blue-500" />
                Recent Metrics
              </h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredMetrics.slice(-20).reverse().map((metric, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.02 }}
                    className="flex items-center justify-between p-2 hover:bg-fa-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        metric.status === 'good' ? 'bg-fa-green-500' :
                        metric.status === 'warning' ? 'bg-fa-orange-500' : 'bg-fa-red-500'
                      }`} />
                      <div>
                        <p className="text-sm font-medium text-fa-gray-800 capitalize">
                          {metric.name.replace(/_/g, ' ')}
                        </p>
                        <p className="text-xs text-fa-gray-600">
                          {formatDistanceToNow(metric.timestamp, { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-fa-gray-700">
                        {metric.duration.toFixed(1)}ms
                      </span>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metric.status)}`}>
                        {metric.status}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Recommendations */}
            {filteredReport && filteredReport.recommendations.length > 0 && (
              <div className="fa-glass-panel p-4">
                <h3 className="fa-heading-3 mb-4 flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-fa-purple-500" />
                  Performance Recommendations
                </h3>
                <div className="space-y-2">
                  {filteredReport.recommendations.map((recommendation, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 bg-fa-blue-50 border-l-4 border-fa-blue-400 rounded-lg"
                    >
                      <p className="text-sm text-fa-blue-800">{recommendation}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Empty State */}
            {filteredMetrics.length === 0 && (
              <div className="text-center py-12">
                <Activity className="w-12 h-12 text-fa-gray-300 mx-auto mb-4" />
                <h3 className="fa-heading-3 text-fa-gray-500 mb-2">No Performance Data</h3>
                <p className="text-fa-gray-400">
                  {isMonitoring 
                    ? 'Performance monitoring is active. Data will appear as operations are performed.'
                    : 'Start monitoring to see performance metrics.'
                  }
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
