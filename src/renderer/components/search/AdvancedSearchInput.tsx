import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Filter, Command, Clock, Zap } from 'lucide-react';
import { SearchSuggestion, SearchMetrics } from '@renderer/services/search-engine.service';
import { useDebounce } from '@renderer/hooks/useDebounce';

export interface AdvancedSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  onFilterToggle?: () => void;
  placeholder?: string;
  showFilterButton?: boolean;
  isFilterActive?: boolean;
  suggestions?: SearchSuggestion[];
  isLoading?: boolean;
  metrics?: SearchMetrics;
  className?: string;
  showMetrics?: boolean;
  enableVoiceSearch?: boolean;
}

export const AdvancedSearchInput: React.FC<AdvancedSearchInputProps> = ({
  value,
  onChange,
  onSearch,
  onFilterToggle,
  placeholder = 'Search todos with smart suggestions...',
  showFilterButton = true,
  isFilterActive = false,
  suggestions = [],
  isLoading = false,
  metrics,
  className = '',
  showMetrics = false,
  enableVoiceSearch = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const debouncedValue = useDebounce(value, 300);

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('todo-search-history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  // Save search history
  const saveToHistory = useCallback((query: string) => {
    if (query.trim() && !searchHistory.includes(query)) {
      const newHistory = [query, ...searchHistory.slice(0, 9)]; // Keep last 10 searches
      setSearchHistory(newHistory);
      localStorage.setItem('todo-search-history', JSON.stringify(newHistory));
    }
  }, [searchHistory]);

  // Trigger search when debounced value changes
  useEffect(() => {
    if (debouncedValue.trim()) {
      onSearch(debouncedValue);
    }
  }, [debouncedValue, onSearch]);

  // Filter suggestions based on current input
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.text.toLowerCase().includes(value.toLowerCase()) && suggestion.text !== value
  ).slice(0, 8);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setSelectedSuggestionIndex(-1);
    setShowSuggestions(newValue.length > 0 && (filteredSuggestions.length > 0 || searchHistory.length > 0));
    setShowHistory(newValue.length === 0 && searchHistory.length > 0);
  };

  // Handle input focus
  const handleFocus = () => {
    setIsFocused(true);
    if (value.length === 0 && searchHistory.length > 0) {
      setShowHistory(true);
    } else if (filteredSuggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Handle input blur
  const handleBlur = () => {
    // Delay to allow suggestion clicks
    setTimeout(() => {
      setIsFocused(false);
      setShowSuggestions(false);
      setShowHistory(false);
      setSelectedSuggestionIndex(-1);
    }, 200);
  };

  // Handle clear search
  const handleClear = () => {
    onChange('');
    setShowSuggestions(false);
    setShowHistory(false);
    inputRef.current?.focus();
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: SearchSuggestion | string) => {
    const query = typeof suggestion === 'string' ? suggestion : suggestion.text;
    onChange(query);
    onSearch(query);
    saveToHistory(query);
    setShowSuggestions(false);
    setShowHistory(false);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = showHistory ? searchHistory.length : filteredSuggestions.length;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < totalItems - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          const item = showHistory 
            ? searchHistory[selectedSuggestionIndex]
            : filteredSuggestions[selectedSuggestionIndex];
          handleSuggestionSelect(item);
        } else if (value.trim()) {
          onSearch(value);
          saveToHistory(value);
          setShowSuggestions(false);
          setShowHistory(false);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setShowHistory(false);
        setSelectedSuggestionIndex(-1);
        inputRef.current?.blur();
        break;
      case 'Tab':
        if (selectedSuggestionIndex >= 0) {
          e.preventDefault();
          const item = showHistory 
            ? searchHistory[selectedSuggestionIndex]
            : filteredSuggestions[selectedSuggestionIndex];
          handleSuggestionSelect(item);
        }
        break;
    }
  };

  // Get suggestion icon based on type
  const getSuggestionIcon = (suggestion: SearchSuggestion) => {
    switch (suggestion.type) {
      case 'query':
        return <Search className="w-4 h-4" />;
      case 'filter':
        return <Filter className="w-4 h-4" />;
      case 'category':
        return <Command className="w-4 h-4" />;
      case 'tag':
        return <Zap className="w-4 h-4" />;
      default:
        return <Search className="w-4 h-4" />;
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className={`fa-glass-panel relative transition-all duration-300 ${
        isFocused ? 'ring-2 ring-fa-blue-400 ring-opacity-50 shadow-lg' : ''
      }`}>
        <div className="flex items-center">
          {/* Search Icon */}
          <div className="flex-shrink-0 pl-4">
            <Search className={`w-5 h-5 transition-colors duration-200 ${
              isFocused ? 'text-fa-blue-500' : 'text-fa-gray-400'
            }`} />
          </div>

          {/* Input */}
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="flex-1 bg-transparent px-4 py-3 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"
          />

          {/* Loading Indicator */}
          {isLoading && (
            <div className="flex-shrink-0 px-2">
              <div className="fa-spinner w-4 h-4 text-fa-blue-500" />
            </div>
          )}

          {/* Clear Button */}
          {value && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleClear}
              className="flex-shrink-0 p-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}

          {/* Filter Button */}
          {showFilterButton && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onFilterToggle}
              className={`flex-shrink-0 p-2 mx-2 rounded-lg transition-all duration-200 ${
                isFilterActive
                  ? 'bg-fa-blue-100 text-fa-blue-600 shadow-sm'
                  : 'text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass'
              }`}
            >
              <Filter className="w-4 h-4" />
            </motion.button>
          )}
        </div>

        {/* Search Metrics */}
        {showMetrics && metrics && (
          <div className="px-4 pb-2 text-xs text-fa-gray-500 flex items-center space-x-4">
            <span>{metrics.totalResults} results</span>
            <span>{metrics.duration.toFixed(1)}ms</span>
            {metrics.queryComplexity > 5 && (
              <span className="text-fa-orange-500">Complex query</span>
            )}
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {(showSuggestions || showHistory) && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 fa-glass-panel-frosted border border-fa-gray-200 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto"
          >
            {showHistory && searchHistory.length > 0 && (
              <div className="p-2">
                <div className="px-3 py-2 text-xs font-medium text-fa-gray-500 flex items-center">
                  <Clock className="w-3 h-3 mr-2" />
                  Recent Searches
                </div>
                {searchHistory.slice(0, 5).map((historyItem, index) => (
                  <motion.button
                    key={`history-${index}`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleSuggestionSelect(historyItem)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                      selectedSuggestionIndex === index
                        ? 'bg-fa-blue-100 text-fa-blue-800'
                        : 'hover:bg-fa-gray-50'
                    }`}
                  >
                    <Clock className="w-4 h-4 text-fa-gray-400" />
                    <span className="truncate">{historyItem}</span>
                  </motion.button>
                ))}
              </div>
            )}

            {showSuggestions && filteredSuggestions.length > 0 && (
              <div className="p-2">
                {searchHistory.length > 0 && (
                  <div className="border-t border-fa-gray-200 my-2" />
                )}
                <div className="px-3 py-2 text-xs font-medium text-fa-gray-500">
                  Suggestions
                </div>
                {filteredSuggestions.map((suggestion, index) => (
                  <motion.button
                    key={`suggestion-${index}`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleSuggestionSelect(suggestion)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                      selectedSuggestionIndex === index + (showHistory ? searchHistory.length : 0)
                        ? 'bg-fa-blue-100 text-fa-blue-800'
                        : 'hover:bg-fa-gray-50'
                    }`}
                  >
                    <div className="text-fa-gray-400">
                      {getSuggestionIcon(suggestion)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="truncate">{suggestion.text}</div>
                      {suggestion.type !== 'query' && (
                        <div className="text-xs text-fa-gray-500 capitalize">
                          {suggestion.type}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-fa-gray-400">
                      {Math.round(suggestion.score * 100)}%
                    </div>
                  </motion.button>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
