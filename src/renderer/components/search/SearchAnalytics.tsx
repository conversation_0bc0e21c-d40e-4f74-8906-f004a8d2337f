import React from 'react';
import { motion } from 'framer-motion';
import { Bar<PERSON>hart3, Clock, Target, Zap, TrendingUp, Filter } from 'lucide-react';
import { SearchMetrics } from '@renderer/services/search-engine.service';

export interface SearchAnalyticsData {
  totalResults: number;
  categories: { name: string; count: number; color?: string }[];
  avgRelevance: number;
  searchTime: number;
  queryComplexity: number;
  topMatches: { field: string; count: number }[];
}

export interface SearchAnalyticsProps {
  analytics: SearchAnalyticsData;
  metrics?: SearchMetrics;
  className?: string;
  compact?: boolean;
}

export const SearchAnalytics: React.FC<SearchAnalyticsProps> = ({
  analytics,
  metrics,
  className = '',
  compact = false,
}) => {
  const getPerformanceColor = (time: number) => {
    if (time < 50) return 'text-fa-green-600 bg-fa-green-100';
    if (time < 100) return 'text-fa-blue-600 bg-fa-blue-100';
    if (time < 200) return 'text-fa-orange-600 bg-fa-orange-100';
    return 'text-fa-red-600 bg-fa-red-100';
  };

  const getRelevanceColor = (relevance: number) => {
    if (relevance > 0.8) return 'text-fa-green-600 bg-fa-green-100';
    if (relevance > 0.6) return 'text-fa-blue-600 bg-fa-blue-100';
    if (relevance > 0.4) return 'text-fa-orange-600 bg-fa-orange-100';
    return 'text-fa-red-600 bg-fa-red-100';
  };

  const getComplexityLevel = (complexity: number) => {
    if (complexity <= 2) return { label: 'Simple', color: 'text-fa-green-600 bg-fa-green-100' };
    if (complexity <= 5) return { label: 'Medium', color: 'text-fa-blue-600 bg-fa-blue-100' };
    if (complexity <= 8) return { label: 'Complex', color: 'text-fa-orange-600 bg-fa-orange-100' };
    return { label: 'Very Complex', color: 'text-fa-red-600 bg-fa-red-100' };
  };

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex items-center space-x-4 text-sm ${className}`}
      >
        <div className="flex items-center space-x-1">
          <Target className="w-4 h-4 text-fa-gray-400" />
          <span className="text-fa-gray-600">{analytics.totalResults} results</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <Clock className="w-4 h-4 text-fa-gray-400" />
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(analytics.searchTime)}`}>
            {analytics.searchTime.toFixed(1)}ms
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          <Zap className="w-4 h-4 text-fa-gray-400" />
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRelevanceColor(analytics.avgRelevance)}`}>
            {Math.round(analytics.avgRelevance * 100)}% relevance
          </span>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`fa-glass-panel p-4 ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="fa-heading-3 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-fa-blue-500" />
          Search Analytics
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Results */}
        <div className="bg-fa-white-glass p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-fa-gray-600">Total Results</p>
              <p className="text-2xl font-bold text-fa-gray-800">{analytics.totalResults}</p>
            </div>
            <Target className="w-8 h-8 text-fa-blue-500" />
          </div>
        </div>

        {/* Search Time */}
        <div className="bg-fa-white-glass p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-fa-gray-600">Search Time</p>
              <p className={`text-2xl font-bold ${getPerformanceColor(analytics.searchTime).split(' ')[0]}`}>
                {analytics.searchTime.toFixed(1)}ms
              </p>
            </div>
            <Clock className="w-8 h-8 text-fa-green-500" />
          </div>
        </div>

        {/* Average Relevance */}
        <div className="bg-fa-white-glass p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-fa-gray-600">Avg Relevance</p>
              <p className={`text-2xl font-bold ${getRelevanceColor(analytics.avgRelevance).split(' ')[0]}`}>
                {Math.round(analytics.avgRelevance * 100)}%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-fa-purple-500" />
          </div>
        </div>

        {/* Query Complexity */}
        <div className="bg-fa-white-glass p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-fa-gray-600">Complexity</p>
              <p className={`text-lg font-bold ${getComplexityLevel(analytics.queryComplexity).color.split(' ')[0]}`}>
                {getComplexityLevel(analytics.queryComplexity).label}
              </p>
            </div>
            <Zap className="w-8 h-8 text-fa-orange-500" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Distribution */}
        {analytics.categories.length > 0 && (
          <div className="bg-fa-white-glass p-4 rounded-lg">
            <h4 className="fa-heading-4 mb-3 flex items-center">
              <Filter className="w-4 h-4 mr-2" />
              Results by Category
            </h4>
            <div className="space-y-2">
              {analytics.categories.slice(0, 5).map((category, index) => {
                const percentage = (category.count / analytics.totalResults) * 100;
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 flex-1">
                      <div
                        className="w-3 h-3 rounded"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      />
                      <span className="text-sm text-fa-gray-700 truncate">
                        {category.name || 'Uncategorized'}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-fa-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${percentage}%` }}
                          transition={{ delay: index * 0.1 }}
                          className="h-2 rounded-full"
                          style={{ backgroundColor: category.color || '#3B82F6' }}
                        />
                      </div>
                      <span className="text-sm text-fa-gray-600 w-8 text-right">
                        {category.count}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Top Match Fields */}
        {analytics.topMatches.length > 0 && (
          <div className="bg-fa-white-glass p-4 rounded-lg">
            <h4 className="fa-heading-4 mb-3 flex items-center">
              <Target className="w-4 h-4 mr-2" />
              Top Match Fields
            </h4>
            <div className="space-y-2">
              {analytics.topMatches.slice(0, 5).map((match, index) => {
                const percentage = (match.count / analytics.totalResults) * 100;
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-fa-gray-700 capitalize flex-1">
                      {match.field}
                    </span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-fa-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${percentage}%` }}
                          transition={{ delay: index * 0.1 }}
                          className="h-2 bg-fa-blue-500 rounded-full"
                        />
                      </div>
                      <span className="text-sm text-fa-gray-600 w-8 text-right">
                        {match.count}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Performance Insights */}
      {metrics && (
        <div className="mt-6 p-3 bg-fa-blue-50 rounded-lg">
          <h4 className="fa-heading-4 mb-2 text-fa-blue-800">Performance Insights</h4>
          <div className="text-sm text-fa-blue-700 space-y-1">
            {metrics.duration < 50 && (
              <p>✅ Excellent search performance - under 50ms</p>
            )}
            {metrics.duration >= 50 && metrics.duration < 100 && (
              <p>⚡ Good search performance - under 100ms</p>
            )}
            {metrics.duration >= 100 && (
              <p>⚠️ Search could be faster - consider optimizing query</p>
            )}
            {analytics.avgRelevance > 0.8 && (
              <p>🎯 High relevance scores - great search quality</p>
            )}
            {analytics.avgRelevance < 0.5 && (
              <p>💡 Low relevance - try more specific search terms</p>
            )}
            {analytics.queryComplexity > 8 && (
              <p>🔍 Complex query detected - results may take longer</p>
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
};
