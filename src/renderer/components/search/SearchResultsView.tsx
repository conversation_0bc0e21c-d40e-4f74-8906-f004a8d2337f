import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Target, Zap, Clock, Tag, Folder } from 'lucide-react';
import { SearchResult, SearchHighlight, MatchReason } from '@renderer/services/search-engine.service';
import { Todo } from '@shared/types';
import { PriorityIndicator } from '@renderer/components/ui/PriorityIndicator';
import { formatDistanceToNow } from 'date-fns';

export interface SearchResultsViewProps {
  results: SearchResult[];
  query: string;
  onResultClick: (todo: Todo) => void;
  onResultEdit?: (todo: Todo) => void;
  className?: string;
  showMatchReasons?: boolean;
  showSnippets?: boolean;
  maxResults?: number;
}

export const SearchResultsView: React.FC<SearchResultsViewProps> = ({
  results,
  query,
  onResultClick,
  onResultEdit,
  className = '',
  showMatchReasons = true,
  showSnippets = true,
  maxResults = 50,
}) => {
  const displayResults = useMemo(() => {
    return results.slice(0, maxResults);
  }, [results, maxResults]);

  const highlightText = (text: string, highlights: SearchHighlight[], field: string): React.ReactNode => {
    if (!highlights.length) return text;

    const fieldHighlights = highlights.filter(h => h.field === field);
    if (!fieldHighlights.length) return text;

    let lastIndex = 0;
    const parts: React.ReactNode[] = [];

    fieldHighlights
      .sort((a, b) => a.start - b.start)
      .forEach((highlight, index) => {
        // Add text before highlight
        if (highlight.start > lastIndex) {
          parts.push(text.substring(lastIndex, highlight.start));
        }

        // Add highlighted text
        parts.push(
          <mark
            key={`highlight-${index}`}
            className="bg-fa-yellow-200 text-fa-yellow-900 px-1 rounded font-medium"
          >
            {text.substring(highlight.start, highlight.end)}
          </mark>
        );

        lastIndex = highlight.end;
      });

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }

    return parts;
  };

  const getMatchReasonIcon = (reason: MatchReason) => {
    switch (reason.type) {
      case 'exact':
        return <Target className="w-3 h-3 text-fa-green-500" />;
      case 'partial':
        return <Search className="w-3 h-3 text-fa-blue-500" />;
      case 'fuzzy':
        return <Zap className="w-3 h-3 text-fa-orange-500" />;
      default:
        return <Search className="w-3 h-3 text-fa-gray-400" />;
    }
  };

  const getMatchReasonLabel = (reason: MatchReason) => {
    switch (reason.type) {
      case 'exact':
        return 'Exact match';
      case 'partial':
        return 'Partial match';
      case 'fuzzy':
        return 'Similar match';
      default:
        return 'Match';
    }
  };

  const getRelevanceColor = (score: number) => {
    if (score > 0.8) return 'text-fa-green-600 bg-fa-green-100';
    if (score > 0.5) return 'text-fa-blue-600 bg-fa-blue-100';
    if (score > 0.3) return 'text-fa-orange-600 bg-fa-orange-100';
    return 'text-fa-gray-600 bg-fa-gray-100';
  };

  if (results.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Search className="w-12 h-12 text-fa-gray-300 mx-auto mb-4" />
        <h3 className="fa-heading-3 text-fa-gray-500 mb-2">No results found</h3>
        <p className="text-fa-gray-400">
          Try adjusting your search terms or filters
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Search className="w-5 h-5 text-fa-gray-400" />
          <span className="text-fa-gray-600">
            {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
          </span>
        </div>
        {results.length > maxResults && (
          <span className="text-sm text-fa-gray-500">
            Showing first {maxResults} results
          </span>
        )}
      </div>

      {/* Results List */}
      <div className="space-y-3">
        <AnimatePresence mode="popLayout">
          {displayResults.map((result, index) => (
            <motion.div
              key={result.item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className="fa-glass-panel p-4 hover:shadow-lg transition-all duration-200 cursor-pointer group"
              onClick={() => onResultClick(result.item)}
            >
              {/* Result Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h3 className="fa-heading-3 mb-1 group-hover:text-fa-blue-600 transition-colors">
                    {highlightText(result.item.title, result.highlights, 'title')}
                  </h3>
                  
                  {/* Metadata */}
                  <div className="flex items-center space-x-4 text-sm text-fa-gray-500">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>
                        {formatDistanceToNow(new Date(result.item.created_at), { addSuffix: true })}
                      </span>
                    </div>
                    
                    {result.item.category_id && (
                      <div className="flex items-center space-x-1">
                        <Folder className="w-3 h-3" />
                        <span>Category</span>
                      </div>
                    )}
                    
                    {result.item.tags && result.item.tags.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Tag className="w-3 h-3" />
                        <span>{result.item.tags.length} tag{result.item.tags.length !== 1 ? 's' : ''}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Priority and Relevance */}
                <div className="flex items-center space-x-3 ml-4">
                  <PriorityIndicator
                    priority={result.item.priority}
                    variant="badge"
                    size="sm"
                  />
                  
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getRelevanceColor(result.score)}`}>
                    {Math.round(result.score * 100)}%
                  </div>
                </div>
              </div>

              {/* Description with Highlights */}
              {result.item.description && (
                <div className="mb-3">
                  <p className="text-fa-gray-700 text-sm leading-relaxed">
                    {highlightText(result.item.description, result.highlights, 'description')}
                  </p>
                </div>
              )}

              {/* Tags with Highlights */}
              {result.item.tags && result.item.tags.length > 0 && (
                <div className="mb-3">
                  <div className="flex flex-wrap gap-2">
                    {result.item.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-fa-blue-50 text-fa-blue-700 text-xs rounded-full"
                      >
                        {highlightText(tag, result.highlights, 'tags')}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Search Snippets */}
              {showSnippets && result.highlights.length > 0 && (
                <div className="mb-3">
                  <div className="space-y-1">
                    {result.highlights.slice(0, 3).map((highlight, highlightIndex) => (
                      <div
                        key={highlightIndex}
                        className="text-xs text-fa-gray-600 bg-fa-gray-50 p-2 rounded border-l-2 border-fa-blue-300"
                      >
                        <span className="font-medium text-fa-gray-700 capitalize">
                          {highlight.field}:
                        </span>{' '}
                        {highlight.snippet}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Match Reasons */}
              {showMatchReasons && result.matchReasons.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {result.matchReasons.slice(0, 4).map((reason, reasonIndex) => (
                    <div
                      key={reasonIndex}
                      className="flex items-center space-x-1 px-2 py-1 bg-fa-white-glass rounded-full text-xs"
                    >
                      {getMatchReasonIcon(reason)}
                      <span className="text-fa-gray-600">
                        {getMatchReasonLabel(reason)} in {reason.field}
                      </span>
                    </div>
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onResultClick(result.item);
                  }}
                  className="px-3 py-1 text-xs bg-fa-blue-100 text-fa-blue-700 rounded-full hover:bg-fa-blue-200 transition-colors"
                >
                  View
                </motion.button>
                
                {onResultEdit && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onResultEdit(result.item);
                    }}
                    className="px-3 py-1 text-xs bg-fa-gray-100 text-fa-gray-700 rounded-full hover:bg-fa-gray-200 transition-colors"
                  >
                    Edit
                  </motion.button>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Load More */}
      {results.length > maxResults && (
        <div className="text-center pt-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-2 bg-fa-blue-100 text-fa-blue-700 rounded-lg hover:bg-fa-blue-200 transition-colors"
          >
            Load More Results
          </motion.button>
        </div>
      )}
    </div>
  );
};
