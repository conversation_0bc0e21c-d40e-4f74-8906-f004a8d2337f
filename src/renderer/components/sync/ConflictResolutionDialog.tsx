import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Merge,
  Check,
  Clock,
  User,
  Globe,
  RefreshCw
} from 'lucide-react';
import { useConflictResolution } from '@renderer/stores';
import { ConflictData } from '@renderer/stores/syncStore';

interface ConflictFieldComparison {
  field: string;
  localValue: any;
  remoteValue: any;
  isDifferent: boolean;
  selectedValue: 'local' | 'remote' | 'custom';
  customValue?: any;
}

export const ConflictResolutionDialog: React.FC = () => {
  const {
    activeConflict,
    showConflictDialog,
    isResolvingConflict,
    hideConflictResolution,
    resolveConflict,
    resolveConflictManually
  } = useConflictResolution();

  const [resolutionStrategy, setResolutionStrategy] = useState<'local' | 'remote' | 'merge'>('merge');
  const [fieldComparisons, setFieldComparisons] = useState<ConflictFieldComparison[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Initialize field comparisons when conflict changes
  React.useEffect(() => {
    if (activeConflict) {
      const localData = activeConflict.localData || {};
      const remoteData = activeConflict.remoteData || {};
      
      const allFields = new Set([
        ...Object.keys(localData),
        ...Object.keys(remoteData)
      ]);

      const comparisons: ConflictFieldComparison[] = Array.from(allFields).map(field => {
        const localValue = localData[field];
        const remoteValue = remoteData[field];
        const isDifferent = JSON.stringify(localValue) !== JSON.stringify(remoteValue);
        
        return {
          field,
          localValue,
          remoteValue,
          isDifferent,
          selectedValue: isDifferent ? 'local' : 'local', // Default to local for conflicts
        };
      });

      setFieldComparisons(comparisons);
    }
  }, [activeConflict]);

  const mergedData = useMemo(() => {
    if (!activeConflict) return {};
    
    const result: any = {};
    fieldComparisons.forEach(comparison => {
      switch (comparison.selectedValue) {
        case 'local':
          result[comparison.field] = comparison.localValue;
          break;
        case 'remote':
          result[comparison.field] = comparison.remoteValue;
          break;
        case 'custom':
          result[comparison.field] = comparison.customValue;
          break;
      }
    });
    
    return result;
  }, [fieldComparisons, activeConflict]);

  const handleFieldSelection = (fieldIndex: number, selection: 'local' | 'remote' | 'custom', customValue?: any) => {
    setFieldComparisons(prev => prev.map((comp, index) => 
      index === fieldIndex 
        ? { ...comp, selectedValue: selection, customValue }
        : comp
    ));
  };

  const handleResolve = async () => {
    if (!activeConflict) return;

    try {
      switch (resolutionStrategy) {
        case 'local':
          await resolveConflict(activeConflict.id, 'local');
          break;
        case 'remote':
          await resolveConflict(activeConflict.id, 'remote');
          break;
        case 'merge':
          await resolveConflictManually(activeConflict.id, mergedData);
          break;
      }
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
    }
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return 'null';
    if (typeof value === 'string') return value;
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'number') return value.toString();
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  };

  const getConflictTypeColor = (type: string) => {
    switch (type) {
      case 'update-update':
        return 'text-fa-warning';
      case 'update-delete':
        return 'text-fa-error';
      case 'create-create':
        return 'text-fa-info';
      default:
        return 'text-fa-gray-600';
    }
  };

  if (!showConflictDialog || !activeConflict) {
    return null;
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className="fa-glass-panel-frosted border border-fa-border rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-fa-border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-fa-warning-bg">
                <AlertTriangle className="w-5 h-5 text-fa-warning" />
              </div>
              <div>
                <h2 className="fa-heading-3 text-fa-gray-800">Resolve Sync Conflict</h2>
                <p className="fa-text-sm text-fa-gray-600">
                  {activeConflict.tableName} • {activeConflict.recordId}
                </p>
              </div>
            </div>
            <button
              onClick={hideConflictResolution}
              className="p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200"
            >
              <X className="w-5 h-5 text-fa-gray-600" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            {/* Conflict Info */}
            <div className="mb-6 p-4 rounded-lg fa-glass-panel-subtle">
              <div className="flex items-center justify-between mb-3">
                <span className="fa-text-sm font-medium text-fa-gray-700">Conflict Type</span>
                <span className={`fa-text-sm font-semibold ${getConflictTypeColor(activeConflict.conflictType)}`}>
                  {activeConflict.conflictType.replace('-', ' → ')}
                </span>
              </div>
              
              {activeConflict.recommendations.length > 0 && (
                <div>
                  <span className="fa-text-sm font-medium text-fa-gray-700 block mb-2">Recommendations</span>
                  <ul className="space-y-1">
                    {activeConflict.recommendations.map((rec, index) => (
                      <li key={index} className="fa-text-xs text-fa-gray-600 flex items-start space-x-2">
                        <span className="text-fa-info">•</span>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Resolution Strategy */}
            <div className="mb-6">
              <h3 className="fa-heading-4 text-fa-gray-800 mb-3">Resolution Strategy</h3>
              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => setResolutionStrategy('local')}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    resolutionStrategy === 'local'
                      ? 'border-fa-info bg-fa-info-bg'
                      : 'border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <User className="w-4 h-4 text-fa-info" />
                    <span className="fa-text-sm font-medium text-fa-gray-800">Keep Local</span>
                  </div>
                  <p className="fa-text-xs text-fa-gray-600">Use your local changes</p>
                </button>

                <button
                  onClick={() => setResolutionStrategy('remote')}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    resolutionStrategy === 'remote'
                      ? 'border-fa-info bg-fa-info-bg'
                      : 'border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <Globe className="w-4 h-4 text-fa-info" />
                    <span className="fa-text-sm font-medium text-fa-gray-800">Keep Remote</span>
                  </div>
                  <p className="fa-text-xs text-fa-gray-600">Use remote changes</p>
                </button>

                <button
                  onClick={() => setResolutionStrategy('merge')}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    resolutionStrategy === 'merge'
                      ? 'border-fa-info bg-fa-info-bg'
                      : 'border-fa-border fa-glass-panel-subtle hover:fa-glass-panel-frosted'
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <Merge className="w-4 h-4 text-fa-info" />
                    <span className="fa-text-sm font-medium text-fa-gray-800">Merge</span>
                  </div>
                  <p className="fa-text-xs text-fa-gray-600">Combine both changes</p>
                </button>
              </div>
            </div>

            {/* Field-by-field comparison for merge strategy */}
            {resolutionStrategy === 'merge' && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="fa-heading-4 text-fa-gray-800">Field Comparison</h3>
                  <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="fa-text-sm text-fa-info hover:text-fa-info-hover transition-colors duration-200"
                  >
                    {showAdvanced ? 'Hide' : 'Show'} Advanced
                  </button>
                </div>

                <div className="space-y-3">
                  {fieldComparisons
                    .filter(comp => showAdvanced || comp.isDifferent)
                    .map((comparison, index) => (
                    <div key={comparison.field} className="fa-glass-panel-subtle rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="fa-text-sm font-medium text-fa-gray-800">
                          {comparison.field}
                        </span>
                        {comparison.isDifferent && (
                          <span className="px-2 py-1 rounded-full bg-fa-warning-bg text-fa-warning fa-text-xs font-medium">
                            Conflict
                          </span>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        {/* Local Value */}
                        <div className={`p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                          comparison.selectedValue === 'local'
                            ? 'border-fa-info bg-fa-info-bg'
                            : 'border-fa-border hover:border-fa-info-border'
                        }`}
                        onClick={() => handleFieldSelection(index, 'local')}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <User className="w-3 h-3 text-fa-info" />
                            <span className="fa-text-xs font-medium text-fa-gray-700">Local</span>
                          </div>
                          <div className="fa-text-sm text-fa-gray-800 font-mono bg-fa-white-glass rounded p-2">
                            {formatValue(comparison.localValue)}
                          </div>
                        </div>

                        {/* Remote Value */}
                        <div className={`p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                          comparison.selectedValue === 'remote'
                            ? 'border-fa-info bg-fa-info-bg'
                            : 'border-fa-border hover:border-fa-info-border'
                        }`}
                        onClick={() => handleFieldSelection(index, 'remote')}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <Globe className="w-3 h-3 text-fa-info" />
                            <span className="fa-text-xs font-medium text-fa-gray-700">Remote</span>
                          </div>
                          <div className="fa-text-sm text-fa-gray-800 font-mono bg-fa-white-glass rounded p-2">
                            {formatValue(comparison.remoteValue)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-fa-border">
            <div className="flex items-center space-x-2 text-fa-gray-600">
              <Clock className="w-4 h-4" />
              <span className="fa-text-sm">
                Created {new Date(activeConflict.createdAt).toLocaleString()}
              </span>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={hideConflictResolution}
                className="px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleResolve}
                disabled={isResolvingConflict}
                className="px-4 py-2 bg-fa-info hover:bg-fa-info-hover disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-white transition-all duration-200 flex items-center space-x-2"
              >
                {isResolvingConflict ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    >
                      <RefreshCw className="w-4 h-4" />
                    </motion.div>
                    <span>Resolving...</span>
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4" />
                    <span>Resolve Conflict</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
