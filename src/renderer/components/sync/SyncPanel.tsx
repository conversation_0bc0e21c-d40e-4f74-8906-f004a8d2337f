import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  RefreshCw, 
  Upload, 
  Download, 
  Trash2, 
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  Settings,
  Activity
} from 'lucide-react';
import { useSyncStore, useConflictResolution } from '@renderer/stores';

export const SyncPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'status' | 'queue' | 'conflicts' | 'settings'>('status');
  
  const {
    syncStatus,
    queueStatus,
    networkStatus,
    showSyncPanel,
    toggleSyncPanel,
    performSync,
    forcePushChanges,
    forcePullChanges,
    clearSyncQueue,
    autoSyncEnabled,
    syncInterval,
    setAutoSync,
    setSyncInterval
  } = useSyncStore();

  const {
    conflicts,
    showConflictResolution,
    loadConflicts
  } = useConflictResolution();

  const handleSync = async () => {
    try {
      await performSync();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  const handleForcePush = async () => {
    try {
      await forcePushChanges();
    } catch (error) {
      console.error('Force push failed:', error);
    }
  };

  const handleForcePull = async () => {
    try {
      await forcePullChanges();
    } catch (error) {
      console.error('Force pull failed:', error);
    }
  };

  const handleClearQueue = async () => {
    try {
      await clearSyncQueue();
    } catch (error) {
      console.error('Clear queue failed:', error);
    }
  };

  if (!showSyncPanel) {
    return null;
  }

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-40 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className="fa-glass-panel-frosted border border-fa-border rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-fa-border">
            <h2 className="fa-heading-3 text-fa-gray-800">Sync Management</h2>
            <button
              onClick={toggleSyncPanel}
              className="p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200"
            >
              <X className="w-5 h-5 text-fa-gray-600" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-fa-border">
            {[
              { id: 'status', label: 'Status', icon: Activity },
              { id: 'queue', label: 'Queue', icon: Clock },
              { id: 'conflicts', label: 'Conflicts', icon: AlertTriangle, badge: conflicts.length },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-6 py-3 border-b-2 transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'border-fa-info text-fa-info'
                    : 'border-transparent text-fa-gray-600 hover:text-fa-gray-800'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="fa-text-sm font-medium">{tab.label}</span>
                {tab.badge !== undefined && tab.badge > 0 && (
                  <span className="px-2 py-1 rounded-full bg-fa-error text-white fa-text-xs font-medium">
                    {tab.badge}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            {/* Status Tab */}
            {activeTab === 'status' && (
              <div className="space-y-6">
                {/* Network Status */}
                <div className="fa-glass-panel-subtle rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="fa-heading-4 text-fa-gray-800">Network Status</h3>
                    <div className="flex items-center space-x-2">
                      {networkStatus?.isOnline ? (
                        <Wifi className="w-4 h-4 text-fa-success" />
                      ) : (
                        <WifiOff className="w-4 h-4 text-fa-error" />
                      )}
                      <span className={`fa-text-sm font-medium ${
                        networkStatus?.isOnline ? 'text-fa-success' : 'text-fa-error'
                      }`}>
                        {networkStatus?.isOnline ? 'Online' : 'Offline'}
                      </span>
                    </div>
                  </div>
                  
                  {networkStatus && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="fa-text-xs text-fa-gray-600">Connection Quality</span>
                        <div className={`fa-text-sm font-semibold ${
                          networkStatus.connectionQuality === 'excellent' ? 'text-fa-success' :
                          networkStatus.connectionQuality === 'good' ? 'text-fa-info' :
                          networkStatus.connectionQuality === 'poor' ? 'text-fa-warning' :
                          'text-fa-error'
                        }`}>
                          {networkStatus.connectionQuality}
                        </div>
                      </div>
                      <div>
                        <span className="fa-text-xs text-fa-gray-600">Latency</span>
                        <div className="fa-text-sm font-semibold text-fa-gray-800">
                          {networkStatus.latency}ms
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Sync Status */}
                <div className="fa-glass-panel-subtle rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="fa-heading-4 text-fa-gray-800">Sync Status</h3>
                    <div className="flex items-center space-x-2">
                      {syncStatus.status === 'syncing' ? (
                        <RefreshCw className="w-4 h-4 text-fa-info animate-spin" />
                      ) : syncStatus.status === 'idle' ? (
                        <CheckCircle className="w-4 h-4 text-fa-success" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-fa-error" />
                      )}
                      <span className={`fa-text-sm font-medium ${
                        syncStatus.status === 'syncing' ? 'text-fa-info' :
                        syncStatus.status === 'idle' ? 'text-fa-success' :
                        'text-fa-error'
                      }`}>
                        {syncStatus.status}
                      </span>
                    </div>
                  </div>

                  {syncStatus.lastSyncAt && (
                    <div className="mb-4">
                      <span className="fa-text-xs text-fa-gray-600">Last Sync</span>
                      <div className="fa-text-sm text-fa-gray-800">
                        {new Date(syncStatus.lastSyncAt).toLocaleString()}
                      </div>
                    </div>
                  )}

                  {syncStatus.errorMessage && (
                    <div className="p-3 rounded-lg bg-fa-error-bg border border-fa-error-border">
                      <div className="fa-text-sm text-fa-error">{syncStatus.errorMessage}</div>
                    </div>
                  )}

                  <div className="flex space-x-3 mt-4">
                    <button
                      onClick={handleSync}
                      disabled={syncStatus.status === 'syncing' || !networkStatus?.isOnline}
                      className="flex items-center space-x-2 px-4 py-2 bg-fa-info hover:bg-fa-info-hover disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-white transition-all duration-200"
                    >
                      <RefreshCw className={`w-4 h-4 ${syncStatus.status === 'syncing' ? 'animate-spin' : ''}`} />
                      <span>Sync Now</span>
                    </button>
                    
                    <button
                      onClick={handleForcePush}
                      disabled={syncStatus.status === 'syncing' || !networkStatus?.isOnline}
                      className="flex items-center space-x-2 px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200"
                    >
                      <Upload className="w-4 h-4" />
                      <span>Force Push</span>
                    </button>
                    
                    <button
                      onClick={handleForcePull}
                      disabled={syncStatus.status === 'syncing' || !networkStatus?.isOnline}
                      className="flex items-center space-x-2 px-4 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200"
                    >
                      <Download className="w-4 h-4" />
                      <span>Force Pull</span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Queue Tab */}
            {activeTab === 'queue' && (
              <div className="space-y-6">
                <div className="fa-glass-panel-subtle rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="fa-heading-4 text-fa-gray-800">Operation Queue</h3>
                    <button
                      onClick={handleClearQueue}
                      disabled={queueStatus.totalOperations === 0}
                      className="flex items-center space-x-2 px-3 py-2 text-fa-error hover:bg-fa-error-bg disabled:opacity-50 disabled:cursor-not-allowed rounded-lg fa-text-sm font-medium transition-all duration-200"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Clear Queue</span>
                    </button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 rounded-lg fa-glass-panel-subtle">
                      <div className="fa-text-2xl font-bold text-fa-info">{queueStatus.totalOperations}</div>
                      <div className="fa-text-xs text-fa-gray-600">Total</div>
                    </div>
                    <div className="text-center p-3 rounded-lg fa-glass-panel-subtle">
                      <div className="fa-text-2xl font-bold text-fa-warning">{queueStatus.pendingOperations}</div>
                      <div className="fa-text-xs text-fa-gray-600">Pending</div>
                    </div>
                    <div className="text-center p-3 rounded-lg fa-glass-panel-subtle">
                      <div className="fa-text-2xl font-bold text-fa-error">{queueStatus.failedOperations}</div>
                      <div className="fa-text-xs text-fa-gray-600">Failed</div>
                    </div>
                    <div className="text-center p-3 rounded-lg fa-glass-panel-subtle">
                      <div className="fa-text-2xl font-bold text-fa-warning">{queueStatus.conflictOperations}</div>
                      <div className="fa-text-xs text-fa-gray-600">Conflicts</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Conflicts Tab */}
            {activeTab === 'conflicts' && (
              <div className="space-y-4">
                {conflicts.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-12 h-12 text-fa-success mx-auto mb-4" />
                    <h3 className="fa-heading-4 text-fa-gray-800 mb-2">No Conflicts</h3>
                    <p className="fa-text-sm text-fa-gray-600">All changes are synchronized successfully.</p>
                  </div>
                ) : (
                  conflicts.map((conflict) => (
                    <div key={conflict.id} className="fa-glass-panel-subtle rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="fa-text-sm font-medium text-fa-gray-800">
                            {conflict.tableName} • {conflict.recordId}
                          </h4>
                          <p className="fa-text-xs text-fa-gray-600">
                            {conflict.conflictType} • {new Date(conflict.createdAt).toLocaleString()}
                          </p>
                        </div>
                        <button
                          onClick={() => showConflictResolution(conflict)}
                          className="px-3 py-2 bg-fa-warning hover:bg-fa-warning-hover rounded-lg fa-text-sm font-medium text-white transition-all duration-200"
                        >
                          Resolve
                        </button>
                      </div>
                      
                      {conflict.recommendations.length > 0 && (
                        <div className="mt-3">
                          <p className="fa-text-xs text-fa-gray-600 mb-1">Recommendations:</p>
                          <ul className="space-y-1">
                            {conflict.recommendations.slice(0, 2).map((rec, index) => (
                              <li key={index} className="fa-text-xs text-fa-gray-600 flex items-start space-x-2">
                                <span className="text-fa-info">•</span>
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <div className="space-y-6">
                <div className="fa-glass-panel-subtle rounded-lg p-4">
                  <h3 className="fa-heading-4 text-fa-gray-800 mb-4">Sync Settings</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="fa-text-sm font-medium text-fa-gray-800">Auto Sync</span>
                        <p className="fa-text-xs text-fa-gray-600">Automatically sync changes when online</p>
                      </div>
                      <button
                        onClick={() => setAutoSync(!autoSyncEnabled)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                          autoSyncEnabled ? 'bg-fa-info' : 'bg-fa-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                            autoSyncEnabled ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>

                    <div>
                      <label className="fa-text-sm font-medium text-fa-gray-800 block mb-2">
                        Sync Interval (seconds)
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="300"
                        value={syncInterval / 1000}
                        onChange={(e) => setSyncInterval(parseInt(e.target.value) * 1000)}
                        className="w-full px-3 py-2 fa-glass-panel-subtle border border-fa-border rounded-lg fa-text-sm focus:outline-none focus:ring-2 focus:ring-fa-info focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};
