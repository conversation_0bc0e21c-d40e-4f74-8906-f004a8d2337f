import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  RefreshCw, 
  Check, 
  AlertCircle, 
  WifiOff, 
  Clock, 
  ChevronDown,
  Activity,
  Zap
} from 'lucide-react';
import { useSyncStatus } from '@renderer/stores';

export const SyncStatusIndicator: React.FC = () => {
  const [showDetails, setShowDetails] = useState(false);
  const {
    syncStatus,
    queueStatus,
    networkStatus,
    isLoadingStatus,
    toggleSyncPanel,
    performSync
  } = useSyncStatus();

  const getStatusIcon = () => {
    if (!networkStatus?.isOnline) {
      return <WifiOff className="h-4 w-4 text-fa-error" />;
    }
    
    switch (syncStatus.status) {
      case 'syncing':
        return <RefreshCw className="h-4 w-4 text-fa-info animate-spin" />;
      case 'idle':
        return queueStatus.pendingOperations > 0 
          ? <Clock className="h-4 w-4 text-fa-warning" />
          : <Check className="h-4 w-4 text-fa-success" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-fa-error" />;
      case 'offline':
        return <WifiOff className="h-4 w-4 text-fa-gray-400" />;
      default:
        return <Clock className="h-4 w-4 text-fa-gray-400" />;
    }
  };

  const getStatusText = () => {
    if (!networkStatus?.isOnline) {
      return 'Offline';
    }
    
    switch (syncStatus.status) {
      case 'syncing':
        return syncStatus.syncProgress 
          ? `Syncing ${syncStatus.syncProgress.current}/${syncStatus.syncProgress.total}`
          : 'Syncing...';
      case 'idle':
        return queueStatus.pendingOperations > 0 
          ? `${queueStatus.pendingOperations} pending`
          : 'Up to date';
      case 'error':
        return 'Sync error';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    if (!networkStatus?.isOnline) return 'text-fa-error';
    
    switch (syncStatus.status) {
      case 'syncing':
        return 'text-fa-info';
      case 'idle':
        return queueStatus.pendingOperations > 0 ? 'text-fa-warning' : 'text-fa-success';
      case 'error':
        return 'text-fa-error';
      case 'offline':
        return 'text-fa-gray-400';
      default:
        return 'text-fa-gray-400';
    }
  };

  const handleSyncClick = async () => {
    if (syncStatus.status !== 'syncing' && networkStatus?.isOnline) {
      try {
        await performSync();
      } catch (error) {
        console.error('Manual sync failed:', error);
      }
    }
  };

  return (
    <div className="relative">
      <motion.div
        className="flex items-center space-x-2 px-3 py-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200 cursor-pointer"
        onClick={() => setShowDetails(!showDetails)}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`fa-text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        
        <ChevronDown 
          className={`h-3 w-3 text-fa-gray-500 transition-transform duration-200 ${
            showDetails ? 'rotate-180' : ''
          }`} 
        />
      </motion.div>

      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full right-0 mt-2 w-80 fa-glass-panel-frosted border border-fa-border rounded-lg shadow-lg z-50"
          >
            <div className="p-4">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="fa-heading-4 text-fa-gray-800">Sync Status</h3>
                <button
                  onClick={handleSyncClick}
                  disabled={syncStatus.status === 'syncing' || !networkStatus?.isOnline}
                  className="p-2 rounded-lg fa-glass-panel-subtle hover:fa-glass-panel-frosted transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RefreshCw className={`h-4 w-4 text-fa-info ${
                    syncStatus.status === 'syncing' ? 'animate-spin' : ''
                  }`} />
                </button>
              </div>

              {/* Network Status */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="fa-text-sm font-medium text-fa-gray-700">Network</span>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      networkStatus?.isOnline ? 'bg-fa-success' : 'bg-fa-error'
                    }`} />
                    <span className="fa-text-xs text-fa-gray-600">
                      {networkStatus?.connectionQuality || 'Unknown'}
                    </span>
                  </div>
                </div>
                {networkStatus?.latency && (
                  <div className="flex items-center space-x-2">
                    <Activity className="h-3 w-3 text-fa-gray-500" />
                    <span className="fa-text-xs text-fa-gray-600">
                      {networkStatus.latency}ms latency
                    </span>
                  </div>
                )}
              </div>

              {/* Queue Status */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="fa-text-sm font-medium text-fa-gray-700">Queue</span>
                  <span className="fa-text-xs text-fa-gray-600">
                    {queueStatus.totalOperations} total
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="fa-glass-panel-subtle rounded-lg p-2">
                    <div className="fa-text-xs text-fa-gray-600">Pending</div>
                    <div className="fa-text-sm font-semibold text-fa-warning">
                      {queueStatus.pendingOperations}
                    </div>
                  </div>
                  <div className="fa-glass-panel-subtle rounded-lg p-2">
                    <div className="fa-text-xs text-fa-gray-600">Failed</div>
                    <div className="fa-text-sm font-semibold text-fa-error">
                      {queueStatus.failedOperations}
                    </div>
                  </div>
                </div>
              </div>

              {/* Last Sync */}
              {syncStatus.lastSyncAt && (
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <span className="fa-text-sm font-medium text-fa-gray-700">Last Sync</span>
                    <span className="fa-text-xs text-fa-gray-600">
                      {new Date(syncStatus.lastSyncAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              )}

              {/* Error Message */}
              {syncStatus.errorMessage && (
                <div className="mb-4 p-3 rounded-lg bg-fa-error-bg border border-fa-error-border">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-4 w-4 text-fa-error mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="fa-text-sm font-medium text-fa-error mb-1">
                        Sync Error
                      </div>
                      <div className="fa-text-xs text-fa-error-text">
                        {syncStatus.errorMessage}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Sync Progress */}
              {syncStatus.syncProgress && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="fa-text-sm font-medium text-fa-gray-700">
                      {syncStatus.syncProgress.operation}
                    </span>
                    <span className="fa-text-xs text-fa-gray-600">
                      {syncStatus.syncProgress.current}/{syncStatus.syncProgress.total}
                    </span>
                  </div>
                  <div className="w-full bg-fa-gray-200 rounded-full h-2">
                    <motion.div
                      className="bg-fa-info h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ 
                        width: `${(syncStatus.syncProgress.current / syncStatus.syncProgress.total) * 100}%` 
                      }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={toggleSyncPanel}
                  className="flex-1 px-3 py-2 fa-glass-panel-subtle hover:fa-glass-panel-frosted rounded-lg fa-text-sm font-medium text-fa-gray-700 transition-all duration-200"
                >
                  View Details
                </button>
                {queueStatus.conflictOperations > 0 && (
                  <button
                    onClick={toggleSyncPanel}
                    className="flex-1 px-3 py-2 bg-fa-warning hover:bg-fa-warning-hover rounded-lg fa-text-sm font-medium text-white transition-all duration-200 flex items-center justify-center space-x-1"
                  >
                    <Zap className="h-3 w-3" />
                    <span>Resolve Conflicts</span>
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
