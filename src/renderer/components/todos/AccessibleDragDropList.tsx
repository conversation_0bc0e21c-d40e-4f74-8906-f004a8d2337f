import React, { useState, useCallback, useRef } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  closestCenter,
  MeasuringStrategy,
  defaultDropAnimationSideEffects,
  DropAnimation,
} from '@dnd-kit/core';
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { motion, AnimatePresence } from 'framer-motion';
import { Todo } from '@shared/types';
import { AccessibleSortableItem } from './AccessibleSortableItem';
import { useTodoAnnouncements } from '@renderer/hooks/useScreenReader';

export interface AccessibleDragDropListProps {
  todos: Todo[];
  onReorder: (activeId: string, overId: string) => void;
  onTodoUpdate: (todo: Todo) => void;
  onTodoDelete: (todoId: string) => void;
  className?: string;
  disabled?: boolean;
}

const dropAnimationConfig: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.4',
      },
    },
  }),
};

const measuring = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
};

export const AccessibleDragDropList: React.FC<AccessibleDragDropListProps> = ({
  todos,
  onReorder,
  onTodoUpdate,
  onTodoDelete,
  className = '',
  disabled = false,
}) => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedTodo, setDraggedTodo] = useState<Todo | null>(null);
  const announcementRef = useRef<string>('');
  
  const { announceTodoMoved, announceError } = useTodoAnnouncements();

  // Configure sensors with accessibility in mind
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Minimum distance before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const todo = todos.find(t => t.id === active.id);
    
    if (todo) {
      setActiveId(active.id as string);
      setDraggedTodo(todo);
      
      // Announce drag start for screen readers
      announcementRef.current = `Picked up todo "${todo.title}". Use arrow keys to move, space to drop, escape to cancel.`;
    }
  }, [todos]);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over || !active) return;
    
    const activeTodo = todos.find(t => t.id === active.id);
    const overTodo = todos.find(t => t.id === over.id);
    
    if (activeTodo && overTodo && activeTodo.id !== overTodo.id) {
      const activeIndex = todos.findIndex(t => t.id === activeTodo.id);
      const overIndex = todos.findIndex(t => t.id === overTodo.id);
      
      // Announce position change for screen readers
      announcementRef.current = `Moving "${activeTodo.title}" ${
        activeIndex < overIndex ? 'down' : 'up'
      } to position ${overIndex + 1} of ${todos.length}`;
    }
  }, [todos]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    setActiveId(null);
    setDraggedTodo(null);
    
    if (!over || active.id === over.id) {
      // Announce cancellation
      const todo = todos.find(t => t.id === active.id);
      if (todo) {
        announcementRef.current = `Drag cancelled. "${todo.title}" returned to original position.`;
      }
      return;
    }

    const activeTodo = todos.find(t => t.id === active.id);
    const overTodo = todos.find(t => t.id === over.id);
    
    if (activeTodo && overTodo) {
      const activeIndex = todos.findIndex(t => t.id === activeTodo.id);
      const overIndex = todos.findIndex(t => t.id === overTodo.id);
      
      if (activeIndex !== overIndex) {
        try {
          onReorder(active.id as string, over.id as string);
          
          // Announce successful move
          announceTodoMoved(activeTodo.title, overIndex + 1, todos.length);
        } catch (error) {
          announceError('Failed to reorder todo');
        }
      }
    }
  }, [todos, onReorder, announceTodoMoved, announceError]);

  const handleDragCancel = useCallback(() => {
    setActiveId(null);
    setDraggedTodo(null);
    
    if (draggedTodo) {
      announcementRef.current = `Drag cancelled. "${draggedTodo.title}" returned to original position.`;
    }
  }, [draggedTodo]);

  // Keyboard shortcuts for alternative interaction
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (disabled) return;
    
    // Alt + Arrow keys for reordering without drag
    if (event.altKey && !activeId) {
      const focusedElement = document.activeElement as HTMLElement;
      const todoId = focusedElement?.getAttribute('data-todo-id');
      
      if (todoId) {
        const currentIndex = todos.findIndex(t => t.id === todoId);
        let newIndex = currentIndex;
        
        if (event.key === 'ArrowUp' && currentIndex > 0) {
          newIndex = currentIndex - 1;
          event.preventDefault();
        } else if (event.key === 'ArrowDown' && currentIndex < todos.length - 1) {
          newIndex = currentIndex + 1;
          event.preventDefault();
        }
        
        if (newIndex !== currentIndex) {
          const targetTodo = todos[newIndex];
          if (targetTodo) {
            onReorder(todoId, targetTodo.id);
            
            // Announce the move
            const movedTodo = todos[currentIndex];
            if (movedTodo) {
              announceTodoMoved(movedTodo.title, newIndex + 1, todos.length);
            }
          }
        }
      }
    }
  }, [todos, onReorder, activeId, disabled, announceTodoMoved]);

  if (disabled || todos.length === 0) {
    return (
      <div className={className}>
        {todos.map((todo) => (
          <AccessibleSortableItem
            key={todo.id}
            todo={todo}
            onUpdate={onTodoUpdate}
            onDelete={onTodoDelete}
            isDragging={false}
            isOverlay={false}
            disabled={true}
          />
        ))}
      </div>
    );
  }

  return (
    <div 
      className={className}
      onKeyDown={handleKeyDown}
      role="application"
      aria-label="Sortable todo list"
      aria-describedby="drag-instructions"
    >
      {/* Hidden instructions for screen readers */}
      <div id="drag-instructions" className="sr-only">
        Use mouse or touch to drag and drop todos to reorder them. 
        Alternatively, focus a todo and use Alt + Arrow keys to move it up or down.
        During keyboard dragging, use arrow keys to move, space to drop, escape to cancel.
      </div>
      
      {/* Live region for drag announcements */}
      <div 
        aria-live="assertive" 
        aria-atomic="true" 
        className="sr-only"
      >
        {announcementRef.current}
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
        modifiers={[restrictToVerticalAxis]}
        measuring={measuring}
        accessibility={{
          announcements: {
            onDragStart({ active }) {
              const todo = todos.find(t => t.id === active.id);
              return todo ? `Picked up todo "${todo.title}"` : 'Picked up item';
            },
            onDragOver({ active, over }) {
              if (!over) return 'Moved to droppable area';
              
              const activeTodo = todos.find(t => t.id === active.id);
              const overTodo = todos.find(t => t.id === over.id);
              
              if (activeTodo && overTodo) {
                const overIndex = todos.findIndex(t => t.id === overTodo.id);
                return `Moved "${activeTodo.title}" to position ${overIndex + 1} of ${todos.length}`;
              }
              
              return 'Moved to new position';
            },
            onDragEnd({ active, over }) {
              if (!over) return 'Drag cancelled';
              
              const activeTodo = todos.find(t => t.id === active.id);
              const overTodo = todos.find(t => t.id === over.id);
              
              if (activeTodo && overTodo && active.id !== over.id) {
                const overIndex = todos.findIndex(t => t.id === overTodo.id);
                return `"${activeTodo.title}" dropped at position ${overIndex + 1} of ${todos.length}`;
              }
              
              return activeTodo ? `"${activeTodo.title}" returned to original position` : 'Item returned to original position';
            },
            onDragCancel({ active }) {
              const todo = todos.find(t => t.id === active.id);
              return todo ? `Drag cancelled. "${todo.title}" returned to original position` : 'Drag cancelled';
            },
          },
        }}
      >
        <SortableContext
          items={todos.map(t => t.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-3">
            {todos.map((todo, index) => (
              <AccessibleSortableItem
                key={todo.id}
                todo={todo}
                onUpdate={onTodoUpdate}
                onDelete={onTodoDelete}
                isDragging={activeId === todo.id}
                isOverlay={false}
                index={index}
                totalCount={todos.length}
              />
            ))}
          </div>
        </SortableContext>

        <DragOverlay dropAnimation={dropAnimationConfig}>
          {draggedTodo && (
            <AccessibleSortableItem
              todo={draggedTodo}
              onUpdate={onTodoUpdate}
              onDelete={onTodoDelete}
              isDragging={true}
              isOverlay={true}
            />
          )}
        </DragOverlay>
      </DndContext>
    </div>
  );
};
