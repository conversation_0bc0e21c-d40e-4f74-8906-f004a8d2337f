import React, { forwardRef } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { motion } from 'framer-motion';
import { GripVertical } from 'lucide-react';
import { Todo } from '@shared/types';
import { TodoItem } from './TodoItem';

export interface AccessibleSortableItemProps {
  todo: Todo;
  onUpdate: (todo: Todo) => void;
  onDelete: (todoId: string) => void;
  isDragging: boolean;
  isOverlay: boolean;
  disabled?: boolean;
  index?: number;
  totalCount?: number;
}

export const AccessibleSortableItem = forwardRef<HTMLDivElement, AccessibleSortableItemProps>(
  ({ todo, onUpdate, onDelete, isDragging, isOverlay, disabled = false, index, totalCount }, ref) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
    } = useSortable({
      id: todo.id,
      disabled,
      data: {
        type: 'todo',
        todo,
      },
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    // Combine refs
    const combinedRef = (node: HTMLDivElement | null) => {
      setNodeRef(node);
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    };

    const dragHandleProps = disabled ? {} : {
      ...attributes,
      ...listeners,
    };

    if (isOverlay) {
      return (
        <div
          ref={combinedRef}
          style={style}
          className="opacity-90 rotate-3 scale-105 shadow-2xl"
        >
          <TodoItem
            todo={todo}
            onUpdate={onUpdate}
            onDelete={onDelete}
          />
        </div>
      );
    }

    return (
      <motion.div
        ref={combinedRef}
        style={style}
        className={`relative group ${
          isDragging || isSortableDragging ? 'opacity-50 z-50' : ''
        }`}
        data-todo-id={todo.id}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex items-center">
          {/* Drag Handle */}
          {!disabled && (
            <div
              {...dragHandleProps}
              className={`
                flex-shrink-0 p-2 mr-2 rounded-lg cursor-grab active:cursor-grabbing
                text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-gray-100
                focus:outline-none focus:ring-2 focus:ring-fa-blue-400 focus:ring-opacity-50
                transition-all duration-200
                ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
              `}
              role="button"
              tabIndex={0}
              aria-label={`Drag to reorder "${todo.title}". Current position ${(index ?? 0) + 1} of ${totalCount ?? 1}`}
              aria-describedby={`drag-help-${todo.id}`}
              onKeyDown={(e) => {
                // Prevent default behavior for drag handle
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                }
              }}
            >
              <GripVertical className="w-4 h-4" aria-hidden="true" />
              
              {/* Hidden help text for screen readers */}
              <div id={`drag-help-${todo.id}`} className="sr-only">
                Press space to start dragging. Use arrow keys to move, space to drop, escape to cancel.
                Alternatively, use Alt + Arrow keys to move this todo up or down without dragging.
              </div>
            </div>
          )}

          {/* Todo Content */}
          <div className="flex-1 min-w-0">
            <TodoItem
              todo={todo}
              onUpdate={onUpdate}
              onDelete={onDelete}
            />
          </div>
        </div>

        {/* Position indicator for screen readers */}
        <div className="sr-only" aria-live="polite">
          {isDragging && `Dragging "${todo.title}"`}
        </div>

        {/* Visual feedback for drag state */}
        {(isDragging || isSortableDragging) && (
          <div className="absolute inset-0 bg-fa-blue-100 border-2 border-fa-blue-300 rounded-xl pointer-events-none opacity-50" />
        )}
      </motion.div>
    );
  }
);

AccessibleSortableItem.displayName = 'AccessibleSortableItem';
