import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, BarChart3, X } from 'lucide-react';
import { Todo } from '@shared/types';
import { searchEngine } from '@renderer/services/search-engine.service';
import { useAdvancedSearch } from '@renderer/hooks/useAdvancedSearch';
import { AdvancedSearchInput } from '@renderer/components/search/AdvancedSearchInput';
import { SearchResultsView } from '@renderer/components/search/SearchResultsView';
import { SearchAnalytics } from '@renderer/components/search/SearchAnalytics';
import { TodoFilterPanel } from './TodoFilterPanel';
import { useTodoFilters } from '@renderer/hooks/useTodoFilters';

export interface EnhancedTodoSearchProps {
  todos: Todo[];
  onTodoSelect: (todo: Todo) => void;
  onTodoEdit?: (todo: Todo) => void;
  className?: string;
  showAnalytics?: boolean;
  maxResults?: number;
}

export const EnhancedTodoSearch: React.FC<EnhancedTodoSearchProps> = ({
  todos,
  onTodoSelect,
  onTodoEdit,
  className = '',
  showAnalytics = true,
  maxResults = 50,
}) => {
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [showAnalyticsPanel, setShowAnalyticsPanel] = useState(false);
  const [searchMode, setSearchMode] = useState<'simple' | 'advanced'>('simple');

  // Initialize advanced search
  const {
    query,
    setQuery,
    results,
    suggestions,
    isLoading,
    error,
    search,
    clearSearch,
    filters,
    updateFilter,
    clearFilters,
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
    metrics,
    analytics,
    indexTodos,
    isIndexed,
  } = useAdvancedSearch(searchEngine, {
    autoSearch: true,
    maxResults,
    enableAnalytics: showAnalytics,
    enableSuggestions: true,
  });

  // Initialize basic filters for fallback
  const {
    filters: basicFilters,
    updateFilter: updateBasicFilter,
    clearFilters: clearBasicFilters,
    filteredTodos,
    filterSummary,
    presets,
    activePreset,
    applyPreset,
  } = useTodoFilters(todos);

  // Index todos when they change
  useEffect(() => {
    if (todos.length > 0) {
      indexTodos(todos);
    }
  }, [todos, indexTodos]);

  // Determine which todos to show
  const displayTodos = useMemo(() => {
    if (query.trim() && results.length > 0) {
      return results.map(result => result.item);
    } else if (!query.trim()) {
      return filteredTodos;
    } else {
      return [];
    }
  }, [query, results, filteredTodos]);

  // Handle search mode toggle
  const toggleSearchMode = () => {
    setSearchMode(prev => prev === 'simple' ? 'advanced' : 'simple');
    if (searchMode === 'advanced') {
      clearSearch();
    }
  };

  // Handle filter panel toggle
  const toggleFilterPanel = () => {
    setIsFilterPanelOpen(prev => !prev);
  };

  // Handle analytics panel toggle
  const toggleAnalyticsPanel = () => {
    setShowAnalyticsPanel(prev => !prev);
  };

  // Handle search
  const handleSearch = (searchQuery: string) => {
    if (searchMode === 'advanced') {
      search(searchQuery);
    }
  };

  // Handle todo selection
  const handleTodoSelect = (todo: Todo) => {
    onTodoSelect(todo);
  };

  // Handle todo edit
  const handleTodoEdit = (todo: Todo) => {
    if (onTodoEdit) {
      onTodoEdit(todo);
    }
  };

  // Get filter options for the filter panel
  const filterOptions = useMemo(() => {
    const statuses = Array.from(new Set(todos.map(todo => todo.status)));
    const priorities = Array.from(new Set(todos.map(todo => todo.priority)));
    const tags = Array.from(new Set(todos.flatMap(todo => todo.tags || [])));
    const categories = []; // TODO: Get from category service

    return {
      statuses,
      priorities,
      tags,
      categories,
    };
  }, [todos]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="fa-heading-2">Search Todos</h2>
          
          {/* Search Mode Toggle */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleSearchMode}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
              searchMode === 'advanced'
                ? 'bg-fa-blue-100 text-fa-blue-700'
                : 'bg-fa-gray-100 text-fa-gray-700 hover:bg-fa-gray-200'
            }`}
          >
            {searchMode === 'advanced' ? 'Advanced' : 'Simple'}
          </motion.button>
        </div>

        <div className="flex items-center space-x-2">
          {/* Analytics Toggle */}
          {showAnalytics && analytics && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleAnalyticsPanel}
              className={`p-2 rounded-lg transition-all duration-200 ${
                showAnalyticsPanel
                  ? 'bg-fa-purple-100 text-fa-purple-600'
                  : 'text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-gray-100'
              }`}
            >
              <BarChart3 className="w-5 h-5" />
            </motion.button>
          )}

          {/* Filter Toggle */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleFilterPanel}
            className={`p-2 rounded-lg transition-all duration-200 ${
              isFilterPanelOpen
                ? 'bg-fa-blue-100 text-fa-blue-600'
                : 'text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-gray-100'
            }`}
          >
            <Filter className="w-5 h-5" />
          </motion.button>
        </div>
      </div>

      {/* Search Input */}
      {searchMode === 'advanced' ? (
        <AdvancedSearchInput
          value={query}
          onChange={setQuery}
          onSearch={handleSearch}
          onFilterToggle={toggleFilterPanel}
          suggestions={suggestions}
          isLoading={isLoading}
          metrics={metrics}
          showMetrics={true}
          isFilterActive={isFilterPanelOpen}
          placeholder="Search todos with advanced features..."
        />
      ) : (
        <div className="fa-glass-panel p-4">
          <div className="flex items-center space-x-3">
            <Search className="w-5 h-5 text-fa-gray-400" />
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Simple search..."
              className="flex-1 bg-transparent text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"
            />
            {query && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={clearSearch}
                className="p-1 text-fa-gray-400 hover:text-fa-gray-600"
              >
                <X className="w-4 h-4" />
              </motion.button>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-fa-red-50 border border-fa-red-200 rounded-lg"
        >
          <p className="text-fa-red-700">{error}</p>
        </motion.div>
      )}

      {/* Analytics Panel */}
      <AnimatePresence>
        {showAnalyticsPanel && analytics && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
          >
            <SearchAnalytics
              analytics={analytics}
              metrics={metrics}
              compact={false}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search Results or Todo List */}
      <div className="min-h-96">
        {query.trim() && searchMode === 'advanced' ? (
          <SearchResultsView
            results={results}
            query={query}
            onResultClick={handleTodoSelect}
            onResultEdit={handleTodoEdit}
            showMatchReasons={true}
            showSnippets={true}
            maxResults={maxResults}
          />
        ) : (
          <div className="space-y-3">
            {displayTodos.length > 0 ? (
              displayTodos.map((todo) => (
                <motion.div
                  key={todo.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="fa-glass-panel p-4 hover:shadow-lg transition-all duration-200 cursor-pointer"
                  onClick={() => handleTodoSelect(todo)}
                >
                  <h3 className="fa-heading-3 mb-2">{todo.title}</h3>
                  {todo.description && (
                    <p className="text-fa-gray-600 text-sm mb-2">{todo.description}</p>
                  )}
                  <div className="flex items-center space-x-4 text-xs text-fa-gray-500">
                    <span>Priority: {todo.priority}</span>
                    <span>Status: {todo.status}</span>
                    {todo.tags && todo.tags.length > 0 && (
                      <span>Tags: {todo.tags.join(', ')}</span>
                    )}
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-12">
                <Search className="w-12 h-12 text-fa-gray-300 mx-auto mb-4" />
                <h3 className="fa-heading-3 text-fa-gray-500 mb-2">
                  {query.trim() ? 'No results found' : 'Start searching'}
                </h3>
                <p className="text-fa-gray-400">
                  {query.trim() 
                    ? 'Try adjusting your search terms or filters'
                    : 'Enter a search term to find todos'
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Filter Panel */}
      <TodoFilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
        filters={basicFilters}
        onUpdateFilter={updateBasicFilter}
        onApplyPreset={applyPreset}
        onClearFilters={clearBasicFilters}
        presets={presets}
        activePreset={activePreset}
        filterOptions={filterOptions}
        filterSummary={filterSummary}
      />
    </div>
  );
};
