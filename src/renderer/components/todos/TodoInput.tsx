import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Calendar, Tag } from 'lucide-react';
import { Todo } from '@shared/types';
import { TodoForm } from './TodoForm';
import { buttonHover } from '@renderer/utils/animations';
import { useHoverAnimation } from '@renderer/hooks/useAnimations';

export interface TodoInputProps {
  onTodoCreate?: (todo: Todo) => void;
}

export const TodoInput: React.FC<TodoInputProps> = ({ onTodoCreate }) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { hoverProps } = useHoverAnimation();

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleQuickSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      // For quick input, open the full form with the title pre-filled
      setShowForm(true);
    }
  };

  const handleFormSubmit = (todo: Todo) => {
    onTodoCreate?.(todo);
    setInputValue('');
    setShowForm(false);
  };

  const handleOpenForm = () => {
    setShowForm(true);
  };

  return (
    <>
      <form onSubmit={handleQuickSubmit} className="w-full">
        <div className={`
          fa-glass-panel-mobile fa-transition-all
          ${isFocused ? 'ring-2 ring-fa-blue-400 shadow-lg' : 'shadow-md'}
          ${isMobile ? 'p-3' : 'p-4'}
        `}>
          <div className="flex items-center">
            <motion.button
              variants={buttonHover}
              whileHover="hover"
              whileTap="tap"
              type="button"
              onClick={handleOpenForm}
              className={`
                text-fa-blue-500 hover:text-fa-blue-600 fa-transition-all fa-touch-target
                ${isMobile ? 'p-3' : 'p-4'}
              `}
            >
              <Plus className="w-5 h-5" />
            </motion.button>

            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onClick={handleOpenForm}
              placeholder="What needs to be done?"
              className="flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none cursor-pointer"
              readOnly
            />

            <div className="flex items-center space-x-2 pr-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="button"
                onClick={handleOpenForm}
                className="p-2 text-fa-gray-400 hover:text-fa-gray-600"
              >
                <Calendar className="w-4 h-4" />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="button"
                onClick={handleOpenForm}
                className="p-2 text-fa-gray-400 hover:text-fa-gray-600"
              >
                <Tag className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </div>
      </form>

      {/* Todo Form Modal */}
      <TodoForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        mode="create"
      />
    </>
  );
};