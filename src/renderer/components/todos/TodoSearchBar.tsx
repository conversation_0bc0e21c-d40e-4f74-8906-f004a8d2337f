import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Filter, Command } from 'lucide-react';
import { useDebounce, useDebouncedCallback } from '@renderer/hooks/useDebounce';

export interface TodoSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onFilterToggle?: () => void;
  placeholder?: string;
  showFilterButton?: boolean;
  isFilterActive?: boolean;
  suggestions?: string[];
  className?: string;
}

export const TodoSearchBar: React.FC<TodoSearchBarProps> = ({
  value,
  onChange,
  onFilterToggle,
  placeholder = 'Search todos...',
  showFilterButton = true,
  isFilterActive = false,
  suggestions = [],
  className = '',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounce the search to avoid excessive API calls
  const debouncedOnChange = useDebouncedCallback(onChange, 300, [onChange]);

  // Filter suggestions based on current input
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(value.toLowerCase()) && suggestion !== value
  ).slice(0, 5);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue); // Immediate update for UI responsiveness
    debouncedOnChange(newValue); // Debounced update for search
    setSelectedSuggestionIndex(-1);
    setShowSuggestions(newValue.length > 0 && filteredSuggestions.length > 0);
  };

  // Handle clear search
  const handleClear = () => {
    onChange('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || filteredSuggestions.length === 0) {
      if (e.key === 'Escape') {
        inputRef.current?.blur();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleSuggestionSelect(filteredSuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle focus events
  const handleFocus = () => {
    setIsFocused(true);
    if (value.length > 0 && filteredSuggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
    }, 150);
  };

  // Keyboard shortcut for focus (Cmd/Ctrl + K)
  useEffect(() => {
    const handleKeyboardShortcut = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyboardShortcut);
    return () => document.removeEventListener('keydown', handleKeyboardShortcut);
  }, []);

  return (
    <div className={`relative ${className}`} role="search">
      {/* Search Input */}
      <div className={`fa-glass-panel relative transition-all duration-300 ${
        isFocused ? 'ring-2 ring-fa-blue-400 ring-opacity-50' : ''
      }`}>
        <div className="flex items-center">
          {/* Search Icon */}
          <div className="flex-shrink-0 pl-4">
            <Search
              className={`w-5 h-5 transition-colors duration-200 ${
                isFocused ? 'text-fa-blue-500' : 'text-fa-gray-400'
              }`}
              aria-hidden="true"
            />
          </div>

          {/* Input */}
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="flex-1 bg-transparent px-4 py-3 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"
            aria-label="Search todos"
            aria-describedby="search-help"
            aria-expanded={showSuggestions}
            aria-autocomplete="list"
            aria-activedescendant={selectedSuggestionIndex >= 0 ? `suggestion-${selectedSuggestionIndex}` : undefined}
            role="combobox"
          />

          {/* Keyboard Shortcut Hint */}
          {!isFocused && !value && (
            <div className="flex-shrink-0 pr-2" aria-hidden="true">
              <div className="flex items-center space-x-1 text-fa-gray-400 text-sm">
                <Command className="w-3 h-3" />
                <span>K</span>
              </div>
            </div>
          )}

          {/* Clear Button */}
          {value && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleClear}
              className="flex-shrink-0 p-1 mx-2 text-fa-gray-400 hover:text-fa-gray-600 rounded-lg hover:bg-fa-white-glass"
              aria-label="Clear search"
              title="Clear search"
            >
              <X className="w-4 h-4" aria-hidden="true" />
            </motion.button>
          )}

          {/* Filter Button */}
          {showFilterButton && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onFilterToggle}
              className={`flex-shrink-0 p-2 mx-2 rounded-lg transition-all duration-200 ${
                isFilterActive
                  ? 'bg-fa-blue-500 text-white'
                  : 'text-fa-gray-400 hover:text-fa-gray-600 hover:bg-fa-white-glass'
              }`}
              aria-label={`${isFilterActive ? 'Hide' : 'Show'} filters`}
              aria-pressed={isFilterActive}
              title={`${isFilterActive ? 'Hide' : 'Show'} filters`}
            >
              <Filter className="w-4 h-4" aria-hidden="true" />
            </motion.button>
          )}
        </div>
      </div>

      {/* Hidden help text for screen readers */}
      <div id="search-help" className="sr-only">
        Use Ctrl+K or Cmd+K to focus search. Use arrow keys to navigate suggestions.
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && filteredSuggestions.length > 0 && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-lg z-50 max-h-60 overflow-y-auto"
            role="listbox"
            aria-label="Search suggestions"
          >
            {filteredSuggestions.map((suggestion, index) => (
              <motion.button
                key={suggestion}
                id={`suggestion-${index}`}
                whileHover={{ backgroundColor: 'rgba(59, 130, 246, 0.1)' }}
                onClick={() => handleSuggestionSelect(suggestion)}
                className={`w-full text-left px-4 py-3 transition-colors duration-150 ${
                  index === selectedSuggestionIndex
                    ? 'bg-fa-blue-100 text-fa-blue-800'
                    : 'text-fa-gray-700 hover:bg-fa-gray-50'
                } ${index === 0 ? 'rounded-t-xl' : ''} ${
                  index === filteredSuggestions.length - 1 ? 'rounded-b-xl' : ''
                }`}
                role="option"
                aria-selected={index === selectedSuggestionIndex}
                aria-label={`Search for ${suggestion}`}
              >
                <div className="flex items-center">
                  <Search className="w-4 h-4 mr-3 text-fa-gray-400" aria-hidden="true" />
                  <span>{suggestion}</span>
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
