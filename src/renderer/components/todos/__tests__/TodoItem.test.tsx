import React from 'react';
import { render, screen } from '@testing-library/react';
import { TodoItem } from '../TodoItem';

// Define Todo type for testing
interface Todo {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'archived' | 'cancelled';
  priority: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
  due_date?: Date;
  tags: string[];
  position: number;
  metadata: Record<string, any>;
  created_at: Date;
  updated_at: Date;
  is_deleted: boolean;
}

// Mock the todoService
jest.mock('@renderer/services/todo.service', () => ({
  todoService: {
    isOverdue: jest.fn(() => false),
    formatDueDate: jest.fn(() => 'Due today'),
  },
}));

// Mock the overdue utils
jest.mock('@renderer/utils/overdueUtils', () => ({
  getOverdueInfo: jest.fn(() => ({
    isOverdue: false,
    severity: 'none',
    daysOverdue: 0,
    hoursOverdue: 0,
    formattedDuration: '',
  })),
  getOverdueClasses: jest.fn(() => ({
    borderClass: '',
    bgClass: '',
    textClass: '',
    iconClass: '',
    pulseClass: '',
  })),
  getOverdueIcon: jest.fn(() => 'AlertCircle'),
}));

describe('TodoItem', () => {
  const mockTodo: Todo = {
    id: '1',
    user_id: 'user1',
    title: 'Test Todo',
    description: 'Test description',
    status: 'pending',
    priority: 'medium',
    due_date: new Date(),
    tags: ['work'],
    position: 1,
    metadata: {},
    created_at: new Date(),
    updated_at: new Date(),
    is_deleted: false,
  };

  it('renders todo item correctly', () => {
    render(<TodoItem todo={mockTodo} />);

    expect(screen.getByText('Test Todo')).toBeInTheDocument();
    expect(screen.getByText('pending')).toBeInTheDocument();
    expect(screen.getByText('work')).toBeInTheDocument();
  });

  it('shows completed state', () => {
    const completedTodo: Todo = { ...mockTodo, status: 'completed' };
    render(<TodoItem todo={completedTodo} />);

    const title = screen.getByText('Test Todo');
    expect(title).toHaveClass('line-through');
  });
});