import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Volume2, VolumeX, Eye, EyeOff, Keyboard, Mouse } from 'lucide-react';

// Screen Reader Announcements
export const ScreenReaderAnnouncement: React.FC<{
  message: string;
  priority?: 'polite' | 'assertive';
  children?: React.ReactNode;
}> = ({ message, priority = 'polite', children }) => {
  const [announcement, setAnnouncement] = useState('');

  useEffect(() => {
    if (message) {
      setAnnouncement(message);
      // Clear after announcement to allow re-announcing the same message
      const timer = setTimeout(() => setAnnouncement(''), 100);
      return () => clearTimeout(timer);
    }
  }, [message]);

  return (
    <>
      {children}
      <div
        aria-live={priority}
        aria-atomic="true"
        className="sr-only"
      >
        {announcement}
      </div>
    </>
  );
};

// Focus Management
export const useFocusManagement = () => {
  const focusableElementsSelector = [
    'a[href]',
    'button:not([disabled])',
    'textarea:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
  ].join(', ');

  const trapFocus = (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(focusableElementsSelector);
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  };

  const restoreFocus = (element: HTMLElement | null) => {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  };

  return { trapFocus, restoreFocus };
};

// Keyboard Navigation Helper
export const KeyboardNavigationProvider: React.FC<{
  children: React.ReactNode;
  onEscape?: () => void;
  onEnter?: () => void;
}> = ({ children, onEscape, onEnter }) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          onEscape?.();
          break;
        case 'Enter':
          if (e.target === document.body) {
            onEnter?.();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onEscape, onEnter]);

  return <>{children}</>;
};

// High Contrast Mode Detection
export const useHighContrast = () => {
  const [isHighContrast, setIsHighContrast] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setIsHighContrast(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isHighContrast;
};

// Accessibility Settings Panel
export const AccessibilityPanel: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  const [settings, setSettings] = useState({
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    soundEnabled: true,
    keyboardNavigation: true,
  });

  const { trapFocus, restoreFocus } = useFocusManagement();
  const panelRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen && panelRef.current) {
      triggerRef.current = document.activeElement as HTMLElement;
      const cleanup = trapFocus(panelRef.current);
      return cleanup;
    }
  }, [isOpen, trapFocus]);

  useEffect(() => {
    if (!isOpen && triggerRef.current) {
      restoreFocus(triggerRef.current);
    }
  }, [isOpen, restoreFocus]);

  const updateSetting = (key: keyof typeof settings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Apply settings to document
    if (key === 'reducedMotion') {
      document.documentElement.style.setProperty(
        '--animation-duration', 
        value ? '0.01ms' : ''
      );
    }
    
    if (key === 'highContrast') {
      document.documentElement.classList.toggle('high-contrast', value);
    }
    
    if (key === 'largeText') {
      document.documentElement.classList.toggle('large-text', value);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            ref={panelRef}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed right-0 top-0 h-full w-80 fa-glass-panel-frosted border-l border-fa-gray-200 z-50 overflow-y-auto"
            role="dialog"
            aria-labelledby="accessibility-title"
            aria-modal="true"
          >
            <div className="p-6 space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <h2 id="accessibility-title" className="fa-heading-2">
                  Accessibility Settings
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 text-fa-gray-500 hover:text-fa-gray-700 fa-transition-all"
                  aria-label="Close accessibility settings"
                >
                  ×
                </button>
              </div>

              {/* Settings */}
              <div className="space-y-4">
                {/* Reduced Motion */}
                <div className="flex items-center justify-between">
                  <div>
                    <label htmlFor="reduced-motion" className="font-medium text-fa-gray-700">
                      Reduce Motion
                    </label>
                    <p className="text-sm text-fa-gray-500">
                      Minimize animations and transitions
                    </p>
                  </div>
                  <button
                    id="reduced-motion"
                    role="switch"
                    aria-checked={settings.reducedMotion}
                    onClick={() => updateSetting('reducedMotion', !settings.reducedMotion)}
                    className={`
                      relative w-12 h-6 rounded-full transition-colors duration-200
                      ${settings.reducedMotion ? 'bg-fa-blue-500' : 'bg-fa-gray-300'}
                    `}
                  >
                    <span
                      className={`
                        absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200
                        ${settings.reducedMotion ? 'translate-x-6' : 'translate-x-0'}
                      `}
                    />
                  </button>
                </div>

                {/* High Contrast */}
                <div className="flex items-center justify-between">
                  <div>
                    <label htmlFor="high-contrast" className="font-medium text-fa-gray-700">
                      High Contrast
                    </label>
                    <p className="text-sm text-fa-gray-500">
                      Increase color contrast for better visibility
                    </p>
                  </div>
                  <button
                    id="high-contrast"
                    role="switch"
                    aria-checked={settings.highContrast}
                    onClick={() => updateSetting('highContrast', !settings.highContrast)}
                    className={`
                      relative w-12 h-6 rounded-full transition-colors duration-200
                      ${settings.highContrast ? 'bg-fa-blue-500' : 'bg-fa-gray-300'}
                    `}
                  >
                    <span
                      className={`
                        absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200
                        ${settings.highContrast ? 'translate-x-6' : 'translate-x-0'}
                      `}
                    />
                  </button>
                </div>

                {/* Large Text */}
                <div className="flex items-center justify-between">
                  <div>
                    <label htmlFor="large-text" className="font-medium text-fa-gray-700">
                      Large Text
                    </label>
                    <p className="text-sm text-fa-gray-500">
                      Increase text size for better readability
                    </p>
                  </div>
                  <button
                    id="large-text"
                    role="switch"
                    aria-checked={settings.largeText}
                    onClick={() => updateSetting('largeText', !settings.largeText)}
                    className={`
                      relative w-12 h-6 rounded-full transition-colors duration-200
                      ${settings.largeText ? 'bg-fa-blue-500' : 'bg-fa-gray-300'}
                    `}
                  >
                    <span
                      className={`
                        absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200
                        ${settings.largeText ? 'translate-x-6' : 'translate-x-0'}
                      `}
                    />
                  </button>
                </div>

                {/* Sound */}
                <div className="flex items-center justify-between">
                  <div>
                    <label htmlFor="sound-enabled" className="font-medium text-fa-gray-700">
                      Sound Feedback
                    </label>
                    <p className="text-sm text-fa-gray-500">
                      Play sounds for notifications and actions
                    </p>
                  </div>
                  <button
                    id="sound-enabled"
                    role="switch"
                    aria-checked={settings.soundEnabled}
                    onClick={() => updateSetting('soundEnabled', !settings.soundEnabled)}
                    className={`
                      relative w-12 h-6 rounded-full transition-colors duration-200
                      ${settings.soundEnabled ? 'bg-fa-blue-500' : 'bg-fa-gray-300'}
                    `}
                  >
                    <span
                      className={`
                        absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200
                        ${settings.soundEnabled ? 'translate-x-6' : 'translate-x-0'}
                      `}
                    />
                  </button>
                </div>
              </div>

              {/* Keyboard Shortcuts */}
              <div className="border-t border-fa-gray-200 pt-6">
                <h3 className="font-medium text-fa-gray-700 mb-4">Keyboard Shortcuts</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>New Todo</span>
                    <kbd className="px-2 py-1 bg-fa-gray-100 rounded text-xs">Ctrl + N</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Search</span>
                    <kbd className="px-2 py-1 bg-fa-gray-100 rounded text-xs">Ctrl + F</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Toggle Sidebar</span>
                    <kbd className="px-2 py-1 bg-fa-gray-100 rounded text-xs">Ctrl + B</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Close Dialog</span>
                    <kbd className="px-2 py-1 bg-fa-gray-100 rounded text-xs">Escape</kbd>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Skip Link Component
export const SkipLink: React.FC<{ href: string; children: React.ReactNode }> = ({
  href,
  children,
}) => {
  return (
    <a
      href={href}
      className="
        sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 
        bg-fa-blue-600 text-white px-4 py-2 rounded-lg z-50
        focus:outline-none focus:ring-2 focus:ring-fa-blue-400
      "
    >
      {children}
    </a>
  );
};

// Live Region for Dynamic Content
export const LiveRegion: React.FC<{
  children: React.ReactNode;
  priority?: 'polite' | 'assertive';
  atomic?: boolean;
}> = ({ children, priority = 'polite', atomic = true }) => {
  return (
    <div
      aria-live={priority}
      aria-atomic={atomic}
      className="sr-only"
    >
      {children}
    </div>
  );
};
