import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Keyboard, X, Command } from 'lucide-react';
import { useFocusAnnouncements } from '@renderer/hooks/useScreenReader';

export interface KeyboardShortcut {
  keys: string[];
  description: string;
  category: string;
}

const shortcuts: KeyboardShortcut[] = [
  // Navigation
  { keys: ['Ctrl/Cmd', 'K'], description: 'Focus search', category: 'Navigation' },
  { keys: ['Tab'], description: 'Navigate between elements', category: 'Navigation' },
  { keys: ['Shift', 'Tab'], description: 'Navigate backwards', category: 'Navigation' },
  { keys: ['Enter'], description: 'Activate focused element', category: 'Navigation' },
  { keys: ['Escape'], description: 'Close dialogs or cancel actions', category: 'Navigation' },

  // Todo Management
  { keys: ['Space'], description: 'Toggle todo completion (when focused)', category: 'Todo Management' },
  { keys: ['E'], description: 'Edit todo (when focused)', category: 'Todo Management' },
  { keys: ['Delete'], description: 'Delete todo (when focused)', category: 'Todo Management' },
  { keys: ['Ctrl/Cmd', 'N'], description: 'Create new todo', category: 'Todo Management' },

  // Drag and Drop Alternatives
  { keys: ['Alt', '↑'], description: 'Move todo up', category: 'Reordering' },
  { keys: ['Alt', '↓'], description: 'Move todo down', category: 'Reordering' },
  { keys: ['Alt', 'Home'], description: 'Move todo to top', category: 'Reordering' },
  { keys: ['Alt', 'End'], description: 'Move todo to bottom', category: 'Reordering' },

  // Drag and Drop (Keyboard)
  { keys: ['Space'], description: 'Start dragging (on drag handle)', category: 'Keyboard Dragging' },
  { keys: ['↑', '↓'], description: 'Move item while dragging', category: 'Keyboard Dragging' },
  { keys: ['Space'], description: 'Drop item', category: 'Keyboard Dragging' },
  { keys: ['Escape'], description: 'Cancel drag', category: 'Keyboard Dragging' },

  // Bulk Operations
  { keys: ['Ctrl/Cmd', 'A'], description: 'Select all todos', category: 'Bulk Operations' },
  { keys: ['Ctrl/Cmd', 'D'], description: 'Deselect all todos', category: 'Bulk Operations' },

  // Accessibility
  { keys: ['Alt', 'A'], description: 'Open accessibility settings', category: 'Accessibility' },
  { keys: ['Alt', 'H'], description: 'Show keyboard shortcuts help', category: 'Accessibility' },
];

export interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose,
}) => {
  const { announceModalOpen, announceModalClose } = useFocusAnnouncements();

  useEffect(() => {
    if (isOpen) {
      announceModalOpen('Keyboard shortcuts help');
    } else {
      announceModalClose('Keyboard shortcuts help');
    }
  }, [isOpen, announceModalOpen, announceModalClose]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed inset-4 md:inset-8 lg:inset-16 fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-2xl z-50 overflow-hidden"
            role="dialog"
            aria-labelledby="shortcuts-title"
            aria-modal="true"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-fa-gray-200">
                <div className="flex items-center space-x-3">
                  <Keyboard className="w-6 h-6 text-fa-blue-500" aria-hidden="true" />
                  <h2 id="shortcuts-title" className="fa-heading-2">
                    Keyboard Shortcuts
                  </h2>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-fa-gray-500 hover:text-fa-gray-700 fa-transition-all rounded-lg hover:bg-fa-gray-100"
                  aria-label="Close keyboard shortcuts help"
                >
                  <X className="w-5 h-5" aria-hidden="true" />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
                    <div key={category} className="space-y-4">
                      <h3 className="fa-heading-3 text-fa-blue-600 border-b border-fa-blue-200 pb-2">
                        {category}
                      </h3>
                      <div className="space-y-3">
                        {categoryShortcuts.map((shortcut, index) => (
                          <div
                            key={`${category}-${index}`}
                            className="flex items-center justify-between p-3 rounded-lg bg-fa-white-glass hover:bg-fa-gray-50 transition-colors duration-200"
                          >
                            <span className="fa-body text-fa-gray-700 flex-1">
                              {shortcut.description}
                            </span>
                            <div className="flex items-center space-x-1 ml-4">
                              {shortcut.keys.map((key, keyIndex) => (
                                <React.Fragment key={keyIndex}>
                                  {keyIndex > 0 && (
                                    <span className="text-fa-gray-400 text-sm">+</span>
                                  )}
                                  <kbd className="px-2 py-1 text-xs font-mono bg-fa-gray-100 border border-fa-gray-300 rounded shadow-sm">
                                    {key === 'Ctrl/Cmd' ? (
                                      <>
                                        <Command className="w-3 h-3 inline mr-1" aria-hidden="true" />
                                        {navigator.platform.includes('Mac') ? 'Cmd' : 'Ctrl'}
                                      </>
                                    ) : (
                                      key
                                    )}
                                  </kbd>
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Footer */}
              <div className="p-6 border-t border-fa-gray-200 bg-fa-gray-50">
                <div className="flex items-center justify-between">
                  <p className="fa-caption text-fa-gray-600">
                    Press <kbd className="px-2 py-1 text-xs font-mono bg-white border border-fa-gray-300 rounded">Escape</kbd> to close this dialog
                  </p>
                  <button
                    onClick={onClose}
                    className="fa-button-primary px-4 py-2"
                  >
                    Got it
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

/**
 * Hook for managing keyboard shortcuts help
 */
export const useKeyboardShortcutsHelp = () => {
  const [isOpen, setIsOpen] = useState(false);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen(prev => !prev);

  // Global keyboard shortcut to open help
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey && event.key === 'h') {
        event.preventDefault();
        toggle();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isOpen,
    open,
    close,
    toggle,
  };
};
