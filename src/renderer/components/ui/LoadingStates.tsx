import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, CheckCircle, AlertCircle, Clock, Zap } from 'lucide-react';
import { useLoadingAnimation } from '@renderer/hooks/useAnimations';

export type LoadingStateType = 'loading' | 'success' | 'error' | 'idle' | 'processing';

export interface LoadingStateProps {
  state: LoadingStateType;
  message?: string;
  submessage?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  inline?: boolean;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  state,
  message,
  submessage,
  className = '',
  size = 'md',
  showIcon = true,
  inline = false,
}) => {
  const { spinnerVariants, pulseVariants } = useLoadingAnimation();

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'p-4',
          icon: 'w-4 h-4',
          text: 'text-sm',
          subtext: 'text-xs',
        };
      case 'lg':
        return {
          container: 'p-8',
          icon: 'w-8 h-8',
          text: 'text-lg',
          subtext: 'text-base',
        };
      case 'md':
      default:
        return {
          container: 'p-6',
          icon: 'w-6 h-6',
          text: 'text-base',
          subtext: 'text-sm',
        };
    }
  };

  const getStateConfig = () => {
    switch (state) {
      case 'loading':
        return {
          icon: Loader2,
          color: 'text-fa-blue-500',
          bgColor: 'bg-fa-blue-50',
          borderColor: 'border-fa-blue-200',
          defaultMessage: 'Loading...',
          animation: spinnerVariants,
        };
      case 'processing':
        return {
          icon: Zap,
          color: 'text-fa-aqua-500',
          bgColor: 'bg-fa-aqua-50',
          borderColor: 'border-fa-aqua-200',
          defaultMessage: 'Processing...',
          animation: pulseVariants,
        };
      case 'success':
        return {
          icon: CheckCircle,
          color: 'text-fa-success',
          bgColor: 'bg-fa-success-bg',
          borderColor: 'border-fa-success-border',
          defaultMessage: 'Success!',
          animation: {
            initial: { scale: 0 },
            animate: { scale: 1 },
            transition: { type: 'spring', damping: 15, stiffness: 300 },
          },
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-fa-error',
          bgColor: 'bg-fa-error-bg',
          borderColor: 'border-fa-error-border',
          defaultMessage: 'Error occurred',
          animation: {
            initial: { scale: 0, rotate: -180 },
            animate: { scale: 1, rotate: 0 },
            transition: { type: 'spring', damping: 20, stiffness: 400 },
          },
        };
      case 'idle':
      default:
        return {
          icon: Clock,
          color: 'text-fa-gray-500',
          bgColor: 'bg-fa-gray-50',
          borderColor: 'border-fa-gray-200',
          defaultMessage: 'Ready',
          animation: {},
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const stateConfig = getStateConfig();
  const IconComponent = stateConfig.icon;

  if (inline) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showIcon && (
          <motion.div
            variants={stateConfig.animation}
            animate="animate"
            className={stateConfig.color}
          >
            <IconComponent className={sizeClasses.icon} />
          </motion.div>
        )}
        {message && (
          <span className={`${sizeClasses.text} ${stateConfig.color} font-medium`}>
            {message || stateConfig.defaultMessage}
          </span>
        )}
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`
        ${sizeClasses.container} 
        ${stateConfig.bgColor} 
        ${stateConfig.borderColor}
        border rounded-lg text-center
        ${className}
      `}
    >
      {showIcon && (
        <motion.div
          variants={stateConfig.animation}
          animate="animate"
          className={`${stateConfig.color} mx-auto mb-3`}
        >
          <IconComponent className={`${sizeClasses.icon} mx-auto`} />
        </motion.div>
      )}
      
      {message && (
        <h3 className={`${sizeClasses.text} font-medium ${stateConfig.color} mb-1`}>
          {message || stateConfig.defaultMessage}
        </h3>
      )}
      
      {submessage && (
        <p className={`${sizeClasses.subtext} text-fa-gray-600`}>
          {submessage}
        </p>
      )}
    </motion.div>
  );
};

// Specialized loading components
export const InlineLoader: React.FC<{ message?: string; size?: 'sm' | 'md' | 'lg' }> = ({
  message = 'Loading...',
  size = 'sm',
}) => (
  <LoadingState
    state="loading"
    message={message}
    size={size}
    inline
    className="justify-center"
  />
);

export const ProcessingIndicator: React.FC<{ message?: string }> = ({
  message = 'Processing...',
}) => (
  <LoadingState
    state="processing"
    message={message}
    size="sm"
    inline
  />
);

export const SuccessIndicator: React.FC<{ message?: string; submessage?: string }> = ({
  message = 'Success!',
  submessage,
}) => (
  <LoadingState
    state="success"
    message={message}
    submessage={submessage}
    size="md"
  />
);

export const ErrorIndicator: React.FC<{ message?: string; submessage?: string }> = ({
  message = 'Error occurred',
  submessage,
}) => (
  <LoadingState
    state="error"
    message={message}
    submessage={submessage}
    size="md"
  />
);

// Progress indicator with steps
export interface ProgressStepProps {
  steps: Array<{
    id: string;
    label: string;
    status: 'pending' | 'active' | 'completed' | 'error';
  }>;
  className?: string;
}

export const ProgressSteps: React.FC<ProgressStepProps> = ({ steps, className = '' }) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {steps.map((step, index) => (
        <motion.div
          key={step.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className="flex items-center space-x-3"
        >
          <div className="flex-shrink-0">
            <AnimatePresence mode="wait">
              {step.status === 'completed' && (
                <motion.div
                  key="completed"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0 }}
                  className="w-6 h-6 bg-fa-success rounded-full flex items-center justify-center"
                >
                  <CheckCircle className="w-4 h-4 text-white" />
                </motion.div>
              )}
              {step.status === 'active' && (
                <motion.div
                  key="active"
                  variants={spinnerVariants}
                  animate="animate"
                  className="w-6 h-6 border-2 border-fa-blue-500 border-t-transparent rounded-full"
                />
              )}
              {step.status === 'error' && (
                <motion.div
                  key="error"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0 }}
                  className="w-6 h-6 bg-fa-error rounded-full flex items-center justify-center"
                >
                  <AlertCircle className="w-4 h-4 text-white" />
                </motion.div>
              )}
              {step.status === 'pending' && (
                <motion.div
                  key="pending"
                  className="w-6 h-6 border-2 border-fa-gray-300 rounded-full"
                />
              )}
            </AnimatePresence>
          </div>
          
          <span
            className={`font-medium ${
              step.status === 'completed'
                ? 'text-fa-success'
                : step.status === 'active'
                ? 'text-fa-blue-600'
                : step.status === 'error'
                ? 'text-fa-error'
                : 'text-fa-gray-500'
            }`}
          >
            {step.label}
          </span>
        </motion.div>
      ))}
    </div>
  );
};

// Floating action button with loading state
export interface FloatingActionButtonProps {
  onClick: () => void;
  isLoading?: boolean;
  icon: React.ComponentType<{ className?: string }>;
  className?: string;
  disabled?: boolean;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onClick,
  isLoading = false,
  icon: Icon,
  className = '',
  disabled = false,
}) => {
  const { spinnerVariants } = useLoadingAnimation();

  return (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`
        fixed bottom-6 right-6 w-14 h-14 
        bg-gradient-to-r from-fa-blue-500 to-fa-aqua-500
        text-white rounded-full shadow-lg
        flex items-center justify-center
        disabled:opacity-50 disabled:cursor-not-allowed
        hover:shadow-xl transition-shadow duration-300
        ${className}
      `}
    >
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            variants={spinnerVariants}
            animate="animate"
          >
            <Loader2 className="w-6 h-6" />
          </motion.div>
        ) : (
          <motion.div
            key="icon"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
          >
            <Icon className="w-6 h-6" />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.button>
  );
};
