import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useMotionValue, useTransform } from 'framer-motion';
import { Check, X, Eye, EyeOff, Search, ChevronDown } from 'lucide-react';
import { useHoverAnimation, useLoadingAnimation } from '@renderer/hooks/useAnimations';

// Enhanced Input with micro-interactions
export interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
  onValidate?: (value: string) => boolean;
  showValidation?: boolean;
}

export const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  error,
  success,
  icon: Icon,
  onValidate,
  showValidation = false,
  className = '',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleValidation = (value: string) => {
    if (onValidate && showValidation) {
      const valid = onValidate(value);
      setIsValid(valid);
    }
  };

  const borderColor = error 
    ? 'border-fa-error' 
    : success || isValid 
    ? 'border-fa-success' 
    : isFocused 
    ? 'border-fa-blue-400' 
    : 'border-fa-gray-300';

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <motion.label
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="block text-sm font-medium text-fa-gray-700"
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        <motion.div
          className={`
            relative fa-input-glass border-2 transition-all duration-200
            ${borderColor}
            ${isFocused ? 'ring-2 ring-fa-blue-400 ring-opacity-20' : ''}
          `}
          whileFocus={{ scale: 1.02 }}
        >
          {Icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Icon className="w-5 h-5 text-fa-gray-400" />
            </div>
          )}
          
          <input
            ref={inputRef}
            {...props}
            className={`
              w-full bg-transparent border-none outline-none
              ${Icon ? 'pl-10' : 'pl-3'} pr-10 py-3
              text-fa-gray-700 placeholder-fa-gray-400
            `}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            onChange={(e) => {
              handleValidation(e.target.value);
              props.onChange?.(e);
            }}
          />
          
          {/* Validation indicator */}
          <AnimatePresence>
            {showValidation && isValid !== null && (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                className="absolute right-3 top-1/2 transform -translate-y-1/2"
              >
                {isValid ? (
                  <Check className="w-5 h-5 text-fa-success" />
                ) : (
                  <X className="w-5 h-5 text-fa-error" />
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        
        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-sm text-fa-error mt-1"
            >
              {error}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Enhanced Password Input
export interface PasswordInputProps extends Omit<EnhancedInputProps, 'type'> {
  showStrength?: boolean;
}

export const PasswordInput: React.FC<PasswordInputProps> = ({
  showStrength = false,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [strength, setStrength] = useState(0);

  const calculateStrength = (password: string): number => {
    let score = 0;
    if (password.length >= 8) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    return score;
  };

  const getStrengthColor = (strength: number): string => {
    if (strength <= 1) return 'bg-fa-error';
    if (strength <= 2) return 'bg-fa-warning';
    if (strength <= 3) return 'bg-fa-blue-400';
    return 'bg-fa-success';
  };

  const getStrengthLabel = (strength: number): string => {
    if (strength <= 1) return 'Weak';
    if (strength <= 2) return 'Fair';
    if (strength <= 3) return 'Good';
    return 'Strong';
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        <EnhancedInput
          {...props}
          type={showPassword ? 'text' : 'password'}
          onChange={(e) => {
            if (showStrength) {
              setStrength(calculateStrength(e.target.value));
            }
            props.onChange?.(e);
          }}
        />
        
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-fa-gray-400 hover:text-fa-gray-600"
        >
          {showPassword ? (
            <EyeOff className="w-5 h-5" />
          ) : (
            <Eye className="w-5 h-5" />
          )}
        </motion.button>
      </div>
      
      {/* Password strength indicator */}
      {showStrength && (
        <div className="space-y-1">
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((level) => (
              <motion.div
                key={level}
                className={`h-1 flex-1 rounded-full ${
                  level <= strength ? getStrengthColor(strength) : 'bg-fa-gray-200'
                }`}
                initial={{ scaleX: 0 }}
                animate={{ scaleX: level <= strength ? 1 : 0 }}
                transition={{ delay: level * 0.1 }}
              />
            ))}
          </div>
          <p className={`text-xs ${
            strength <= 1 ? 'text-fa-error' :
            strength <= 2 ? 'text-fa-warning' :
            strength <= 3 ? 'text-fa-blue-600' :
            'text-fa-success'
          }`}>
            Password strength: {getStrengthLabel(strength)}
          </p>
        </div>
      )}
    </div>
  );
};

// Enhanced Search Input
export interface SearchInputProps extends Omit<EnhancedInputProps, 'icon'> {
  onSearch?: (query: string) => void;
  suggestions?: string[];
  showSuggestions?: boolean;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  onSearch,
  suggestions = [],
  showSuggestions = false,
  ...props
}) => {
  const [query, setQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(query.toLowerCase())
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < filteredSuggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0) {
        setQuery(filteredSuggestions[selectedIndex]);
        onSearch?.(filteredSuggestions[selectedIndex]);
      } else {
        onSearch?.(query);
      }
      setShowDropdown(false);
    } else if (e.key === 'Escape') {
      setShowDropdown(false);
      setSelectedIndex(-1);
    }
  };

  return (
    <div className="relative">
      <EnhancedInput
        {...props}
        icon={Search}
        value={query}
        onChange={(e) => {
          setQuery(e.target.value);
          setShowDropdown(showSuggestions && e.target.value.length > 0);
          props.onChange?.(e);
        }}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          if (showSuggestions && query.length > 0) {
            setShowDropdown(true);
          }
        }}
        onBlur={() => {
          // Delay hiding to allow clicking on suggestions
          setTimeout(() => setShowDropdown(false), 200);
        }}
      />
      
      {/* Suggestions dropdown */}
      <AnimatePresence>
        {showDropdown && filteredSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-1 fa-glass-panel border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
          >
            {filteredSuggestions.map((suggestion, index) => (
              <motion.button
                key={suggestion}
                whileHover={{ backgroundColor: 'rgba(0, 123, 255, 0.1)' }}
                onClick={() => {
                  setQuery(suggestion);
                  onSearch?.(suggestion);
                  setShowDropdown(false);
                }}
                className={`
                  w-full text-left px-4 py-2 hover:bg-fa-blue-50 transition-colors
                  ${index === selectedIndex ? 'bg-fa-blue-50' : ''}
                  ${index === 0 ? 'rounded-t-lg' : ''}
                  ${index === filteredSuggestions.length - 1 ? 'rounded-b-lg' : ''}
                `}
              >
                {suggestion}
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Enhanced Select with animations
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface EnhancedSelectProps {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  className?: string;
}

export const EnhancedSelect: React.FC<EnhancedSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  label,
  error,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const { hoverProps } = useHoverAnimation();

  const selectedOption = options.find(option => option.value === value);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < options.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && !options[selectedIndex].disabled) {
        onChange?.(options[selectedIndex].value);
        setIsOpen(false);
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-fa-gray-700">
          {label}
        </label>
      )}
      
      <div className="relative">
        <motion.button
          {...hoverProps}
          onClick={() => setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          className={`
            w-full fa-input-glass border-2 transition-all duration-200
            ${error ? 'border-fa-error' : 'border-fa-gray-300'}
            ${isOpen ? 'border-fa-blue-400 ring-2 ring-fa-blue-400 ring-opacity-20' : ''}
            flex items-center justify-between
          `}
        >
          <span className={selectedOption ? 'text-fa-gray-700' : 'text-fa-gray-400'}>
            {selectedOption?.label || placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-fa-gray-400" />
          </motion.div>
        </motion.button>
        
        {/* Options dropdown */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              className="absolute top-full left-0 right-0 mt-1 fa-glass-panel border border-fa-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
            >
              {options.map((option, index) => (
                <motion.button
                  key={option.value}
                  whileHover={!option.disabled ? { backgroundColor: 'rgba(0, 123, 255, 0.1)' } : {}}
                  onClick={() => {
                    if (!option.disabled) {
                      onChange?.(option.value);
                      setIsOpen(false);
                    }
                  }}
                  disabled={option.disabled}
                  className={`
                    w-full text-left px-4 py-2 transition-colors
                    ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-fa-blue-50'}
                    ${index === selectedIndex ? 'bg-fa-blue-50' : ''}
                    ${option.value === value ? 'bg-fa-blue-100 text-fa-blue-700' : ''}
                    ${index === 0 ? 'rounded-t-lg' : ''}
                    ${index === options.length - 1 ? 'rounded-b-lg' : ''}
                  `}
                >
                  {option.label}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-sm text-fa-error mt-1"
            >
              {error}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
