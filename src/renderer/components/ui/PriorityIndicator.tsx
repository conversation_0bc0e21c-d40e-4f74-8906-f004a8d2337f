import React from 'react';
import { motion } from 'framer-motion';
import { TodoPriority } from '@shared/types';
import {
  getPriorityColor,
  getPriorityBgColor,
  getPriorityBorderColor,
  getPriorityTextColor,
  getPriorityIcon,
  getPriorityLabel,
  getPriorityGradient,
  getPriorityGlow,
  getPriorityBarHeight,
  getPriorityPulse,
  isVeryHighPriority
} from '@renderer/utils/priorityUtils';

export type PriorityIndicatorVariant = 
  | 'icon' 
  | 'badge' 
  | 'bar' 
  | 'chip' 
  | 'full'
  | 'minimal'
  | 'glow';

export interface PriorityIndicatorProps {
  priority: TodoPriority;
  variant?: PriorityIndicatorVariant;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
  animated?: boolean;
}

export const PriorityIndicator: React.FC<PriorityIndicatorProps> = ({
  priority,
  variant = 'icon',
  size = 'md',
  showLabel = false,
  className = '',
  animated = true
}) => {
  const IconComponent = getPriorityIcon(priority);

  const sizeClasses = {
    sm: 'w-3 h-3 text-xs',
    md: 'w-4 h-4 text-sm',
    lg: 'w-5 h-5 text-base'
  };

  const badgeSizeClasses = {
    sm: 'px-1.5 py-0.5 text-xs',
    md: 'px-2 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  const iconSize = sizeClasses[size];
  const badgeSize = badgeSizeClasses[size];

  // Icon variant - just the priority icon
  if (variant === 'icon') {
    return (
      <motion.div
        className={`inline-flex items-center ${className}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        <IconComponent 
          className={`${iconSize} ${getPriorityColor(priority)} ${getPriorityPulse(priority)}`} 
        />
        {showLabel && (
          <span className={`ml-1 ${getPriorityTextColor(priority)} font-medium`}>
            {getPriorityLabel(priority)}
          </span>
        )}
      </motion.div>
    );
  }

  // Badge variant - colored background with icon and optional label
  if (variant === 'badge') {
    return (
      <motion.div
        className={`inline-flex items-center rounded-full ${badgeSize} ${getPriorityBgColor(priority)} ${getPriorityBorderColor(priority)} border ${className}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        <IconComponent className={`${sizeClasses.sm} ${getPriorityTextColor(priority)}`} />
        {showLabel && (
          <span className={`ml-1 ${getPriorityTextColor(priority)} font-medium`}>
            {getPriorityLabel(priority)}
          </span>
        )}
      </motion.div>
    );
  }

  // Bar variant - colored bar indicator
  if (variant === 'bar') {
    return (
      <motion.div
        className={`w-full ${getPriorityBarHeight(priority)} bg-gradient-to-r ${getPriorityGradient(priority)} rounded-full ${className} ${getPriorityPulse(priority)}`}
        initial={animated ? { scaleX: 0 } : undefined}
        animate={animated ? { scaleX: 1 } : undefined}
        transition={{ duration: 0.3 }}
      />
    );
  }

  // Chip variant - pill-shaped with background
  if (variant === 'chip') {
    return (
      <motion.div
        className={`inline-flex items-center rounded-lg ${badgeSize} ${getPriorityBgColor(priority)} ${getPriorityBorderColor(priority)} border ${className}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        <IconComponent className={`${sizeClasses.sm} ${getPriorityTextColor(priority)}`} />
        <span className={`ml-1 ${getPriorityTextColor(priority)} font-medium`}>
          {getPriorityLabel(priority)}
        </span>
      </motion.div>
    );
  }

  // Full variant - comprehensive display with gradient and glow
  if (variant === 'full') {
    return (
      <motion.div
        className={`inline-flex items-center rounded-lg ${badgeSize} bg-gradient-to-r ${getPriorityGradient(priority)} text-white shadow-lg ${getPriorityGlow(priority)} ${className} ${getPriorityPulse(priority)}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        <IconComponent className={`${sizeClasses.sm} text-white`} />
        <span className="ml-1 text-white font-medium">
          {getPriorityLabel(priority)}
        </span>
      </motion.div>
    );
  }

  // Minimal variant - subtle indicator
  if (variant === 'minimal') {
    return (
      <motion.div
        className={`inline-flex items-center ${className}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${getPriorityGradient(priority)} ${getPriorityPulse(priority)}`} />
        {showLabel && (
          <span className={`ml-2 ${getPriorityTextColor(priority)} text-sm font-medium`}>
            {getPriorityLabel(priority)}
          </span>
        )}
      </motion.div>
    );
  }

  // Glow variant - for very high priority items with special effects
  if (variant === 'glow') {
    return (
      <motion.div
        className={`inline-flex items-center rounded-lg ${badgeSize} ${className}`}
        initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
        animate={animated ? { scale: 1, opacity: 1 } : undefined}
        transition={{ duration: 0.2 }}
      >
        {isVeryHighPriority(priority) ? (
          <motion.div
            className={`inline-flex items-center rounded-lg ${badgeSize} bg-gradient-to-r ${getPriorityGradient(priority)} text-white shadow-lg ${getPriorityGlow(priority)} animate-pulse`}
            animate={{
              boxShadow: [
                '0 0 20px rgba(239, 68, 68, 0.5)',
                '0 0 30px rgba(239, 68, 68, 0.8)',
                '0 0 20px rgba(239, 68, 68, 0.5)'
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          >
            <IconComponent className={`${sizeClasses.sm} text-white`} />
            {showLabel && (
              <span className="ml-1 text-white font-bold">
                {getPriorityLabel(priority)}
              </span>
            )}
          </motion.div>
        ) : (
          <div className={`inline-flex items-center rounded-lg ${badgeSize} ${getPriorityBgColor(priority)} ${getPriorityBorderColor(priority)} border`}>
            <IconComponent className={`${sizeClasses.sm} ${getPriorityTextColor(priority)}`} />
            {showLabel && (
              <span className={`ml-1 ${getPriorityTextColor(priority)} font-medium`}>
                {getPriorityLabel(priority)}
              </span>
            )}
          </div>
        )}
      </motion.div>
    );
  }

  // Default fallback
  return (
    <motion.div
      className={`inline-flex items-center ${className}`}
      initial={animated ? { scale: 0.8, opacity: 0 } : undefined}
      animate={animated ? { scale: 1, opacity: 1 } : undefined}
      transition={{ duration: 0.2 }}
    >
      <IconComponent className={`${iconSize} ${getPriorityColor(priority)}`} />
    </motion.div>
  );
};
