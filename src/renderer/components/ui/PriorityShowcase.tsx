import React from 'react';
import { motion } from 'framer-motion';
import { TodoPriority } from '@shared/types';
import { PriorityIndicator } from './PriorityIndicator';
import { getPriorityOptions } from '@renderer/utils/priorityUtils';

export const PriorityShowcase: React.FC = () => {
  const priorities: TodoPriority[] = ['very_high', 'high', 'medium', 'low', 'very_low'];
  const variants = ['icon', 'badge', 'bar', 'chip', 'full', 'minimal', 'glow'] as const;

  return (
    <div className="p-6 space-y-8 bg-white rounded-lg shadow-lg">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Priority Visual Indicators</h2>
        <p className="text-gray-600">Comprehensive priority indicator system for the todo application</p>
      </div>

      {variants.map((variant) => (
        <motion.div
          key={variant}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          <h3 className="text-lg font-semibold text-gray-700 capitalize border-b pb-2">
            {variant} Variant
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {priorities.map((priority) => (
              <div key={priority} className="flex flex-col items-center space-y-2 p-4 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-600 capitalize">
                  {priority.replace('_', ' ')}
                </span>
                <PriorityIndicator
                  priority={priority}
                  variant={variant}
                  size="md"
                  showLabel={variant === 'chip' || variant === 'full' || variant === 'glow'}
                  animated={true}
                />
              </div>
            ))}
          </div>
        </motion.div>
      ))}

      {/* Size Variations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="space-y-4"
      >
        <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
          Size Variations (Badge Variant)
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {['sm', 'md', 'lg'].map((size) => (
            <div key={size} className="space-y-3">
              <h4 className="text-md font-medium text-gray-600 uppercase text-center">
                {size} Size
              </h4>
              <div className="flex flex-wrap justify-center gap-2">
                {priorities.map((priority) => (
                  <PriorityIndicator
                    key={priority}
                    priority={priority}
                    variant="badge"
                    size={size as 'sm' | 'md' | 'lg'}
                    showLabel={false}
                    animated={true}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Usage Examples */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
        className="space-y-4"
      >
        <h3 className="text-lg font-semibold text-gray-700 border-b pb-2">
          Usage Examples
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-600">Todo Item Priority</h4>
            <div className="p-4 bg-white border rounded-lg shadow-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-800">Complete project proposal</span>
                <PriorityIndicator
                  priority="very_high"
                  variant="badge"
                  size="sm"
                  showLabel={false}
                  animated={true}
                />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-600">Filter Option</h4>
            <div className="p-4 bg-white border rounded-lg shadow-sm">
              <div className="flex items-center space-x-3">
                <input type="checkbox" className="rounded" />
                <PriorityIndicator
                  priority="high"
                  variant="icon"
                  size="sm"
                  showLabel={true}
                  animated={false}
                />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-600">Form Selection</h4>
            <div className="p-4 bg-white border rounded-lg shadow-sm">
              <PriorityIndicator
                priority="medium"
                variant="chip"
                size="md"
                showLabel={true}
                animated={true}
              />
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-600">Priority Bar</h4>
            <div className="p-4 bg-white border rounded-lg shadow-sm">
              <div className="space-y-2">
                <span className="text-sm text-gray-600">Task Priority Level</span>
                <PriorityIndicator
                  priority="high"
                  variant="bar"
                  animated={true}
                />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
