import React from 'react';
import { motion } from 'framer-motion';
import { useLoadingAnimation } from '@renderer/hooks/useAnimations';

export interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  animation?: 'pulse' | 'wave' | 'none';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  width,
  height,
  variant = 'rectangular',
  animation = 'wave',
}) => {
  const { pulseVariants } = useLoadingAnimation();

  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded-md';
      case 'circular':
        return 'rounded-full';
      case 'rounded':
        return 'rounded-lg';
      case 'rectangular':
      default:
        return 'rounded-md';
    }
  };

  const getAnimationClasses = () => {
    switch (animation) {
      case 'pulse':
        return 'fa-skeleton';
      case 'wave':
        return 'fa-skeleton';
      case 'none':
      default:
        return 'bg-fa-gray-200';
    }
  };

  const style: React.CSSProperties = {};
  if (width) style.width = width;
  if (height) style.height = height;

  return (
    <motion.div
      className={`${getVariantClasses()} ${getAnimationClasses()} ${className}`}
      style={style}
      variants={animation === 'pulse' ? pulseVariants : undefined}
      animate={animation === 'pulse' ? 'animate' : undefined}
    />
  );
};

// Predefined skeleton components
export const SkeletonText: React.FC<{ lines?: number; className?: string }> = ({
  lines = 1,
  className = '',
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          className={index === lines - 1 ? 'w-3/4' : 'w-full'}
        />
      ))}
    </div>
  );
};

export const SkeletonAvatar: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({
  size = 'md',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
  };

  return (
    <Skeleton
      variant="circular"
      className={`${sizeClasses[size]} ${className}`}
    />
  );
};

export const SkeletonButton: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({
  size = 'md',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'h-8 w-20',
    md: 'h-10 w-24',
    lg: 'h-12 w-32',
  };

  return (
    <Skeleton
      variant="rounded"
      className={`${sizeClasses[size]} ${className}`}
    />
  );
};

// Todo-specific skeleton components
export const TodoItemSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`fa-glass-panel p-4 ${className}`}>
      <div className="flex items-start space-x-4">
        {/* Checkbox skeleton */}
        <Skeleton variant="circular" className="w-6 h-6 flex-shrink-0 mt-1" />
        
        {/* Content skeleton */}
        <div className="flex-1 space-y-3">
          {/* Title */}
          <Skeleton variant="text" className="h-5 w-3/4" />
          
          {/* Description */}
          <SkeletonText lines={2} className="text-sm" />
          
          {/* Meta info */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Skeleton variant="circular" className="w-4 h-4" />
              <Skeleton variant="text" className="h-3 w-16" />
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton variant="circular" className="w-4 h-4" />
              <Skeleton variant="text" className="h-3 w-20" />
            </div>
            <Skeleton variant="rounded" className="h-5 w-12" />
          </div>
        </div>
        
        {/* Action buttons skeleton */}
        <div className="flex items-center space-x-2">
          <Skeleton variant="circular" className="w-8 h-8" />
          <Skeleton variant="circular" className="w-8 h-8" />
        </div>
      </div>
    </div>
  );
};

export const TodoListSkeleton: React.FC<{ count?: number; className?: string }> = ({
  count = 5,
  className = '',
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.3 }}
        >
          <TodoItemSkeleton />
        </motion.div>
      ))}
    </div>
  );
};

export const TodoFormSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`fa-glass-panel-frosted p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <Skeleton variant="text" className="h-6 w-32" />
        <Skeleton variant="circular" className="w-6 h-6" />
      </div>
      
      {/* Form fields */}
      <div className="space-y-4">
        {/* Title field */}
        <div className="space-y-2">
          <Skeleton variant="text" className="h-4 w-16" />
          <Skeleton variant="rounded" className="h-10 w-full" />
        </div>
        
        {/* Description field */}
        <div className="space-y-2">
          <Skeleton variant="text" className="h-4 w-20" />
          <Skeleton variant="rounded" className="h-24 w-full" />
        </div>
        
        {/* Form controls */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton variant="text" className="h-4 w-16" />
            <Skeleton variant="rounded" className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton variant="text" className="h-4 w-20" />
            <Skeleton variant="rounded" className="h-10 w-full" />
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="flex items-center justify-end space-x-3">
        <SkeletonButton size="md" />
        <SkeletonButton size="md" />
      </div>
    </div>
  );
};

export const SidebarSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`fa-glass-panel h-full p-4 space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-3">
        <Skeleton variant="text" className="h-6 w-24" />
        <Skeleton variant="rounded" className="h-10 w-full" />
      </div>
      
      {/* Menu items */}
      <div className="space-y-2">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-3 p-2">
            <Skeleton variant="circular" className="w-5 h-5" />
            <Skeleton variant="text" className="h-4 flex-1" />
          </div>
        ))}
      </div>
      
      {/* Categories */}
      <div className="space-y-3">
        <Skeleton variant="text" className="h-5 w-20" />
        <div className="space-y-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between p-2">
              <Skeleton variant="text" className="h-4 w-16" />
              <Skeleton variant="rounded" className="h-5 w-8" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Loading state wrapper component
export const LoadingWrapper: React.FC<{
  isLoading: boolean;
  skeleton: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}> = ({ isLoading, skeleton, children, className = '' }) => {
  return (
    <div className={className}>
      {isLoading ? skeleton : children}
    </div>
  );
};
