import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, Zap, Clock } from 'lucide-react';
import { toastSlideIn } from '@renderer/utils/animations';
import { useReducedMotion } from '@renderer/hooks/useAnimations';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'update';

export interface ToastData {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  progress?: number; // 0-100 for progress toasts
  autoClose?: boolean;
}

interface ToastProps {
  toast: ToastData;
  onDismiss: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState(toast.duration || 5000);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    if (toast.duration && toast.duration > 0 && toast.autoClose !== false && !toast.persistent) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onDismiss(toast.id), prefersReducedMotion ? 0 : 300);
      }, toast.duration);

      const interval = setInterval(() => {
        setTimeLeft(prev => Math.max(0, prev - 100));
      }, 100);

      return () => {
        clearTimeout(timer);
        clearInterval(interval);
      };
    }
  }, [toast.duration, toast.id, onDismiss, toast.autoClose, toast.persistent, prefersReducedMotion]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => onDismiss(toast.id), prefersReducedMotion ? 0 : 300);
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-fa-success" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-fa-error" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-fa-warning" />;
      case 'info':
        return <Info className="w-5 h-5 text-fa-info" />;
      case 'loading':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{
              duration: prefersReducedMotion ? 0 : 1,
              repeat: prefersReducedMotion ? 0 : Infinity,
              ease: 'linear'
            }}
          >
            <Clock className="w-5 h-5 text-fa-blue-500" />
          </motion.div>
        );
      case 'update':
        return <Zap className="w-5 h-5 text-fa-aqua-500" />;
      default:
        return <Info className="w-5 h-5 text-fa-info" />;
    }
  };

  const getBackgroundColor = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-fa-success-bg border-fa-success-border';
      case 'error':
        return 'bg-fa-error-bg border-fa-error-border';
      case 'warning':
        return 'bg-fa-warning-bg border-fa-warning-border';
      case 'info':
        return 'bg-fa-info-bg border-fa-info-border';
      case 'loading':
        return 'bg-fa-blue-50 border-fa-blue-200';
      case 'update':
        return 'bg-fa-aqua-50 border-fa-aqua-200';
      default:
        return 'bg-fa-info-bg border-fa-info-border';
    }
  };

  const getTextColor = () => {
    switch (toast.type) {
      case 'success':
        return 'text-green-800';
      case 'error':
        return 'text-red-800';
      case 'warning':
        return 'text-yellow-800';
      case 'info':
        return 'text-blue-800';
      default:
        return 'text-blue-800';
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          variants={toastSlideIn}
          initial="initial"
          animate="animate"
          exit="exit"
          className={`
            relative max-w-sm w-full border rounded-lg shadow-lg fa-glass-panel
            ${getBackgroundColor()}
          `}
        >
          <div className="p-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                {getIcon()}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className={`text-sm font-medium ${getTextColor()}`}>
                  {toast.title}
                </h4>
                
                {toast.message && (
                  <p className={`mt-1 text-sm ${getTextColor()} opacity-80`}>
                    {toast.message}
                  </p>
                )}
                
                {toast.action && (
                  <div className="mt-3">
                    <button
                      onClick={toast.action.onClick}
                      className={`
                        text-sm font-medium underline hover:no-underline
                        ${getTextColor()}
                      `}
                    >
                      {toast.action.label}
                    </button>
                  </div>
                )}
              </div>
              
              <button
                onClick={handleDismiss}
                className={`
                  flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10
                  ${getTextColor()} opacity-60 hover:opacity-100
                `}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface ToastContainerProps {
  toasts: ToastData[];
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  onDismiss,
  position = 'top-right'
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <div className={`fixed z-50 ${getPositionClasses()}`}>
      <div className="space-y-3">
        <AnimatePresence>
          {toasts.map((toast) => (
            <Toast
              key={toast.id}
              toast={toast}
              onDismiss={onDismiss}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Toast hook for managing toasts
export function useToast() {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  const addToast = useCallback((toast: Omit<ToastData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastData = {
      ...toast,
      id,
      duration: toast.duration ?? 5000
    };

    setToasts(prev => [...prev, newToast]);
    return id;
  }, []);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const dismissAll = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const success = useCallback((title: string, message?: string, options?: Partial<ToastData>) => {
    return addToast({ ...options, type: 'success', title, message });
  }, [addToast]);

  const error = useCallback((title: string, message?: string, options?: Partial<ToastData>) => {
    return addToast({ ...options, type: 'error', title, message, duration: options?.duration ?? 7000 });
  }, [addToast]);

  const warning = useCallback((title: string, message?: string, options?: Partial<ToastData>) => {
    return addToast({ ...options, type: 'warning', title, message });
  }, [addToast]);

  const info = useCallback((title: string, message?: string, options?: Partial<ToastData>) => {
    return addToast({ ...options, type: 'info', title, message });
  }, [addToast]);

  return {
    toasts,
    addToast,
    dismissToast,
    dismissAll,
    success,
    error,
    warning,
    info
  };
}
