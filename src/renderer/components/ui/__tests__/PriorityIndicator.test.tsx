import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PriorityIndicator } from '../PriorityIndicator';
import { TodoPriority } from '@shared/types';

describe('PriorityIndicator', () => {
  const priorities: TodoPriority[] = ['very_low', 'low', 'medium', 'high', 'very_high'];

  test('renders icon variant for all priority levels', () => {
    priorities.forEach((priority) => {
      const { container } = render(
        <PriorityIndicator priority={priority} variant="icon" />
      );
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  test('renders badge variant with correct styling', () => {
    render(<PriorityIndicator priority="high" variant="badge" />);
    const badge = screen.getByRole('generic');
    expect(badge).toHaveClass('inline-flex', 'items-center', 'rounded-full');
  });

  test('shows label when showLabel is true', () => {
    render(
      <PriorityIndicator 
        priority="very_high" 
        variant="chip" 
        showLabel={true} 
      />
    );
    expect(screen.getByText('Very High')).toBeInTheDocument();
  });

  test('applies correct size classes', () => {
    const { rerender } = render(
      <PriorityIndicator priority="medium" variant="icon" size="sm" />
    );
    
    rerender(
      <PriorityIndicator priority="medium" variant="icon" size="lg" />
    );
    
    // Test passes if no errors are thrown during rendering
    expect(true).toBe(true);
  });

  test('renders bar variant', () => {
    const { container } = render(
      <PriorityIndicator priority="high" variant="bar" />
    );
    const bar = container.querySelector('.bg-gradient-to-r');
    expect(bar).toBeInTheDocument();
  });

  test('renders glow variant for very high priority', () => {
    render(
      <PriorityIndicator 
        priority="very_high" 
        variant="glow" 
        showLabel={true}
      />
    );
    expect(screen.getByText('Very High')).toBeInTheDocument();
  });

  test('handles animation prop', () => {
    const { rerender } = render(
      <PriorityIndicator priority="medium" animated={true} />
    );
    
    rerender(
      <PriorityIndicator priority="medium" animated={false} />
    );
    
    // Test passes if no errors are thrown
    expect(true).toBe(true);
  });
});
