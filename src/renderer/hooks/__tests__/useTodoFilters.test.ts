import { renderHook, act } from '@testing-library/react';
import { useTodoFilters } from '../useTodoFilters';
import { Todo, Category, TodoStatus, TodoPriority } from '@shared/types';

// Mock data
const mockTodos: Todo[] = [
  {
    id: '1',
    user_id: 'user1',
    category_id: 'cat1',
    title: 'Test Todo 1',
    description: 'Description 1',
    status: 'pending' as TodoStatus,
    priority: 'high' as TodoPriority,
    due_date: new Date('2024-01-01'),
    reminder_at: undefined,
    tags: ['work', 'urgent'],
    position: 1,
    estimated_duration: undefined,
    actual_duration: undefined,
    metadata: {},
    created_at: new Date('2023-12-01'),
    updated_at: new Date('2023-12-01'),
    completed_at: undefined,
    is_deleted: false,
  },
  {
    id: '2',
    user_id: 'user1',
    category_id: 'cat2',
    title: 'Test Todo 2',
    description: 'Description 2',
    status: 'completed' as TodoStatus,
    priority: 'low' as TodoPriority,
    due_date: undefined,
    reminder_at: undefined,
    tags: ['personal'],
    position: 2,
    estimated_duration: undefined,
    actual_duration: undefined,
    metadata: {},
    created_at: new Date('2023-12-02'),
    updated_at: new Date('2023-12-02'),
    completed_at: new Date('2023-12-02'),
    is_deleted: false,
  },
  {
    id: '3',
    user_id: 'user1',
    category_id: undefined,
    title: 'Test Todo 3',
    description: 'Description 3',
    status: 'in_progress' as TodoStatus,
    priority: 'medium' as TodoPriority,
    due_date: undefined,
    reminder_at: undefined,
    tags: ['work'],
    position: 3,
    estimated_duration: undefined,
    actual_duration: undefined,
    metadata: {},
    created_at: new Date('2023-12-03'),
    updated_at: new Date('2023-12-03'),
    completed_at: undefined,
    is_deleted: false,
  },
];

const mockCategories: Category[] = [
  {
    id: 'cat1',
    user_id: 'user1',
    name: 'Work',
    color: '#ff0000',
    icon: '💼',
    sort_order: 1,
    is_default: false,
    created_at: new Date('2023-12-01'),
    updated_at: new Date('2023-12-01'),
  },
  {
    id: 'cat2',
    user_id: 'user1',
    name: 'Personal',
    color: '#00ff00',
    icon: '🏠',
    sort_order: 2,
    is_default: false,
    created_at: new Date('2023-12-01'),
    updated_at: new Date('2023-12-01'),
  },
];

describe('useTodoFilters', () => {
  it('should filter todos by category', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    // Initially, all todos should be shown
    expect(result.current.filteredTodos).toHaveLength(3);

    // Filter by category 'cat1' (Work)
    act(() => {
      result.current.updateFilter('category', ['cat1']);
    });

    expect(result.current.filteredTodos).toHaveLength(1);
    expect(result.current.filteredTodos[0].id).toBe('1');
    expect(result.current.filteredTodos[0].category_id).toBe('cat1');
  });

  it('should filter todos by multiple categories', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    // Filter by both categories
    act(() => {
      result.current.updateFilter('category', ['cat1', 'cat2']);
    });

    expect(result.current.filteredTodos).toHaveLength(2);
    expect(result.current.filteredTodos.map(t => t.id)).toEqual(['1', '2']);
  });

  it('should filter todos by tags', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    // Filter by 'work' tag
    act(() => {
      result.current.updateFilter('tags', ['work']);
    });

    expect(result.current.filteredTodos).toHaveLength(2);
    expect(result.current.filteredTodos.map(t => t.id)).toEqual(['1', '3']);
  });

  it('should combine category and tag filters', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    // Filter by category 'cat1' AND 'work' tag
    act(() => {
      result.current.updateFilter('category', ['cat1']);
      result.current.updateFilter('tags', ['work']);
    });

    expect(result.current.filteredTodos).toHaveLength(1);
    expect(result.current.filteredTodos[0].id).toBe('1');
  });

  it('should include categories in filter options', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    expect(result.current.filterOptions.categories).toEqual(mockCategories);
    expect(result.current.filterOptions.tags).toEqual(['work', 'urgent', 'personal']);
  });

  it('should show active category filters in summary', () => {
    const { result } = renderHook(() => useTodoFilters(mockTodos, mockCategories));

    act(() => {
      result.current.updateFilter('category', ['cat1']);
    });

    expect(result.current.filterSummary.hasActiveFilters).toBe(true);
    expect(result.current.filterSummary.activeFilters).toContain('Category: cat1');
  });
});
