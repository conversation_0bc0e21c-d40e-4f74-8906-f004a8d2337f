import { renderHook, act } from '@testing-library/react';
import { useTodoSorting } from '../useTodoSorting';
import { Todo, TodoStatus, TodoPriority } from '@shared/types';

// Mock data for testing
const createMockTodo = (overrides: Partial<Todo> = {}): Todo => ({
  id: 'test-id',
  user_id: 'user1',
  category_id: 'cat1',
  title: 'Test Todo',
  description: 'Test description',
  status: 'pending' as TodoStatus,
  priority: 'medium' as TodoPriority,
  due_date: undefined,
  reminder_at: undefined,
  tags: [],
  position: 1,
  estimated_duration: undefined,
  actual_duration: undefined,
  metadata: {},
  created_at: new Date('2024-01-01T10:00:00Z'),
  updated_at: new Date('2024-01-01T10:00:00Z'),
  completed_at: undefined,
  is_deleted: false,
  ...overrides,
});

const mockTodos: Todo[] = [
  createMockTodo({
    id: '1',
    title: 'High Priority Task',
    priority: 'high',
    due_date: new Date('2024-01-15T10:00:00Z'),
    created_at: new Date('2024-01-01T10:00:00Z'),
    updated_at: new Date('2024-01-02T10:00:00Z'),
    position: 3,
  }),
  createMockTodo({
    id: '2',
    title: 'Very High Priority Task',
    priority: 'very_high',
    due_date: new Date('2024-01-10T10:00:00Z'),
    created_at: new Date('2024-01-02T10:00:00Z'),
    updated_at: new Date('2024-01-01T10:00:00Z'),
    position: 1,
  }),
  createMockTodo({
    id: '3',
    title: 'Low Priority Task',
    priority: 'low',
    due_date: undefined, // No due date
    created_at: new Date('2024-01-03T10:00:00Z'),
    updated_at: new Date('2024-01-03T10:00:00Z'),
    position: 2,
  }),
  createMockTodo({
    id: '4',
    title: 'Medium Priority Task',
    priority: 'medium',
    due_date: new Date('2024-01-05T10:00:00Z'),
    created_at: new Date('2024-01-04T10:00:00Z'),
    updated_at: new Date('2024-01-04T10:00:00Z'),
    position: 4,
  }),
  createMockTodo({
    id: '5',
    title: 'Very Low Priority Task',
    priority: 'very_low',
    due_date: new Date('2024-01-20T10:00:00Z'),
    created_at: new Date('2024-01-05T10:00:00Z'),
    updated_at: new Date('2024-01-05T10:00:00Z'),
    position: 5,
  }),
];

describe('useTodoSorting', () => {
  it('should initialize with default sort configuration', () => {
    const { result } = renderHook(() => useTodoSorting(mockTodos));
    
    expect(result.current.sortConfig).toEqual({
      field: 'position',
      order: 'asc',
    });
    expect(result.current.activePreset).toBe('default');
  });

  describe('Priority Sorting', () => {
    it('should sort by priority in descending order (high to low)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('priority', 'desc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].priority).toBe('very_high');
      expect(sortedTodos[1].priority).toBe('high');
      expect(sortedTodos[2].priority).toBe('medium');
      expect(sortedTodos[3].priority).toBe('low');
      expect(sortedTodos[4].priority).toBe('very_low');
    });

    it('should sort by priority in ascending order (low to high)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('priority', 'asc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].priority).toBe('very_low');
      expect(sortedTodos[1].priority).toBe('low');
      expect(sortedTodos[2].priority).toBe('medium');
      expect(sortedTodos[3].priority).toBe('high');
      expect(sortedTodos[4].priority).toBe('very_high');
    });

    it('should apply priority high to low preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.applyPreset('priority_high');
      });
      
      expect(result.current.sortConfig).toEqual({
        field: 'priority',
        order: 'desc',
      });
      expect(result.current.activePreset).toBe('priority_high');
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].priority).toBe('very_high');
      expect(sortedTodos[1].priority).toBe('high');
    });
  });

  describe('Due Date Sorting', () => {
    it('should sort by due date in ascending order (soonest first)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('due_date', 'asc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      // Should be: 2024-01-05, 2024-01-10, 2024-01-15, 2024-01-20, then undefined
      expect(sortedTodos[0].id).toBe('4'); // 2024-01-05
      expect(sortedTodos[1].id).toBe('2'); // 2024-01-10
      expect(sortedTodos[2].id).toBe('1'); // 2024-01-15
      expect(sortedTodos[3].id).toBe('5'); // 2024-01-20
      expect(sortedTodos[4].id).toBe('3'); // undefined due_date (should be last)
    });

    it('should sort by due date in descending order (latest first)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('due_date', 'desc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      // Should be: undefined, 2024-01-20, 2024-01-15, 2024-01-10, 2024-01-05
      expect(sortedTodos[0].id).toBe('3'); // undefined due_date (should be first in desc)
      expect(sortedTodos[1].id).toBe('5'); // 2024-01-20
      expect(sortedTodos[2].id).toBe('1'); // 2024-01-15
      expect(sortedTodos[3].id).toBe('2'); // 2024-01-10
      expect(sortedTodos[4].id).toBe('4'); // 2024-01-05
    });

    it('should apply due date soonest preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.applyPreset('due_date_soon');
      });
      
      expect(result.current.sortConfig).toEqual({
        field: 'due_date',
        order: 'asc',
      });
      expect(result.current.activePreset).toBe('due_date_soon');
    });
  });

  describe('Created At Sorting', () => {
    it('should sort by created_at in ascending order (oldest first)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('created_at', 'asc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].id).toBe('1'); // 2024-01-01
      expect(sortedTodos[1].id).toBe('2'); // 2024-01-02
      expect(sortedTodos[2].id).toBe('3'); // 2024-01-03
      expect(sortedTodos[3].id).toBe('4'); // 2024-01-04
      expect(sortedTodos[4].id).toBe('5'); // 2024-01-05
    });

    it('should sort by created_at in descending order (newest first)', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('created_at', 'desc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].id).toBe('5'); // 2024-01-05
      expect(sortedTodos[1].id).toBe('4'); // 2024-01-04
      expect(sortedTodos[2].id).toBe('3'); // 2024-01-03
      expect(sortedTodos[3].id).toBe('2'); // 2024-01-02
      expect(sortedTodos[4].id).toBe('1'); // 2024-01-01
    });

    it('should apply recently created preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.applyPreset('recently_created');
      });
      
      expect(result.current.sortConfig).toEqual({
        field: 'created_at',
        order: 'desc',
      });
      expect(result.current.activePreset).toBe('recently_created');
    });
  });

  describe('Updated At Sorting', () => {
    it('should sort by updated_at correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('updated_at', 'desc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      // Check that sorting is working (exact order depends on mock data)
      expect(sortedTodos).toHaveLength(5);
    });

    it('should apply recently updated preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.applyPreset('recently_updated');
      });
      
      expect(result.current.sortConfig).toEqual({
        field: 'updated_at',
        order: 'desc',
      });
      expect(result.current.activePreset).toBe('recently_updated');
    });
  });

  describe('Position Sorting', () => {
    it('should sort by position correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('position', 'asc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].position).toBe(1);
      expect(sortedTodos[1].position).toBe(2);
      expect(sortedTodos[2].position).toBe(3);
      expect(sortedTodos[3].position).toBe(4);
      expect(sortedTodos[4].position).toBe(5);
    });
  });

  describe('Title Sorting', () => {
    it('should sort by title alphabetically', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('title', 'asc');
      });
      
      const sortedTodos = result.current.sortedTodos;
      const titles = sortedTodos.map(todo => todo.title);
      const sortedTitles = [...titles].sort();
      expect(titles).toEqual(sortedTitles);
    });

    it('should apply alphabetical preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.applyPreset('alphabetical');
      });
      
      expect(result.current.sortConfig).toEqual({
        field: 'title',
        order: 'asc',
      });
      expect(result.current.activePreset).toBe('alphabetical');
    });
  });

  describe('Sort Controls', () => {
    it('should toggle sort order correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      // Start with ascending
      act(() => {
        result.current.updateSort('priority', 'asc');
      });
      
      expect(result.current.sortConfig.order).toBe('asc');
      
      // Toggle to descending
      act(() => {
        result.current.toggleSortOrder();
      });
      
      expect(result.current.sortConfig.order).toBe('desc');
      expect(result.current.activePreset).toBe(''); // Should clear preset
    });

    it('should provide correct sort indicators', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('priority', 'asc');
      });
      
      expect(result.current.getSortIndicator('priority')).toBe('↑');
      expect(result.current.getSortIndicator('title')).toBe(null);
      
      act(() => {
        result.current.updateSort('priority', 'desc');
      });
      
      expect(result.current.getSortIndicator('priority')).toBe('↓');
    });

    it('should identify sorted fields correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('priority');
      });
      
      expect(result.current.isSortedBy('priority')).toBe(true);
      expect(result.current.isSortedBy('title')).toBe(false);
    });

    it('should provide correct sort description', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));
      
      act(() => {
        result.current.updateSort('priority', 'desc');
      });
      
      expect(result.current.sortDescription).toBe('priority (descending)');
      
      act(() => {
        result.current.applyPreset('priority_high');
      });
      
      expect(result.current.sortDescription).toBe('Most important first');
    });
  });

  describe('Smart Sorting Presets', () => {
    it('should apply smart priority preset correctly', () => {
      const { result } = renderHook(() => useTodoSorting(mockTodos));

      act(() => {
        result.current.applyPreset('smart_priority');
      });

      expect(result.current.activePreset).toBe('smart_priority');
      expect(result.current.sortDescription).toBe('Priority with urgency consideration');

      // Smart priority should consider both priority and due date urgency
      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos).toHaveLength(5);
    });

    it('should apply overdue first preset correctly', () => {
      const todosWithOverdue = [
        createMockTodo({
          id: '1',
          priority: 'low',
          due_date: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday (overdue)
        }),
        createMockTodo({
          id: '2',
          priority: 'very_high',
          due_date: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
        }),
        createMockTodo({
          id: '3',
          priority: 'high',
          due_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago (overdue)
        }),
      ];

      const { result } = renderHook(() => useTodoSorting(todosWithOverdue));

      act(() => {
        result.current.applyPreset('overdue_first');
      });

      expect(result.current.activePreset).toBe('overdue_first');
      expect(result.current.sortDescription).toBe('Overdue and urgent tasks first');

      const sortedTodos = result.current.sortedTodos;
      // Overdue tasks should come first, regardless of priority
      expect(sortedTodos[0].id).toBe('3'); // Most overdue first
      expect(sortedTodos[1].id).toBe('1'); // Less overdue second
      expect(sortedTodos[2].id).toBe('2'); // Not overdue last
    });

    it('should handle smart priority sorting with mixed urgency levels', () => {
      const todosWithMixedUrgency = [
        createMockTodo({
          id: '1',
          priority: 'medium',
          due_date: new Date(Date.now() + 60 * 60 * 1000) // Due in 1 hour (very urgent)
        }),
        createMockTodo({
          id: '2',
          priority: 'very_high',
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Due in 30 days (low urgency)
        }),
        createMockTodo({
          id: '3',
          priority: 'low',
          due_date: undefined // No due date (no urgency)
        }),
      ];

      const { result } = renderHook(() => useTodoSorting(todosWithMixedUrgency));

      act(() => {
        result.current.applyPreset('smart_priority');
      });

      const sortedTodos = result.current.sortedTodos;
      // Should balance priority and urgency
      expect(sortedTodos).toHaveLength(3);
    });
  });

  describe('Estimated Duration Sorting', () => {
    it('should sort by estimated duration correctly', () => {
      const todosWithDuration = [
        createMockTodo({ id: '1', estimated_duration: 'PT2H' }), // 2 hours
        createMockTodo({ id: '2', estimated_duration: 'PT30M' }), // 30 minutes
        createMockTodo({ id: '3', estimated_duration: 'PT1H30M' }), // 1.5 hours
        createMockTodo({ id: '4', estimated_duration: undefined }), // No duration
      ];

      const { result } = renderHook(() => useTodoSorting(todosWithDuration));

      act(() => {
        result.current.updateSort('estimated_duration', 'asc');
      });

      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].id).toBe('4'); // No duration (0 minutes)
      expect(sortedTodos[1].id).toBe('2'); // 30 minutes
      expect(sortedTodos[2].id).toBe('3'); // 90 minutes
      expect(sortedTodos[3].id).toBe('1'); // 120 minutes
    });

    it('should handle invalid duration formats gracefully', () => {
      const todosWithInvalidDuration = [
        createMockTodo({ id: '1', estimated_duration: 'invalid' }),
        createMockTodo({ id: '2', estimated_duration: 'PT1H' }),
      ];

      const { result } = renderHook(() => useTodoSorting(todosWithInvalidDuration));

      act(() => {
        result.current.updateSort('estimated_duration', 'asc');
      });

      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos).toHaveLength(2);
      // Should not throw error and should handle invalid duration as 0
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty todo list', () => {
      const { result } = renderHook(() => useTodoSorting([]));

      expect(result.current.sortedTodos).toEqual([]);
    });

    it('should handle todos with null/undefined dates', () => {
      const todosWithNullDates = [
        createMockTodo({ id: '1', due_date: undefined }),
        createMockTodo({ id: '2', due_date: new Date('2024-01-01') }),
        createMockTodo({ id: '3', due_date: undefined }),
      ];

      const { result } = renderHook(() => useTodoSorting(todosWithNullDates));

      act(() => {
        result.current.updateSort('due_date', 'asc');
      });

      const sortedTodos = result.current.sortedTodos;
      expect(sortedTodos[0].id).toBe('2'); // Has due date, should be first
      expect(sortedTodos[1].due_date).toBeUndefined(); // Null dates at end
      expect(sortedTodos[2].due_date).toBeUndefined();
    });
  });
});
