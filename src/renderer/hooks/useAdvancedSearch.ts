import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  SearchEngine, 
  SearchQuery, 
  SearchResult, 
  SearchSuggestion, 
  SearchMetrics,
  SearchFilters,
  SearchSortOption 
} from '@renderer/services/search-engine.service';
import { Todo } from '@shared/types';
import { SearchAnalyticsData } from '@renderer/components/search/SearchAnalytics';

export interface UseAdvancedSearchOptions {
  autoSearch?: boolean;
  debounceMs?: number;
  maxResults?: number;
  enableAnalytics?: boolean;
  enableSuggestions?: boolean;
}

export interface UseAdvancedSearchReturn {
  // Search state
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult[];
  suggestions: SearchSuggestion[];
  isLoading: boolean;
  error: string | null;
  
  // Search actions
  search: (searchQuery?: string) => Promise<void>;
  clearSearch: () => void;
  
  // Filters
  filters: SearchFilters;
  updateFilter: <K extends keyof SearchFilters>(key: K, value: SearchFilters[K]) => void;
  clearFilters: () => void;
  
  // Sorting
  sortBy: SearchSortOption;
  sortOrder: 'asc' | 'desc';
  setSortBy: (sortBy: SearchSortOption) => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  
  // Analytics
  metrics: SearchMetrics | null;
  analytics: SearchAnalyticsData | null;
  
  // Index management
  indexTodos: (todos: Todo[]) => Promise<void>;
  isIndexed: boolean;
}

const defaultFilters: SearchFilters = {
  categories: [],
  priorities: [],
  statuses: [],
  tags: [],
  dateRange: {
    type: 'created',
  },
  hasAttachments: null,
  hasReminder: null,
};

export const useAdvancedSearch = (
  searchEngine: SearchEngine,
  options: UseAdvancedSearchOptions = {}
): UseAdvancedSearchReturn => {
  const {
    autoSearch = true,
    debounceMs = 300,
    maxResults = 100,
    enableAnalytics = true,
    enableSuggestions = true,
  } = options;

  // Search state
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isIndexed, setIsIndexed] = useState(false);

  // Filter state
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);
  
  // Sort state
  const [sortBy, setSortBy] = useState<SearchSortOption>('relevance');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Analytics state
  const [metrics, setMetrics] = useState<SearchMetrics | null>(null);
  const [analytics, setAnalytics] = useState<SearchAnalyticsData | null>(null);

  // Debounced search query
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  // Debounce the query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  // Auto search when debounced query changes
  useEffect(() => {
    if (autoSearch && debouncedQuery.trim() && isIndexed) {
      search(debouncedQuery);
    } else if (!debouncedQuery.trim()) {
      setResults([]);
      setAnalytics(null);
      setMetrics(null);
    }
  }, [debouncedQuery, filters, sortBy, sortOrder, autoSearch, isIndexed]);

  // Get suggestions when query changes
  useEffect(() => {
    if (enableSuggestions && query.length > 1 && isIndexed) {
      getSuggestions(query);
    } else {
      setSuggestions([]);
    }
  }, [query, enableSuggestions, isIndexed]);

  // Search function
  const search = useCallback(async (searchQuery?: string) => {
    const queryToSearch = searchQuery || query;
    
    if (!queryToSearch.trim() || !isIndexed) {
      setResults([]);
      setAnalytics(null);
      setMetrics(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const searchParams: SearchQuery = {
        text: queryToSearch,
        filters,
        sortBy,
        sortOrder,
      };

      const searchResults = await searchEngine.search(searchParams);
      const limitedResults = searchResults.slice(0, maxResults);
      
      setResults(limitedResults);

      // Generate analytics if enabled
      if (enableAnalytics) {
        const analyticsData = generateAnalytics(limitedResults, queryToSearch);
        setAnalytics(analyticsData);
      }

    } catch (err) {
      console.error('Search failed:', err);
      setError(err instanceof Error ? err.message : 'Search failed');
      setResults([]);
      setAnalytics(null);
    } finally {
      setIsLoading(false);
    }
  }, [query, filters, sortBy, sortOrder, maxResults, enableAnalytics, isIndexed, searchEngine]);

  // Get suggestions
  const getSuggestions = useCallback(async (searchQuery: string) => {
    try {
      const suggestionResults = await searchEngine.getSuggestions(searchQuery);
      setSuggestions(suggestionResults);
    } catch (err) {
      console.error('Failed to get suggestions:', err);
      setSuggestions([]);
    }
  }, [searchEngine]);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setSuggestions([]);
    setAnalytics(null);
    setMetrics(null);
    setError(null);
  }, []);

  // Update filter
  const updateFilter = useCallback(<K extends keyof SearchFilters>(
    key: K, 
    value: SearchFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Index todos
  const indexTodos = useCallback(async (todos: Todo[]) => {
    try {
      setIsLoading(true);
      await searchEngine.indexTodos(todos);
      setIsIndexed(true);
    } catch (err) {
      console.error('Failed to index todos:', err);
      setError('Failed to index todos for search');
    } finally {
      setIsLoading(false);
    }
  }, [searchEngine]);

  // Generate analytics data
  const generateAnalytics = useCallback((
    searchResults: SearchResult[], 
    searchQuery: string
  ): SearchAnalyticsData => {
    // Calculate category distribution
    const categoryMap = new Map<string, number>();
    searchResults.forEach(result => {
      const categoryId = result.item.category_id || 'uncategorized';
      categoryMap.set(categoryId, (categoryMap.get(categoryId) || 0) + 1);
    });

    const categories = Array.from(categoryMap.entries()).map(([name, count]) => ({
      name: name === 'uncategorized' ? 'Uncategorized' : name,
      count,
    }));

    // Calculate average relevance
    const avgRelevance = searchResults.length > 0
      ? searchResults.reduce((sum, result) => sum + result.score, 0) / searchResults.length
      : 0;

    // Calculate top match fields
    const fieldMap = new Map<string, number>();
    searchResults.forEach(result => {
      result.matchReasons.forEach(reason => {
        fieldMap.set(reason.field, (fieldMap.get(reason.field) || 0) + 1);
      });
    });

    const topMatches = Array.from(fieldMap.entries())
      .map(([field, count]) => ({ field, count }))
      .sort((a, b) => b.count - a.count);

    return {
      totalResults: searchResults.length,
      categories,
      avgRelevance,
      searchTime: metrics?.duration || 0,
      queryComplexity: searchQuery.split(' ').length,
      topMatches,
    };
  }, [metrics]);

  // Listen to search engine events
  useEffect(() => {
    const handleSearchPerformed = (searchMetrics: SearchMetrics) => {
      setMetrics(searchMetrics);
    };

    searchEngine.on('searchPerformed', handleSearchPerformed);

    return () => {
      // Note: In a real implementation, you'd want to remove the listener
      // This is a simplified version
    };
  }, [searchEngine]);

  return {
    // Search state
    query,
    setQuery,
    results,
    suggestions,
    isLoading,
    error,
    
    // Search actions
    search,
    clearSearch,
    
    // Filters
    filters,
    updateFilter,
    clearFilters,
    
    // Sorting
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
    
    // Analytics
    metrics,
    analytics,
    
    // Index management
    indexTodos,
    isIndexed,
  };
};
