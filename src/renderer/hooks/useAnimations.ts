import { useEffect, useState } from 'react';
import { useAnimation, AnimationControls } from 'framer-motion';

// Hook for managing reduced motion preferences
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// Hook for performance-aware animations
export const usePerformantAnimation = () => {
  const prefersReducedMotion = useReducedMotion();
  const controls = useAnimation();

  const animateWithPerformance = (
    animation: any,
    options?: { skipIfReduced?: boolean }
  ) => {
    if (prefersReducedMotion && options?.skipIfReduced) {
      return Promise.resolve();
    }

    if (prefersReducedMotion) {
      // Simplified animation for reduced motion
      return controls.start({
        ...animation,
        transition: { duration: 0.01 },
      });
    }

    return controls.start(animation);
  };

  return {
    controls,
    prefersReducedMotion,
    animateWithPerformance,
  };
};

// Hook for staggered list animations
export const useStaggeredAnimation = (itemCount: number) => {
  const prefersReducedMotion = useReducedMotion();
  
  const getStaggerDelay = (index: number) => {
    if (prefersReducedMotion) return 0;
    return index * 0.05; // 50ms stagger
  };

  const getItemVariants = (index: number) => ({
    initial: {
      opacity: 0,
      y: prefersReducedMotion ? 0 : 20,
      scale: prefersReducedMotion ? 1 : 0.95,
    },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: getStaggerDelay(index),
        duration: prefersReducedMotion ? 0.01 : 0.3,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      y: prefersReducedMotion ? 0 : -20,
      scale: prefersReducedMotion ? 1 : 0.95,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.2,
      },
    },
  });

  return {
    getItemVariants,
    getStaggerDelay,
    prefersReducedMotion,
  };
};

// Hook for scroll-triggered animations
export const useScrollAnimation = () => {
  const [isInView, setIsInView] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  const scrollVariants = {
    initial: {
      opacity: prefersReducedMotion ? 1 : 0,
      y: prefersReducedMotion ? 0 : 50,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.6,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  return {
    scrollVariants,
    isInView,
    setIsInView,
    prefersReducedMotion,
  };
};

// Hook for hover animations with performance optimization
export const useHoverAnimation = () => {
  const prefersReducedMotion = useReducedMotion();
  const [isHovered, setIsHovered] = useState(false);

  const hoverVariants = {
    initial: {
      scale: 1,
      y: 0,
    },
    hover: {
      scale: prefersReducedMotion ? 1 : 1.02,
      y: prefersReducedMotion ? 0 : -2,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    tap: {
      scale: prefersReducedMotion ? 1 : 0.98,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.1,
      },
    },
  };

  const hoverProps = {
    onMouseEnter: () => setIsHovered(true),
    onMouseLeave: () => setIsHovered(false),
    whileHover: 'hover',
    whileTap: 'tap',
    variants: hoverVariants,
  };

  return {
    hoverVariants,
    hoverProps,
    isHovered,
    prefersReducedMotion,
  };
};

// Hook for loading animations
export const useLoadingAnimation = () => {
  const prefersReducedMotion = useReducedMotion();

  const spinnerVariants = {
    animate: {
      rotate: prefersReducedMotion ? 0 : 360,
      transition: {
        duration: prefersReducedMotion ? 0 : 1,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'linear',
      },
    },
  };

  const pulseVariants = {
    animate: {
      scale: prefersReducedMotion ? 1 : [1, 1.1, 1],
      opacity: prefersReducedMotion ? 1 : [0.7, 1, 0.7],
      transition: {
        duration: prefersReducedMotion ? 0 : 1.5,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  const dotsVariants = {
    animate: {
      y: prefersReducedMotion ? 0 : [0, -10, 0],
      transition: {
        duration: prefersReducedMotion ? 0 : 0.6,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return {
    spinnerVariants,
    pulseVariants,
    dotsVariants,
    prefersReducedMotion,
  };
};

// Hook for modal animations
export const useModalAnimation = () => {
  const prefersReducedMotion = useReducedMotion();

  const modalVariants = {
    initial: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.9,
      y: prefersReducedMotion ? 0 : 20,
    },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.3,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.9,
      y: prefersReducedMotion ? 0 : 20,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  const overlayVariants = {
    initial: {
      opacity: 0,
    },
    animate: {
      opacity: 1,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.2,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.2,
      },
    },
  };

  return {
    modalVariants,
    overlayVariants,
    prefersReducedMotion,
  };
};

// Hook for page transitions
export const usePageTransition = () => {
  const prefersReducedMotion = useReducedMotion();

  const pageVariants = {
    initial: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 0.95,
      filter: prefersReducedMotion ? 'blur(0px)' : 'blur(4px)',
    },
    animate: {
      opacity: 1,
      scale: 1,
      filter: 'blur(0px)',
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.4,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      scale: prefersReducedMotion ? 1 : 1.05,
      filter: prefersReducedMotion ? 'blur(0px)' : 'blur(4px)',
      transition: {
        duration: prefersReducedMotion ? 0.01 : 0.3,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  };

  return {
    pageVariants,
    prefersReducedMotion,
  };
};
