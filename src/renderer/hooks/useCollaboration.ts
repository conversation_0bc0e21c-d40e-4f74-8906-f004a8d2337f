import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  CollaborationUser, 
  CollaborationSettings,
  PresenceUpdate,
  LiveUpdate,
  ConflictData,
  TypingData,
  collaborationService 
} from '@renderer/services/collaboration.service';
import { Todo } from '@shared/types';

export interface UseCollaborationOptions {
  autoConnect?: boolean;
  enablePresence?: boolean;
  enableTypingIndicators?: boolean;
  typingDebounceMs?: number;
}

export interface UseCollaborationReturn {
  // Connection state
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  
  // Users
  users: CollaborationUser[];
  currentUser: CollaborationUser | null;
  onlineUsers: CollaborationUser[];
  
  // Presence
  updatePresence: (todoId?: string, field?: string) => void;
  setIdle: () => void;
  getUsersEditingTodo: (todoId: string) => CollaborationUser[];
  
  // Live updates
  broadcastTodoUpdate: (todoId: string, changes: Partial<Todo>, type?: LiveUpdate['type']) => void;
  liveUpdates: LiveUpdate[];
  clearLiveUpdates: () => void;
  
  // Typing indicators
  startTyping: (todoId: string, field: string) => void;
  stopTyping: (todoId: string, field: string) => void;
  getTypingUsers: (todoId: string, field?: string) => CollaborationUser[];
  
  // Conflicts
  conflicts: ConflictData[];
  resolveConflict: (conflictId: string, resolution: 'accept' | 'reject' | 'merge') => void;
  
  // Settings
  settings: CollaborationSettings | null;
  updateSettings: (settings: Partial<CollaborationSettings>) => void;
  
  // Events
  onPresenceUpdate: (callback: (update: PresenceUpdate) => void) => () => void;
  onLiveUpdate: (callback: (update: LiveUpdate) => void) => () => void;
  onConflict: (callback: (conflict: ConflictData) => void) => () => void;
}

export const useCollaboration = (
  options: UseCollaborationOptions = {}
): UseCollaborationReturn => {
  const {
    autoConnect = false,
    enablePresence = true,
    enableTypingIndicators = true,
    typingDebounceMs = 1000,
  } = options;

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [users, setUsers] = useState<CollaborationUser[]>([]);
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null);
  const [liveUpdates, setLiveUpdates] = useState<LiveUpdate[]>([]);
  const [conflicts, setConflicts] = useState<ConflictData[]>([]);
  const [settings, setSettings] = useState<CollaborationSettings | null>(null);
  const [typingUsers, setTypingUsers] = useState<Map<string, TypingData>>(new Map());

  // Refs for cleanup
  const typingTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const eventCleanup = useRef<(() => void)[]>([]);

  // Initialize collaboration
  useEffect(() => {
    const initialize = async () => {
      if (autoConnect) {
        try {
          await collaborationService.connect();
        } catch (error) {
          console.error('Auto-connect failed:', error);
        }
      }

      // Get initial state
      setCurrentUser(collaborationService.getCurrentUser());
      setUsers(collaborationService.getUsers());
      setIsConnected(collaborationService.isConnectedToCollaboration());
      setSettings(collaborationService.getSettings());
    };

    initialize();

    // Set up event listeners
    const handleConnected = () => {
      setIsConnected(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleUsersUpdated = (updatedUsers: CollaborationUser[]) => {
      setUsers(updatedUsers);
    };

    const handleUserPresenceChanged = ({ user }: { user: CollaborationUser }) => {
      setUsers(prev => prev.map(u => u.id === user.id ? user : u));
    };

    const handleLiveUpdate = (update: LiveUpdate) => {
      setLiveUpdates(prev => [...prev.slice(-19), update]); // Keep last 20 updates
    };

    const handleConflictDetected = (conflict: ConflictData) => {
      setConflicts(prev => [...prev, conflict]);
    };

    const handleSettingsUpdated = (newSettings: CollaborationSettings) => {
      setSettings(newSettings);
    };

    const handleUserTyping = (data: TypingData) => {
      const key = `${data.userId}-${data.todoId}-${data.field}`;
      
      if (data.isTyping) {
        setTypingUsers(prev => new Map(prev.set(key, data)));
        
        // Clear existing timeout
        const existingTimeout = typingTimeouts.current.get(key);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }
        
        // Set new timeout to clear typing indicator
        const timeout = setTimeout(() => {
          setTypingUsers(prev => {
            const newMap = new Map(prev);
            newMap.delete(key);
            return newMap;
          });
          typingTimeouts.current.delete(key);
        }, typingDebounceMs);
        
        typingTimeouts.current.set(key, timeout);
      } else {
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          newMap.delete(key);
          return newMap;
        });
        
        const timeout = typingTimeouts.current.get(key);
        if (timeout) {
          clearTimeout(timeout);
          typingTimeouts.current.delete(key);
        }
      }
    };

    // Register event listeners
    collaborationService.on('connected', handleConnected);
    collaborationService.on('disconnected', handleDisconnected);
    collaborationService.on('usersUpdated', handleUsersUpdated);
    collaborationService.on('userPresenceChanged', handleUserPresenceChanged);
    collaborationService.on('liveUpdate', handleLiveUpdate);
    collaborationService.on('conflictDetected', handleConflictDetected);
    collaborationService.on('settingsUpdated', handleSettingsUpdated);
    collaborationService.on('userTyping', handleUserTyping);
    collaborationService.on('remoteUserTyping', handleUserTyping);

    // Store cleanup functions
    eventCleanup.current = [
      () => collaborationService.off('connected', handleConnected),
      () => collaborationService.off('disconnected', handleDisconnected),
      () => collaborationService.off('usersUpdated', handleUsersUpdated),
      () => collaborationService.off('userPresenceChanged', handleUserPresenceChanged),
      () => collaborationService.off('liveUpdate', handleLiveUpdate),
      () => collaborationService.off('conflictDetected', handleConflictDetected),
      () => collaborationService.off('settingsUpdated', handleSettingsUpdated),
      () => collaborationService.off('userTyping', handleUserTyping),
      () => collaborationService.off('remoteUserTyping', handleUserTyping),
    ];

    return () => {
      // Cleanup event listeners
      eventCleanup.current.forEach(cleanup => cleanup());
      
      // Clear typing timeouts
      typingTimeouts.current.forEach(timeout => clearTimeout(timeout));
      typingTimeouts.current.clear();
    };
  }, [autoConnect, typingDebounceMs]);

  // Connection methods
  const connect = useCallback(async () => {
    await collaborationService.connect();
  }, []);

  const disconnect = useCallback(() => {
    collaborationService.disconnect();
  }, []);

  // Presence methods
  const updatePresence = useCallback((todoId?: string, field?: string) => {
    if (enablePresence) {
      collaborationService.updatePresence(todoId, field);
    }
  }, [enablePresence]);

  const setIdle = useCallback(() => {
    if (enablePresence) {
      collaborationService.setUserIdle();
    }
  }, [enablePresence]);

  const getUsersEditingTodo = useCallback((todoId: string) => {
    return collaborationService.getUsersEditingTodo(todoId);
  }, []);

  // Live update methods
  const broadcastTodoUpdate = useCallback((
    todoId: string, 
    changes: Partial<Todo>, 
    type: LiveUpdate['type'] = 'todo_updated'
  ) => {
    collaborationService.broadcastTodoUpdate(todoId, changes, type);
  }, []);

  const clearLiveUpdates = useCallback(() => {
    setLiveUpdates([]);
  }, []);

  // Typing indicator methods
  const startTyping = useCallback((todoId: string, field: string) => {
    if (enableTypingIndicators) {
      collaborationService.startTyping(todoId, field);
    }
  }, [enableTypingIndicators]);

  const stopTyping = useCallback((todoId: string, field: string) => {
    if (enableTypingIndicators) {
      collaborationService.stopTyping(todoId, field);
    }
  }, [enableTypingIndicators]);

  const getTypingUsers = useCallback((todoId: string, field?: string) => {
    const typingData = Array.from(typingUsers.values()).filter(data => 
      data.todoId === todoId && (!field || data.field === field) && data.isTyping
    );
    
    return typingData
      .map(data => users.find(user => user.id === data.userId))
      .filter(Boolean) as CollaborationUser[];
  }, [typingUsers, users]);

  // Conflict resolution
  const resolveConflict = useCallback((conflictId: string, resolution: 'accept' | 'reject' | 'merge') => {
    // Remove conflict from list
    setConflicts(prev => prev.filter(conflict => 
      `${conflict.todoId}-${conflict.conflictingUsers.join('-')}` !== conflictId
    ));
    
    // In a real implementation, apply the resolution
    console.log(`Conflict ${conflictId} resolved with: ${resolution}`);
  }, []);

  // Settings
  const updateSettings = useCallback((newSettings: Partial<CollaborationSettings>) => {
    collaborationService.updateSettings(newSettings);
  }, []);

  // Event subscription methods
  const onPresenceUpdate = useCallback((callback: (update: PresenceUpdate) => void) => {
    collaborationService.on('presenceUpdate', callback);
    return () => collaborationService.off('presenceUpdate', callback);
  }, []);

  const onLiveUpdate = useCallback((callback: (update: LiveUpdate) => void) => {
    collaborationService.on('liveUpdate', callback);
    return () => collaborationService.off('liveUpdate', callback);
  }, []);

  const onConflict = useCallback((callback: (conflict: ConflictData) => void) => {
    collaborationService.on('conflictDetected', callback);
    return () => collaborationService.off('conflictDetected', callback);
  }, []);

  // Computed values
  const onlineUsers = users.filter(user => user.isOnline);

  return {
    // Connection state
    isConnected,
    connect,
    disconnect,
    
    // Users
    users,
    currentUser,
    onlineUsers,
    
    // Presence
    updatePresence,
    setIdle,
    getUsersEditingTodo,
    
    // Live updates
    broadcastTodoUpdate,
    liveUpdates,
    clearLiveUpdates,
    
    // Typing indicators
    startTyping,
    stopTyping,
    getTypingUsers,
    
    // Conflicts
    conflicts,
    resolveConflict,
    
    // Settings
    settings,
    updateSettings,
    
    // Events
    onPresenceUpdate,
    onLiveUpdate,
    onConflict,
  };
};
