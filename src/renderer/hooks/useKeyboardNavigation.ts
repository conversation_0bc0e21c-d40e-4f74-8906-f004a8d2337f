import { useEffect, useCallback, useRef } from 'react';

export interface KeyboardNavigationOptions {
  onEnter?: () => void;
  onEscape?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onTab?: (shiftKey: boolean) => void;
  onSpace?: () => void;
  onHome?: () => void;
  onEnd?: () => void;
  onPageUp?: () => void;
  onPageDown?: () => void;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  disabled?: boolean;
}

/**
 * Hook for handling keyboard navigation with accessibility best practices
 */
export const useKeyboardNavigation = (options: KeyboardNavigationOptions = {}) => {
  const {
    onEnter,
    onEscape,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onTab,
    onSpace,
    onHome,
    onEnd,
    onPageUp,
    onPageDown,
    preventDefault = true,
    stopPropagation = false,
    disabled = false,
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return;

    const { key, shiftKey } = event;

    let handled = false;

    switch (key) {
      case 'Enter':
        if (onEnter) {
          onEnter();
          handled = true;
        }
        break;
      case 'Escape':
        if (onEscape) {
          onEscape();
          handled = true;
        }
        break;
      case 'ArrowUp':
        if (onArrowUp) {
          onArrowUp();
          handled = true;
        }
        break;
      case 'ArrowDown':
        if (onArrowDown) {
          onArrowDown();
          handled = true;
        }
        break;
      case 'ArrowLeft':
        if (onArrowLeft) {
          onArrowLeft();
          handled = true;
        }
        break;
      case 'ArrowRight':
        if (onArrowRight) {
          onArrowRight();
          handled = true;
        }
        break;
      case 'Tab':
        if (onTab) {
          onTab(shiftKey);
          handled = true;
        }
        break;
      case ' ':
        if (onSpace) {
          onSpace();
          handled = true;
        }
        break;
      case 'Home':
        if (onHome) {
          onHome();
          handled = true;
        }
        break;
      case 'End':
        if (onEnd) {
          onEnd();
          handled = true;
        }
        break;
      case 'PageUp':
        if (onPageUp) {
          onPageUp();
          handled = true;
        }
        break;
      case 'PageDown':
        if (onPageDown) {
          onPageDown();
          handled = true;
        }
        break;
    }

    if (handled) {
      if (preventDefault) event.preventDefault();
      if (stopPropagation) event.stopPropagation();
    }
  }, [
    onEnter, onEscape, onArrowUp, onArrowDown, onArrowLeft, onArrowRight,
    onTab, onSpace, onHome, onEnd, onPageUp, onPageDown,
    preventDefault, stopPropagation, disabled
  ]);

  return { handleKeyDown };
};

/**
 * Hook for managing focus within a container (focus trap)
 */
export const useFocusTrap = (isActive: boolean = true) => {
  const containerRef = useRef<HTMLElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const getFocusableElements = useCallback((container: HTMLElement): HTMLElement[] => {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors));
  }, []);

  const trapFocus = useCallback((event: KeyboardEvent) => {
    if (!isActive || !containerRef.current || event.key !== 'Tab') return;

    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }, [isActive, getFocusableElements]);

  const focusFirst = useCallback(() => {
    if (!containerRef.current) return;
    
    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }, [getFocusableElements]);

  const focusLast = useCallback(() => {
    if (!containerRef.current) return;
    
    const focusableElements = getFocusableElements(containerRef.current);
    if (focusableElements.length > 0) {
      focusableElements[focusableElements.length - 1].focus();
    }
  }, [getFocusableElements]);

  useEffect(() => {
    if (isActive && containerRef.current) {
      // Store the previously focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the first focusable element
      focusFirst();
      
      // Add event listener for focus trapping
      document.addEventListener('keydown', trapFocus);
      
      return () => {
        document.removeEventListener('keydown', trapFocus);
        
        // Restore focus to the previously focused element
        if (previousFocusRef.current) {
          previousFocusRef.current.focus();
        }
      };
    }
  }, [isActive, trapFocus, focusFirst]);

  return {
    containerRef,
    focusFirst,
    focusLast,
    getFocusableElements: () => containerRef.current ? getFocusableElements(containerRef.current) : []
  };
};

/**
 * Hook for managing roving tabindex (for lists, grids, etc.)
 */
export const useRovingTabindex = (itemCount: number, orientation: 'horizontal' | 'vertical' = 'vertical') => {
  const activeIndexRef = useRef(0);
  const containerRef = useRef<HTMLElement>(null);

  const setActiveIndex = useCallback((index: number) => {
    if (index < 0 || index >= itemCount) return;
    
    activeIndexRef.current = index;
    
    // Update tabindex for all items
    if (containerRef.current) {
      const items = containerRef.current.querySelectorAll('[role="option"], [role="tab"], [role="menuitem"]');
      items.forEach((item, i) => {
        (item as HTMLElement).tabIndex = i === index ? 0 : -1;
      });
    }
  }, [itemCount]);

  const moveNext = useCallback(() => {
    const nextIndex = (activeIndexRef.current + 1) % itemCount;
    setActiveIndex(nextIndex);
    return nextIndex;
  }, [itemCount, setActiveIndex]);

  const movePrevious = useCallback(() => {
    const prevIndex = activeIndexRef.current === 0 ? itemCount - 1 : activeIndexRef.current - 1;
    setActiveIndex(prevIndex);
    return prevIndex;
  }, [itemCount, setActiveIndex]);

  const moveFirst = useCallback(() => {
    setActiveIndex(0);
    return 0;
  }, [setActiveIndex]);

  const moveLast = useCallback(() => {
    const lastIndex = itemCount - 1;
    setActiveIndex(lastIndex);
    return lastIndex;
  }, [itemCount, setActiveIndex]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    let handled = false;
    let newIndex = activeIndexRef.current;

    switch (event.key) {
      case 'ArrowDown':
        if (orientation === 'vertical') {
          newIndex = moveNext();
          handled = true;
        }
        break;
      case 'ArrowUp':
        if (orientation === 'vertical') {
          newIndex = movePrevious();
          handled = true;
        }
        break;
      case 'ArrowRight':
        if (orientation === 'horizontal') {
          newIndex = moveNext();
          handled = true;
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal') {
          newIndex = movePrevious();
          handled = true;
        }
        break;
      case 'Home':
        newIndex = moveFirst();
        handled = true;
        break;
      case 'End':
        newIndex = moveLast();
        handled = true;
        break;
    }

    if (handled) {
      event.preventDefault();
      
      // Focus the new active item
      if (containerRef.current) {
        const items = containerRef.current.querySelectorAll('[role="option"], [role="tab"], [role="menuitem"]');
        const activeItem = items[newIndex] as HTMLElement;
        if (activeItem) {
          activeItem.focus();
        }
      }
    }
  }, [orientation, moveNext, movePrevious, moveFirst, moveLast]);

  return {
    containerRef,
    activeIndex: activeIndexRef.current,
    setActiveIndex,
    handleKeyDown,
    moveNext,
    movePrevious,
    moveFirst,
    moveLast
  };
};
