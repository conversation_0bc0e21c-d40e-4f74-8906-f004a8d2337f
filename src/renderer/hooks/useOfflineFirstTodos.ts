import { useState, useEffect, useCallback } from 'react';
import { Todo, TodoStatus, TodoPriority } from '@shared/types';
import { offlineFirstTodoService, OfflineFirstOptions, OfflineStatus } from '../services/offline-first-todo.service';

export interface TodoFilters {
  status?: TodoStatus;
  priority?: TodoPriority;
  category_id?: string;
  due_date_from?: Date;
  due_date_to?: Date;
  tags?: string[];
  search?: string;
  is_completed?: boolean;
  is_overdue?: boolean;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface CreateTodoRequest {
  title: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

/**
 * React hook for offline-first todo operations
 * 
 * This hook provides:
 * - Offline-first CRUD operations
 * - Optimistic updates for immediate UI feedback
 * - Automatic retry and sync when back online
 * - Loading states and error handling
 * - Real-time offline status monitoring
 */
export const useOfflineFirstTodos = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [offlineStatus, setOfflineStatus] = useState<OfflineStatus | null>(null);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Load offline status
  const loadOfflineStatus = useCallback(async () => {
    try {
      const status = await offlineFirstTodoService.getOfflineStatus();
      setOfflineStatus(status);
    } catch (error) {
      console.warn('Failed to load offline status:', error);
    }
  }, []);

  // Load todos with offline-first strategy
  const loadTodos = useCallback(async (
    filters?: TodoFilters,
    pagination?: PaginationOptions,
    options: OfflineFirstOptions = {}
  ) => {
    setLoading(true);
    setError(null);

    try {
      const result = await offlineFirstTodoService.getAllTodosOfflineFirst(
        filters,
        pagination,
        { useCache: true, ...options }
      );

      setTodos(result.todos);
      setTotal(result.total);
      setHasMore(result.hasMore);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load todos';
      setError(errorMessage);
      console.error('Failed to load todos:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create todo with optimistic updates
  const createTodo = useCallback(async (
    todoData: CreateTodoRequest,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true }
  ): Promise<Todo | null> => {
    setError(null);

    try {
      // Optimistic update - add temporary todo to UI immediately
      if (options.enableOptimisticUpdates) {
        const optimisticTodo: Todo = {
          id: `temp-${Date.now()}`,
          title: todoData.title,
          description: todoData.description || '',
          status: todoData.status || 'pending',
          priority: todoData.priority || 'medium',
          due_date: todoData.due_date || null,
          category_id: todoData.category_id || null,
          tags: todoData.tags || [],
          estimated_duration: todoData.estimated_duration || null,
          metadata: todoData.metadata || {},
          created_at: new Date(),
          updated_at: new Date(),
          completed_at: null,
          user_id: 'current-user', // This would come from auth context
          is_deleted: false
        };

        setTodos(prev => [optimisticTodo, ...prev]);
      }

      const newTodo = await offlineFirstTodoService.createTodoOfflineFirst(todoData, options);

      // Replace optimistic todo with real todo
      if (options.enableOptimisticUpdates) {
        setTodos(prev => prev.map(todo => 
          todo.id.startsWith('temp-') ? newTodo : todo
        ));
      } else {
        setTodos(prev => [newTodo, ...prev]);
      }

      setTotal(prev => prev + 1);
      return newTodo;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create todo';
      setError(errorMessage);

      // Revert optimistic update on error
      if (options.enableOptimisticUpdates) {
        setTodos(prev => prev.filter(todo => !todo.id.startsWith('temp-')));
      }

      console.error('Failed to create todo:', error);
      return null;
    }
  }, []);

  // Update todo with optimistic updates
  const updateTodo = useCallback(async (
    todoId: string,
    updates: UpdateTodoRequest,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true }
  ): Promise<Todo | null> => {
    setError(null);

    try {
      // Optimistic update
      if (options.enableOptimisticUpdates) {
        setTodos(prev => prev.map(todo => 
          todo.id === todoId 
            ? { ...todo, ...updates, updated_at: new Date() }
            : todo
        ));
      }

      const updatedTodo = await offlineFirstTodoService.updateTodoOfflineFirst(todoId, updates, options);

      // Replace with real updated todo
      setTodos(prev => prev.map(todo => 
        todo.id === todoId ? updatedTodo : todo
      ));

      return updatedTodo;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update todo';
      setError(errorMessage);

      // Revert optimistic update on error
      if (options.enableOptimisticUpdates) {
        // Reload todos to get the correct state
        loadTodos();
      }

      console.error('Failed to update todo:', error);
      return null;
    }
  }, [loadTodos]);

  // Update todo status with optimistic updates
  const updateTodoStatus = useCallback(async (
    todoId: string,
    status: TodoStatus,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true, priority: 'high' }
  ): Promise<Todo | null> => {
    setError(null);

    try {
      // Optimistic update
      if (options.enableOptimisticUpdates) {
        setTodos(prev => prev.map(todo => 
          todo.id === todoId 
            ? { 
                ...todo, 
                status, 
                updated_at: new Date(),
                completed_at: status === 'completed' ? new Date() : null
              }
            : todo
        ));
      }

      const updatedTodo = await offlineFirstTodoService.updateTodoStatusOfflineFirst(todoId, status, options);

      // Replace with real updated todo
      setTodos(prev => prev.map(todo => 
        todo.id === todoId ? updatedTodo : todo
      ));

      return updatedTodo;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update todo status';
      setError(errorMessage);

      // Revert optimistic update on error
      if (options.enableOptimisticUpdates) {
        loadTodos();
      }

      console.error('Failed to update todo status:', error);
      return null;
    }
  }, [loadTodos]);

  // Delete todo with optimistic updates
  const deleteTodo = useCallback(async (
    todoId: string,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true, priority: 'high' }
  ): Promise<boolean> => {
    setError(null);

    try {
      // Optimistic update - remove from UI immediately
      if (options.enableOptimisticUpdates) {
        setTodos(prev => prev.filter(todo => todo.id !== todoId));
        setTotal(prev => prev - 1);
      }

      await offlineFirstTodoService.deleteTodoOfflineFirst(todoId, options);
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete todo';
      setError(errorMessage);

      // Revert optimistic update on error
      if (options.enableOptimisticUpdates) {
        loadTodos();
      }

      console.error('Failed to delete todo:', error);
      return false;
    }
  }, [loadTodos]);

  // Force sync pending operations
  const forceSyncPending = useCallback(async (): Promise<boolean> => {
    try {
      await offlineFirstTodoService.forceSyncPendingOperations();
      await loadOfflineStatus(); // Refresh status after sync
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sync pending operations';
      setError(errorMessage);
      console.error('Failed to force sync:', error);
      return false;
    }
  }, [loadOfflineStatus]);

  // Clear local cache
  const clearCache = useCallback(async (): Promise<boolean> => {
    try {
      await offlineFirstTodoService.clearLocalCache();
      await loadOfflineStatus(); // Refresh status after clearing cache
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to clear cache';
      setError(errorMessage);
      console.error('Failed to clear cache:', error);
      return false;
    }
  }, [loadOfflineStatus]);

  // Initialize and load offline status
  useEffect(() => {
    loadOfflineStatus();
    
    // Set up periodic status updates
    const interval = setInterval(loadOfflineStatus, 30000); // Every 30 seconds
    
    return () => clearInterval(interval);
  }, [loadOfflineStatus]);

  return {
    // Data
    todos,
    total,
    hasMore,
    offlineStatus,
    
    // State
    loading,
    error,
    
    // Actions
    loadTodos,
    createTodo,
    updateTodo,
    updateTodoStatus,
    deleteTodo,
    forceSyncPending,
    clearCache,
    loadOfflineStatus,
    
    // Utilities
    clearError: () => setError(null),
    isOfflineCapable: offlineStatus?.isOfflineCapable || false,
    isOnline: offlineStatus?.isOnline || navigator.onLine,
    pendingOperations: offlineStatus?.pendingOperations || 0
  };
};
