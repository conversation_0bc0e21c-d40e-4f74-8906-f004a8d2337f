import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  PerformanceMonitorService, 
  PerformanceMetric, 
  PerformanceReport, 
  PerformanceAlert,
  performanceMonitor 
} from '@renderer/services/performance-monitor.service';

export interface UsePerformanceMonitorOptions {
  autoStart?: boolean;
  trackComponentRenders?: boolean;
  alertThreshold?: 'warning' | 'critical';
}

export interface UsePerformanceMonitorReturn {
  // Monitoring state
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
  
  // Metrics
  metrics: PerformanceMetric[];
  alerts: PerformanceAlert[];
  report: PerformanceReport | null;
  
  // Measurement functions
  measureAsync: <T>(name: string, operation: () => Promise<T>, metadata?: Record<string, any>) => Promise<T>;
  measureSync: <T>(name: string, operation: () => T, metadata?: Record<string, any>) => T;
  recordMetric: (metric: PerformanceMetric) => void;
  
  // Utilities
  clearMetrics: () => void;
  generateReport: (timeRange?: { start: Date; end: Date }) => PerformanceReport;
  
  // Component performance tracking
  trackRender: (componentName: string, metadata?: Record<string, any>) => void;
}

export const usePerformanceMonitor = (
  monitor: PerformanceMonitorService = performanceMonitor,
  options: UsePerformanceMonitorOptions = {}
): UsePerformanceMonitorReturn => {
  const {
    autoStart = false,
    trackComponentRenders = false,
    alertThreshold = 'warning',
  } = options;

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [report, setReport] = useState<PerformanceReport | null>(null);
  
  const renderStartTime = useRef<number>(0);
  const componentName = useRef<string>('');

  // Auto-start monitoring if requested
  useEffect(() => {
    if (autoStart) {
      startMonitoring();
    }

    return () => {
      if (isMonitoring) {
        stopMonitoring();
      }
    };
  }, [autoStart]);

  // Track component renders if enabled
  useEffect(() => {
    if (trackComponentRenders) {
      renderStartTime.current = performance.now();
    }
  });

  useEffect(() => {
    if (trackComponentRenders && renderStartTime.current > 0) {
      const duration = performance.now() - renderStartTime.current;
      if (duration > 0) {
        monitor.recordMetric({
          name: 'component_render',
          duration,
          timestamp: new Date(),
          status: duration > 16 ? (duration > 33 ? 'critical' : 'warning') : 'good',
          metadata: {
            component: componentName.current || 'Unknown',
          },
        });
      }
      renderStartTime.current = 0;
    }
  });

  // Set up event listeners
  useEffect(() => {
    const handleMetricRecorded = (metric: PerformanceMetric) => {
      setMetrics(prev => [...prev.slice(-99), metric]); // Keep last 100 metrics
      
      // Update report periodically
      if (metrics.length % 10 === 0) {
        const newReport = monitor.generateReport();
        setReport(newReport);
      }
    };

    const handlePerformanceAlert = (alert: PerformanceAlert) => {
      if (alertThreshold === 'warning' || alert.type === 'critical') {
        setAlerts(prev => [...prev.slice(-19), alert]); // Keep last 20 alerts
      }
    };

    const handleMetricsCleared = () => {
      setMetrics([]);
      setAlerts([]);
      setReport(null);
    };

    monitor.on('metricRecorded', handleMetricRecorded);
    monitor.on('performanceAlert', handlePerformanceAlert);
    monitor.on('metricsCleared', handleMetricsCleared);

    return () => {
      monitor.off('metricRecorded', handleMetricRecorded);
      monitor.off('performanceAlert', handlePerformanceAlert);
      monitor.off('metricsCleared', handleMetricsCleared);
    };
  }, [monitor, alertThreshold, metrics.length]);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    monitor.startMonitoring();
    setIsMonitoring(true);
  }, [monitor]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    monitor.stopMonitoring();
    setIsMonitoring(false);
  }, [monitor]);

  // Measure async operations
  const measureAsync = useCallback(async <T>(
    name: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> => {
    return monitor.measureAsync(name, operation, metadata);
  }, [monitor]);

  // Measure sync operations
  const measureSync = useCallback(<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T => {
    return monitor.measureSync(name, operation, metadata);
  }, [monitor]);

  // Record metric
  const recordMetric = useCallback((metric: PerformanceMetric) => {
    monitor.recordMetric(metric);
  }, [monitor]);

  // Clear metrics
  const clearMetrics = useCallback(() => {
    monitor.clearMetrics();
  }, [monitor]);

  // Generate report
  const generateReport = useCallback((timeRange?: { start: Date; end: Date }) => {
    const newReport = monitor.generateReport(timeRange);
    setReport(newReport);
    return newReport;
  }, [monitor]);

  // Track render performance
  const trackRender = useCallback((name: string, metadata?: Record<string, any>) => {
    componentName.current = name;
    if (trackComponentRenders) {
      renderStartTime.current = performance.now();
    }
  }, [trackComponentRenders]);

  return {
    // Monitoring state
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    
    // Metrics
    metrics,
    alerts,
    report,
    
    // Measurement functions
    measureAsync,
    measureSync,
    recordMetric,
    
    // Utilities
    clearMetrics,
    generateReport,
    
    // Component performance tracking
    trackRender,
  };
};

// Higher-order component for automatic render tracking
export function withPerformanceTracking<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const PerformanceTrackedComponent = (props: P) => {
    const { trackRender } = usePerformanceMonitor(performanceMonitor, {
      trackComponentRenders: true,
    });

    const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
    
    useEffect(() => {
      trackRender(name);
    });

    return <WrappedComponent {...props} />;
  };

  PerformanceTrackedComponent.displayName = `withPerformanceTracking(${componentName || WrappedComponent.displayName || WrappedComponent.name})`;

  return PerformanceTrackedComponent;
}

// Hook for measuring component lifecycle performance
export const useComponentPerformance = (componentName: string) => {
  const { measureSync, recordMetric } = usePerformanceMonitor();
  const mountTime = useRef<number>(0);
  const updateCount = useRef<number>(0);

  useEffect(() => {
    // Component mounted
    mountTime.current = performance.now();
    
    return () => {
      // Component unmounted
      const totalLifetime = performance.now() - mountTime.current;
      recordMetric({
        name: 'component_lifetime',
        duration: totalLifetime,
        timestamp: new Date(),
        status: 'good',
        metadata: {
          component: componentName,
          updateCount: updateCount.current,
        },
      });
    };
  }, [componentName, recordMetric]);

  useEffect(() => {
    // Component updated
    updateCount.current++;
    
    if (updateCount.current > 1) { // Skip initial render
      measureSync('component_update', () => {
        // This will be measured by the performance monitor
      }, {
        component: componentName,
        updateNumber: updateCount.current,
      });
    }
  });

  return {
    measureRender: useCallback((renderFunction: () => void) => {
      measureSync('component_render', renderFunction, {
        component: componentName,
      });
    }, [measureSync, componentName]),
  };
};
