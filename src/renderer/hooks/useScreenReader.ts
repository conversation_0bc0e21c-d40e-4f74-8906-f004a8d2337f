import { useCallback, useRef, useEffect } from 'react';

export type AnnouncementPriority = 'polite' | 'assertive';

export interface ScreenReaderAnnouncement {
  message: string;
  priority?: AnnouncementPriority;
  delay?: number;
  clearPrevious?: boolean;
}

/**
 * Hook for making screen reader announcements
 */
export const useScreenReader = () => {
  const politeRegionRef = useRef<HTMLDivElement | null>(null);
  const assertiveRegionRef = useRef<HTMLDivElement | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create live regions if they don't exist
  useEffect(() => {
    if (!politeRegionRef.current) {
      const politeRegion = document.createElement('div');
      politeRegion.setAttribute('aria-live', 'polite');
      politeRegion.setAttribute('aria-atomic', 'true');
      politeRegion.className = 'sr-only';
      politeRegion.id = 'polite-announcements';
      document.body.appendChild(politeRegion);
      politeRegionRef.current = politeRegion;
    }

    if (!assertiveRegionRef.current) {
      const assertiveRegion = document.createElement('div');
      assertiveRegion.setAttribute('aria-live', 'assertive');
      assertiveRegion.setAttribute('aria-atomic', 'true');
      assertiveRegion.className = 'sr-only';
      assertiveRegion.id = 'assertive-announcements';
      document.body.appendChild(assertiveRegion);
      assertiveRegionRef.current = assertiveRegion;
    }

    return () => {
      if (politeRegionRef.current) {
        document.body.removeChild(politeRegionRef.current);
        politeRegionRef.current = null;
      }
      if (assertiveRegionRef.current) {
        document.body.removeChild(assertiveRegionRef.current);
        assertiveRegionRef.current = null;
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const announce = useCallback((announcement: ScreenReaderAnnouncement | string) => {
    const config = typeof announcement === 'string' 
      ? { message: announcement, priority: 'polite' as AnnouncementPriority }
      : announcement;

    const {
      message,
      priority = 'polite',
      delay = 0,
      clearPrevious = false
    } = config;

    if (!message.trim()) return;

    const targetRegion = priority === 'assertive' 
      ? assertiveRegionRef.current 
      : politeRegionRef.current;

    if (!targetRegion) return;

    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const makeAnnouncement = () => {
      if (clearPrevious) {
        targetRegion.textContent = '';
        // Small delay to ensure screen readers notice the change
        setTimeout(() => {
          targetRegion.textContent = message;
        }, 10);
      } else {
        targetRegion.textContent = message;
      }
    };

    if (delay > 0) {
      timeoutRef.current = setTimeout(makeAnnouncement, delay);
    } else {
      makeAnnouncement();
    }
  }, []);

  const announcePolite = useCallback((message: string, delay?: number) => {
    announce({ message, priority: 'polite', delay });
  }, [announce]);

  const announceAssertive = useCallback((message: string, delay?: number) => {
    announce({ message, priority: 'assertive', delay });
  }, [announce]);

  const clear = useCallback((priority?: AnnouncementPriority) => {
    if (!priority || priority === 'polite') {
      if (politeRegionRef.current) {
        politeRegionRef.current.textContent = '';
      }
    }
    if (!priority || priority === 'assertive') {
      if (assertiveRegionRef.current) {
        assertiveRegionRef.current.textContent = '';
      }
    }
  }, []);

  return {
    announce,
    announcePolite,
    announceAssertive,
    clear
  };
};

/**
 * Hook for announcing todo-specific actions
 */
export const useTodoAnnouncements = () => {
  const { announce } = useScreenReader();

  const announceTodoCreated = useCallback((title: string) => {
    announce(`Todo "${title}" created`);
  }, [announce]);

  const announceTodoUpdated = useCallback((title: string) => {
    announce(`Todo "${title}" updated`);
  }, [announce]);

  const announceTodoDeleted = useCallback((title: string) => {
    announce(`Todo "${title}" deleted`);
  }, [announce]);

  const announceTodoCompleted = useCallback((title: string) => {
    announce(`Todo "${title}" marked as complete`);
  }, [announce]);

  const announceTodoIncomplete = useCallback((title: string) => {
    announce(`Todo "${title}" marked as incomplete`);
  }, [announce]);

  const announceTodoMoved = useCallback((title: string, newPosition: number, total: number) => {
    announce(`Todo "${title}" moved to position ${newPosition} of ${total}`);
  }, [announce]);

  const announceFilterApplied = useCallback((filterCount: number, totalCount: number) => {
    if (filterCount === 0) {
      announce('No todos match the current filters');
    } else if (filterCount === totalCount) {
      announce(`All ${totalCount} todos are shown`);
    } else {
      announce(`Showing ${filterCount} of ${totalCount} todos`);
    }
  }, [announce]);

  const announceSearchResults = useCallback((resultCount: number, query: string) => {
    if (resultCount === 0) {
      announce(`No todos found for "${query}"`);
    } else {
      announce(`${resultCount} todo${resultCount === 1 ? '' : 's'} found for "${query}"`);
    }
  }, [announce]);

  const announceBulkAction = useCallback((action: string, count: number) => {
    announce(`${action} applied to ${count} todo${count === 1 ? '' : 's'}`);
  }, [announce]);

  const announceError = useCallback((error: string) => {
    announce({ message: `Error: ${error}`, priority: 'assertive' });
  }, [announce]);

  const announceSuccess = useCallback((message: string) => {
    announce({ message, priority: 'polite' });
  }, [announce]);

  return {
    announceTodoCreated,
    announceTodoUpdated,
    announceTodoDeleted,
    announceTodoCompleted,
    announceTodoIncomplete,
    announceTodoMoved,
    announceFilterApplied,
    announceSearchResults,
    announceBulkAction,
    announceError,
    announceSuccess
  };
};

/**
 * Hook for managing focus announcements
 */
export const useFocusAnnouncements = () => {
  const { announce } = useScreenReader();

  const announceFocusChange = useCallback((elementDescription: string) => {
    announce({ message: `Focused: ${elementDescription}`, delay: 100 });
  }, [announce]);

  const announceModalOpen = useCallback((modalTitle: string) => {
    announce({ 
      message: `${modalTitle} dialog opened`, 
      priority: 'assertive',
      clearPrevious: true 
    });
  }, [announce]);

  const announceModalClose = useCallback((modalTitle: string) => {
    announce({ 
      message: `${modalTitle} dialog closed`, 
      priority: 'polite' 
    });
  }, [announce]);

  const announcePageChange = useCallback((pageTitle: string) => {
    announce({ 
      message: `Navigated to ${pageTitle}`, 
      priority: 'assertive',
      delay: 500,
      clearPrevious: true 
    });
  }, [announce]);

  const announceKeyboardShortcut = useCallback((shortcut: string, action: string) => {
    announce(`Keyboard shortcut: ${shortcut} for ${action}`);
  }, [announce]);

  return {
    announceFocusChange,
    announceModalOpen,
    announceModalClose,
    announcePageChange,
    announceKeyboardShortcut
  };
};

/**
 * Hook for managing loading state announcements
 */
export const useLoadingAnnouncements = () => {
  const { announce } = useScreenReader();

  const announceLoadingStart = useCallback((operation: string) => {
    announce({ message: `Loading ${operation}...`, priority: 'polite' });
  }, [announce]);

  const announceLoadingComplete = useCallback((operation: string) => {
    announce({ message: `${operation} loaded`, priority: 'polite' });
  }, [announce]);

  const announceLoadingError = useCallback((operation: string, error?: string) => {
    const message = error 
      ? `Failed to load ${operation}: ${error}`
      : `Failed to load ${operation}`;
    announce({ message, priority: 'assertive' });
  }, [announce]);

  const announceProgress = useCallback((operation: string, current: number, total: number) => {
    const percentage = Math.round((current / total) * 100);
    announce(`${operation}: ${percentage}% complete`);
  }, [announce]);

  return {
    announceLoadingStart,
    announceLoadingComplete,
    announceLoadingError,
    announceProgress
  };
};
