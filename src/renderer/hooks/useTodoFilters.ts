import { useState, useMemo, useCallback } from 'react';
import { Todo, TodoStatus, TodoPriority, Category } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';

export interface TodoFilters {
  search: string;
  status: TodoStatus[];
  priority: TodoPriority[];
  tags: string[];
  dueDate: 'all' | 'overdue' | 'today' | 'tomorrow' | 'this_week' | 'this_month' | 'no_due_date';
  category: string[];
  isCompleted?: boolean;
}

export interface FilterPreset {
  id: string;
  name: string;
  filters: Partial<TodoFilters>;
  icon?: string;
}

const DEFAULT_FILTERS: TodoFilters = {
  search: '',
  status: [],
  priority: [],
  tags: [],
  dueDate: 'all',
  category: [],
  isCompleted: undefined,
};

const FILTER_PRESETS: FilterPreset[] = [
  {
    id: 'all',
    name: 'All Tasks',
    filters: {},
    icon: '📋',
  },
  {
    id: 'active',
    name: 'Active',
    filters: { status: ['pending', 'in_progress'] },
    icon: '⚡',
  },
  {
    id: 'completed',
    name: 'Completed',
    filters: { status: ['completed'] },
    icon: '✅',
  },
  {
    id: 'overdue',
    name: 'Overdue',
    filters: { dueDate: 'overdue' },
    icon: '🚨',
  },
  {
    id: 'high_priority',
    name: 'High Priority',
    filters: { priority: ['high', 'very_high'] },
    icon: '🔥',
  },
  {
    id: 'today',
    name: 'Due Today',
    filters: { dueDate: 'today' },
    icon: '📅',
  },
];

export function useTodoFilters(todos: Todo[], categories: Category[] = []) {
  const [filters, setFilters] = useState<TodoFilters>(DEFAULT_FILTERS);
  const [activePreset, setActivePreset] = useState<string>('all');

  // Apply filters to todos
  const filteredTodos = useMemo(() => {
    return todos.filter((todo) => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch = 
          todo.title.toLowerCase().includes(searchLower) ||
          todo.description?.toLowerCase().includes(searchLower) ||
          todo.tags.some(tag => tag.toLowerCase().includes(searchLower));
        
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(todo.status)) {
        return false;
      }

      // Priority filter
      if (filters.priority.length > 0 && !filters.priority.includes(todo.priority)) {
        return false;
      }

      // Tags filter
      if (filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(filterTag =>
          todo.tags.some(todoTag => todoTag.toLowerCase().includes(filterTag.toLowerCase()))
        );
        if (!hasMatchingTag) return false;
      }

      // Category filter
      if (filters.category.length > 0) {
        if (!todo.category_id || !filters.category.includes(todo.category_id)) {
          return false;
        }
      }

      // Due date filter
      if (filters.dueDate !== 'all') {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const weekFromNow = new Date(today);
        weekFromNow.setDate(weekFromNow.getDate() + 7);
        const monthFromNow = new Date(today);
        monthFromNow.setMonth(monthFromNow.getMonth() + 1);

        switch (filters.dueDate) {
          case 'overdue':
            if (!todo.due_date || new Date(todo.due_date) >= today || todo.status === 'completed') {
              return false;
            }
            break;
          case 'today':
            if (!todo.due_date) return false;
            const dueDate = new Date(todo.due_date);
            if (dueDate < today || dueDate >= tomorrow) return false;
            break;
          case 'tomorrow':
            if (!todo.due_date) return false;
            const dueTomorrow = new Date(todo.due_date);
            if (dueTomorrow < tomorrow || dueTomorrow >= new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)) {
              return false;
            }
            break;
          case 'this_week':
            if (!todo.due_date || new Date(todo.due_date) > weekFromNow) return false;
            break;
          case 'this_month':
            if (!todo.due_date || new Date(todo.due_date) > monthFromNow) return false;
            break;
          case 'no_due_date':
            if (todo.due_date) return false;
            break;
        }
      }

      // Completion filter
      if (filters.isCompleted !== undefined) {
        const isCompleted = todo.status === 'completed';
        if (filters.isCompleted !== isCompleted) return false;
      }

      return true;
    });
  }, [todos, filters]);

  // Update individual filter
  const updateFilter = useCallback(<K extends keyof TodoFilters>(
    key: K,
    value: TodoFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setActivePreset(''); // Clear preset when manually filtering
  }, []);

  // Apply preset
  const applyPreset = useCallback((presetId: string) => {
    const preset = FILTER_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setFilters(prev => ({ ...DEFAULT_FILTERS, ...prev, ...preset.filters }));
      setActivePreset(presetId);
    }
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setActivePreset('all');
  }, []);

  // Get filter summary
  const filterSummary = useMemo(() => {
    const activeFilters: string[] = [];
    
    if (filters.search) activeFilters.push(`Search: "${filters.search}"`);
    if (filters.status.length > 0) activeFilters.push(`Status: ${filters.status.join(', ')}`);
    if (filters.priority.length > 0) activeFilters.push(`Priority: ${filters.priority.join(', ')}`);
    if (filters.tags.length > 0) activeFilters.push(`Tags: ${filters.tags.join(', ')}`);
    if (filters.dueDate !== 'all') activeFilters.push(`Due: ${filters.dueDate.replace('_', ' ')}`);
    if (filters.category.length > 0) activeFilters.push(`Category: ${filters.category.join(', ')}`);
    
    return {
      activeFilters,
      hasActiveFilters: activeFilters.length > 0,
      totalFiltered: filteredTodos.length,
      totalOriginal: todos.length,
    };
  }, [filters, filteredTodos.length, todos.length]);

  // Get available filter options from current todos
  const filterOptions = useMemo(() => {
    const allStatuses = [...new Set(todos.map(todo => todo.status))];
    const allPriorities = [...new Set(todos.map(todo => todo.priority))];
    const allTags = [...new Set(todos.flatMap(todo => todo.tags))];

    return {
      statuses: allStatuses,
      priorities: allPriorities,
      tags: allTags,
      categories: categories,
    };
  }, [todos, categories]);

  return {
    filters,
    filteredTodos,
    updateFilter,
    applyPreset,
    clearFilters,
    filterSummary,
    filterOptions,
    presets: FILTER_PRESETS,
    activePreset,
  };
}
