import { useCallback } from 'react';
import { arrayMove } from '@dnd-kit/sortable';
import { Todo } from '@shared/types';
import { useTodoStore } from '@renderer/stores/todoStore';
import { useTodoAnnouncements } from './useScreenReader';

export interface TodoReorderingOptions {
  optimisticUpdates?: boolean;
  announceChanges?: boolean;
}

/**
 * Hook for managing todo reordering with accessibility support
 */
export const useTodoReordering = (options: TodoReorderingOptions = {}) => {
  const {
    optimisticUpdates = true,
    announceChanges = true,
  } = options;

  const { todos, updateTodo } = useTodoStore();
  const { announceTodoMoved, announceError } = useTodoAnnouncements();

  /**
   * Reorder todos by moving one item to another position
   */
  const reorderTodos = useCallback(async (activeId: string, overId: string) => {
    const activeIndex = todos.findIndex(todo => todo.id === activeId);
    const overIndex = todos.findIndex(todo => todo.id === overId);

    if (activeIndex === -1 || overIndex === -1 || activeIndex === overIndex) {
      return;
    }

    const activeTodo = todos[activeIndex];
    const newTodos = arrayMove(todos, activeIndex, overIndex);

    // Optimistic update
    if (optimisticUpdates) {
      // Update positions in the store immediately
      newTodos.forEach((todo, index) => {
        if (todo.position !== index) {
          updateTodo(todo.id, { position: index });
        }
      });
    }

    try {
      // Update the position of the moved todo
      await updateTodo(activeId, { position: overIndex });

      // Update positions of affected todos
      const start = Math.min(activeIndex, overIndex);
      const end = Math.max(activeIndex, overIndex);
      
      for (let i = start; i <= end; i++) {
        const todo = newTodos[i];
        if (todo.id !== activeId && todo.position !== i) {
          await updateTodo(todo.id, { position: i });
        }
      }

      if (announceChanges) {
        announceTodoMoved(activeTodo.title, overIndex + 1, todos.length);
      }
    } catch (error) {
      console.error('Failed to reorder todos:', error);
      
      if (announceChanges) {
        announceError('Failed to reorder todo');
      }

      // Revert optimistic updates if they failed
      if (optimisticUpdates) {
        todos.forEach((todo, index) => {
          updateTodo(todo.id, { position: index });
        });
      }
    }
  }, [todos, updateTodo, optimisticUpdates, announceChanges, announceTodoMoved, announceError]);

  /**
   * Move a todo up by one position
   */
  const moveTodoUp = useCallback(async (todoId: string) => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    
    if (currentIndex <= 0) {
      return; // Already at the top
    }

    const targetTodo = todos[currentIndex - 1];
    await reorderTodos(todoId, targetTodo.id);
  }, [todos, reorderTodos]);

  /**
   * Move a todo down by one position
   */
  const moveTodoDown = useCallback(async (todoId: string) => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    
    if (currentIndex === -1 || currentIndex >= todos.length - 1) {
      return; // Already at the bottom or not found
    }

    const targetTodo = todos[currentIndex + 1];
    await reorderTodos(todoId, targetTodo.id);
  }, [todos, reorderTodos]);

  /**
   * Move a todo to the top of the list
   */
  const moveTodoToTop = useCallback(async (todoId: string) => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    
    if (currentIndex <= 0) {
      return; // Already at the top
    }

    const firstTodo = todos[0];
    await reorderTodos(todoId, firstTodo.id);
  }, [todos, reorderTodos]);

  /**
   * Move a todo to the bottom of the list
   */
  const moveTodoToBottom = useCallback(async (todoId: string) => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    
    if (currentIndex === -1 || currentIndex >= todos.length - 1) {
      return; // Already at the bottom or not found
    }

    const lastTodo = todos[todos.length - 1];
    await reorderTodos(todoId, lastTodo.id);
  }, [todos, reorderTodos]);

  /**
   * Move a todo to a specific position
   */
  const moveTodoToPosition = useCallback(async (todoId: string, targetPosition: number) => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    
    if (currentIndex === -1 || targetPosition < 0 || targetPosition >= todos.length) {
      return; // Invalid position
    }

    if (currentIndex === targetPosition) {
      return; // Already at target position
    }

    const targetTodo = todos[targetPosition];
    await reorderTodos(todoId, targetTodo.id);
  }, [todos, reorderTodos]);

  /**
   * Get the current position of a todo
   */
  const getTodoPosition = useCallback((todoId: string): number => {
    return todos.findIndex(todo => todo.id === todoId);
  }, [todos]);

  /**
   * Check if a todo can be moved up
   */
  const canMoveTodoUp = useCallback((todoId: string): boolean => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    return currentIndex > 0;
  }, [todos]);

  /**
   * Check if a todo can be moved down
   */
  const canMoveTodoDown = useCallback((todoId: string): boolean => {
    const currentIndex = todos.findIndex(todo => todo.id === todoId);
    return currentIndex !== -1 && currentIndex < todos.length - 1;
  }, [todos]);

  return {
    reorderTodos,
    moveTodoUp,
    moveTodoDown,
    moveTodoToTop,
    moveTodoToBottom,
    moveTodoToPosition,
    getTodoPosition,
    canMoveTodoUp,
    canMoveTodoDown,
  };
};
