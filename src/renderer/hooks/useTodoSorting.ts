import { useState, useMemo, useCallback } from 'react';
import { Todo, TodoStatus, TodoPriority } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';

// Helper function to parse ISO 8601 duration to minutes
function parseDurationToMinutes(duration: string): number {
  try {
    // Parse ISO 8601 duration format (e.g., "PT1H30M" = 90 minutes)
    const match = duration.match(/^PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0', 10);
    const minutes = parseInt(match[2] || '0', 10);
    const seconds = parseInt(match[3] || '0', 10);

    return hours * 60 + minutes + Math.round(seconds / 60);
  } catch {
    return 0;
  }
}

// Smart priority sorting: combines priority with due date urgency
function smartPrioritySort(a: Todo, b: Todo): number {
  const now = new Date();
  const oneDayMs = 24 * 60 * 60 * 1000;

  // Calculate urgency scores based on due dates
  const getUrgencyScore = (todo: Todo): number => {
    if (!todo.due_date) return 0; // No urgency if no due date

    const dueDate = new Date(todo.due_date);
    const timeDiff = dueDate.getTime() - now.getTime();
    const daysUntilDue = timeDiff / oneDayMs;

    if (daysUntilDue < 0) return 10; // Overdue = highest urgency
    if (daysUntilDue < 1) return 8;  // Due today
    if (daysUntilDue < 3) return 6;  // Due within 3 days
    if (daysUntilDue < 7) return 4;  // Due within a week
    if (daysUntilDue < 30) return 2; // Due within a month
    return 1; // Due later
  };

  const aPriorityScore = PRIORITY_ORDER[a.priority];
  const bPriorityScore = PRIORITY_ORDER[b.priority];
  const aUrgencyScore = getUrgencyScore(a);
  const bUrgencyScore = getUrgencyScore(b);

  // Combine priority and urgency (priority weighted more heavily)
  const aScore = aPriorityScore * 2 + aUrgencyScore;
  const bScore = bPriorityScore * 2 + bUrgencyScore;

  return bScore - aScore; // Higher scores first
}

// Overdue first sorting: prioritizes overdue tasks, then by due date
function overdueFirstSort(a: Todo, b: Todo): number {
  const now = new Date();

  const isOverdue = (todo: Todo): boolean => {
    return todo.due_date ? new Date(todo.due_date) < now : false;
  };

  const aOverdue = isOverdue(a);
  const bOverdue = isOverdue(b);

  // Overdue tasks come first
  if (aOverdue && !bOverdue) return -1;
  if (!aOverdue && bOverdue) return 1;

  // If both overdue or both not overdue, sort by due date
  if (!a.due_date && !b.due_date) return 0;
  if (!a.due_date) return 1;
  if (!b.due_date) return -1;

  return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
}

export type SortField = 
  | 'title' 
  | 'status' 
  | 'priority' 
  | 'due_date' 
  | 'created_at' 
  | 'updated_at'
  | 'position'
  | 'estimated_duration';

export type SortOrder = 'asc' | 'desc';

export interface SortConfig {
  field: SortField;
  order: SortOrder;
}

export interface SortPreset {
  id: string;
  name: string;
  config: SortConfig;
  icon?: string;
  description?: string;
}

const SORT_PRESETS: SortPreset[] = [
  {
    id: 'default',
    name: 'Default',
    config: { field: 'position', order: 'asc' },
    icon: '📋',
    description: 'Custom order',
  },
  {
    id: 'priority_high',
    name: 'Priority (High to Low)',
    config: { field: 'priority', order: 'desc' },
    icon: '🔥',
    description: 'Most important first',
  },
  {
    id: 'due_date_soon',
    name: 'Due Date (Soonest)',
    config: { field: 'due_date', order: 'asc' },
    icon: '⏰',
    description: 'Upcoming deadlines first',
  },
  {
    id: 'alphabetical',
    name: 'Alphabetical',
    config: { field: 'title', order: 'asc' },
    icon: '🔤',
    description: 'A to Z',
  },
  {
    id: 'recently_created',
    name: 'Recently Created',
    config: { field: 'created_at', order: 'desc' },
    icon: '🆕',
    description: 'Newest first',
  },
  {
    id: 'recently_updated',
    name: 'Recently Updated',
    config: { field: 'updated_at', order: 'desc' },
    icon: '📝',
    description: 'Last modified first',
  },
  {
    id: 'smart_priority',
    name: 'Smart Priority',
    config: { field: 'priority', order: 'desc' },
    icon: '🧠',
    description: 'Priority with urgency consideration',
  },
  {
    id: 'overdue_first',
    name: 'Overdue First',
    config: { field: 'due_date', order: 'asc' },
    icon: '🚨',
    description: 'Overdue and urgent tasks first',
  },
];

const PRIORITY_ORDER: Record<TodoPriority, number> = {
  very_high: 5,
  high: 4,
  medium: 3,
  low: 2,
  very_low: 1,
};

const STATUS_ORDER: Record<TodoStatus, number> = {
  in_progress: 5,
  pending: 4,
  completed: 3,
  archived: 2,
  cancelled: 1,
};

export function useTodoSorting(todos: Todo[]) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: 'position', order: 'asc' });
  const [activePreset, setActivePreset] = useState<string>('default');

  // Sort todos based on current configuration
  const sortedTodos = useMemo(() => {
    const sorted = [...todos].sort((a, b) => {
      let comparison = 0;

      // Handle smart sorting presets with special logic
      if (activePreset === 'smart_priority') {
        return smartPrioritySort(a, b);
      } else if (activePreset === 'overdue_first') {
        return overdueFirstSort(a, b);
      }

      switch (sortConfig.field) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;

        case 'status':
          comparison = STATUS_ORDER[a.status] - STATUS_ORDER[b.status];
          break;

        case 'priority':
          comparison = PRIORITY_ORDER[a.priority] - PRIORITY_ORDER[b.priority];
          break;

        case 'due_date':
          // Handle null due dates (put them at the end)
          if (!a.due_date && !b.due_date) comparison = 0;
          else if (!a.due_date) comparison = 1;
          else if (!b.due_date) comparison = -1;
          else comparison = new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
          break;

        case 'created_at':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;

        case 'updated_at':
          comparison = new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
          break;

        case 'position':
          comparison = a.position - b.position;
          break;

        case 'estimated_duration':
          // Handle null estimated durations and parse ISO 8601 duration
          const aDuration = a.estimated_duration ? parseDurationToMinutes(a.estimated_duration) : 0;
          const bDuration = b.estimated_duration ? parseDurationToMinutes(b.estimated_duration) : 0;
          comparison = aDuration - bDuration;
          break;

        default:
          comparison = 0;
      }

      // Apply sort order
      return sortConfig.order === 'desc' ? -comparison : comparison;
    });

    return sorted;
  }, [todos, sortConfig]);

  // Update sort configuration
  const updateSort = useCallback((field: SortField, order?: SortOrder) => {
    setSortConfig(prev => ({
      field,
      order: order || (prev.field === field && prev.order === 'asc' ? 'desc' : 'asc'),
    }));
    setActivePreset(''); // Clear preset when manually sorting
  }, []);

  // Apply sort preset
  const applyPreset = useCallback((presetId: string) => {
    const preset = SORT_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setSortConfig(preset.config);
      setActivePreset(presetId);
    }
  }, []);

  // Toggle sort order for current field
  const toggleSortOrder = useCallback(() => {
    setSortConfig(prev => ({
      ...prev,
      order: prev.order === 'asc' ? 'desc' : 'asc',
    }));
    setActivePreset(''); // Clear preset when manually changing order
  }, []);

  // Get sort indicator for a field
  const getSortIndicator = useCallback((field: SortField) => {
    if (sortConfig.field !== field) return null;
    return sortConfig.order === 'asc' ? '↑' : '↓';
  }, [sortConfig]);

  // Check if a field is currently being sorted
  const isSortedBy = useCallback((field: SortField) => {
    return sortConfig.field === field;
  }, [sortConfig.field]);

  // Get human-readable sort description
  const sortDescription = useMemo(() => {
    const preset = SORT_PRESETS.find(p => p.id === activePreset);
    if (preset) {
      return preset.description || preset.name;
    }

    const fieldName = sortConfig.field.replace('_', ' ');
    const orderName = sortConfig.order === 'asc' ? 'ascending' : 'descending';
    return `${fieldName} (${orderName})`;
  }, [sortConfig, activePreset]);

  // Multi-field sorting (for future enhancement)
  const addSecondarySort = useCallback((field: SortField, order: SortOrder = 'asc') => {
    // This could be implemented for more complex sorting scenarios
    console.log('Secondary sort not yet implemented:', field, order);
  }, []);

  return {
    sortConfig,
    sortedTodos,
    updateSort,
    applyPreset,
    toggleSortOrder,
    getSortIndicator,
    isSortedBy,
    sortDescription,
    presets: SORT_PRESETS,
    activePreset,
    addSecondarySort,
  };
}
