import { Todo, TodoStatus, TodoPriority } from '@shared/types';
import { performanceMonitor } from './performance-monitor.service';

export interface AnalyticsData {
  // Overview metrics
  totalTodos: number;
  completedTodos: number;
  pendingTodos: number;
  overdueTodos: number;
  completionRate: number;
  
  // Time-based metrics
  todosCreatedToday: number;
  todosCompletedToday: number;
  averageCompletionTime: number; // in hours
  
  // Productivity metrics
  currentStreak: number;
  longestStreak: number;
  productivityScore: number; // 0-100
  
  // Category and priority distribution
  categoryDistribution: { name: string; count: number; percentage: number }[];
  priorityDistribution: { priority: TodoPriority; count: number; percentage: number }[];
  
  // Trends
  completionTrend: { date: string; completed: number; created: number }[];
  productivityTrend: { date: string; score: number }[];
  
  // Time patterns
  hourlyActivity: { hour: number; count: number }[];
  weeklyActivity: { day: string; count: number }[];
  
  // Goals and achievements
  dailyGoal: number;
  dailyProgress: number;
  weeklyGoal: number;
  weeklyProgress: number;
  achievements: Achievement[];
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  type: 'streak' | 'completion' | 'productivity' | 'milestone';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface AnalyticsTimeRange {
  start: Date;
  end: Date;
  label: string;
}

export interface ProductivityInsight {
  type: 'positive' | 'neutral' | 'negative';
  title: string;
  description: string;
  suggestion?: string;
  metric: number;
  trend: 'up' | 'down' | 'stable';
}

export interface AnalyticsSettings {
  dailyGoal: number;
  weeklyGoal: number;
  enableStreakTracking: boolean;
  enableProductivityScore: boolean;
  enableAchievements: boolean;
  workingHours: {
    start: number; // 0-23
    end: number; // 0-23
  };
  workingDays: number[]; // 0-6 (Sunday-Saturday)
}

export class AnalyticsService {
  private settings: AnalyticsSettings = {
    dailyGoal: 5,
    weeklyGoal: 30,
    enableStreakTracking: true,
    enableProductivityScore: true,
    enableAchievements: true,
    workingHours: { start: 9, end: 17 },
    workingDays: [1, 2, 3, 4, 5], // Monday-Friday
  };

  private achievements: Achievement[] = [];
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.initializeAchievements();
  }

  // Main analytics generation
  public generateAnalytics(todos: Todo[], timeRange?: AnalyticsTimeRange): AnalyticsData {
    return performanceMonitor.measureSync('analytics_generation', () => {
      const filteredTodos = timeRange ? this.filterTodosByTimeRange(todos, timeRange) : todos;
      
      // Calculate basic metrics
      const totalTodos = filteredTodos.length;
      const completedTodos = filteredTodos.filter(todo => todo.status === 'completed').length;
      const pendingTodos = filteredTodos.filter(todo => 
        ['pending', 'in_progress'].includes(todo.status)
      ).length;
      const overdueTodos = this.getOverdueTodos(filteredTodos).length;
      const completionRate = totalTodos > 0 ? (completedTodos / totalTodos) * 100 : 0;

      // Time-based metrics
      const today = new Date();
      const todosCreatedToday = this.getTodosCreatedOnDate(filteredTodos, today).length;
      const todosCompletedToday = this.getTodosCompletedOnDate(filteredTodos, today).length;
      const averageCompletionTime = this.calculateAverageCompletionTime(filteredTodos);

      // Productivity metrics
      const currentStreak = this.calculateCurrentStreak(todos);
      const longestStreak = this.calculateLongestStreak(todos);
      const productivityScore = this.calculateProductivityScore(filteredTodos);

      // Distribution metrics
      const categoryDistribution = this.calculateCategoryDistribution(filteredTodos);
      const priorityDistribution = this.calculatePriorityDistribution(filteredTodos);

      // Trends
      const completionTrend = this.calculateCompletionTrend(todos, 30); // Last 30 days
      const productivityTrend = this.calculateProductivityTrend(todos, 30);

      // Time patterns
      const hourlyActivity = this.calculateHourlyActivity(filteredTodos);
      const weeklyActivity = this.calculateWeeklyActivity(filteredTodos);

      // Goals
      const dailyProgress = (todosCompletedToday / this.settings.dailyGoal) * 100;
      const weeklyProgress = this.calculateWeeklyProgress(todos);

      // Check for new achievements
      this.checkAchievements(todos);

      return {
        totalTodos,
        completedTodos,
        pendingTodos,
        overdueTodos,
        completionRate,
        todosCreatedToday,
        todosCompletedToday,
        averageCompletionTime,
        currentStreak,
        longestStreak,
        productivityScore,
        categoryDistribution,
        priorityDistribution,
        completionTrend,
        productivityTrend,
        hourlyActivity,
        weeklyActivity,
        dailyGoal: this.settings.dailyGoal,
        dailyProgress,
        weeklyGoal: this.settings.weeklyGoal,
        weeklyProgress,
        achievements: this.achievements,
      };
    }, { todoCount: todos.length });
  }

  // Productivity insights
  public generateInsights(todos: Todo[]): ProductivityInsight[] {
    const insights: ProductivityInsight[] = [];
    const analytics = this.generateAnalytics(todos);

    // Completion rate insight
    if (analytics.completionRate >= 80) {
      insights.push({
        type: 'positive',
        title: 'Excellent Completion Rate',
        description: `You're completing ${analytics.completionRate.toFixed(1)}% of your todos!`,
        metric: analytics.completionRate,
        trend: 'up',
      });
    } else if (analytics.completionRate < 50) {
      insights.push({
        type: 'negative',
        title: 'Low Completion Rate',
        description: `Only ${analytics.completionRate.toFixed(1)}% of todos are completed.`,
        suggestion: 'Try breaking down large tasks into smaller, manageable pieces.',
        metric: analytics.completionRate,
        trend: 'down',
      });
    }

    // Streak insight
    if (analytics.currentStreak >= 7) {
      insights.push({
        type: 'positive',
        title: 'Great Streak!',
        description: `You're on a ${analytics.currentStreak}-day completion streak!`,
        metric: analytics.currentStreak,
        trend: 'up',
      });
    } else if (analytics.currentStreak === 0) {
      insights.push({
        type: 'neutral',
        title: 'Start a New Streak',
        description: 'Complete at least one todo today to start building momentum.',
        suggestion: 'Pick a small, easy task to get started.',
        metric: 0,
        trend: 'stable',
      });
    }

    // Overdue insight
    if (analytics.overdueTodos > 0) {
      insights.push({
        type: 'negative',
        title: 'Overdue Tasks',
        description: `You have ${analytics.overdueTodos} overdue task${analytics.overdueTodos !== 1 ? 's' : ''}.`,
        suggestion: 'Consider rescheduling or breaking down overdue tasks.',
        metric: analytics.overdueTodos,
        trend: 'down',
      });
    }

    // Productivity score insight
    if (analytics.productivityScore >= 80) {
      insights.push({
        type: 'positive',
        title: 'High Productivity',
        description: `Your productivity score is ${analytics.productivityScore.toFixed(0)}/100!`,
        metric: analytics.productivityScore,
        trend: 'up',
      });
    }

    // Daily goal insight
    if (analytics.dailyProgress >= 100) {
      insights.push({
        type: 'positive',
        title: 'Daily Goal Achieved!',
        description: `You've completed ${analytics.todosCompletedToday} todos today.`,
        metric: analytics.dailyProgress,
        trend: 'up',
      });
    } else if (analytics.dailyProgress < 50) {
      const remaining = this.settings.dailyGoal - analytics.todosCompletedToday;
      insights.push({
        type: 'neutral',
        title: 'Daily Goal Progress',
        description: `${remaining} more todo${remaining !== 1 ? 's' : ''} to reach your daily goal.`,
        metric: analytics.dailyProgress,
        trend: 'stable',
      });
    }

    return insights;
  }

  // Time range helpers
  public getTimeRanges(): AnalyticsTimeRange[] {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekStart = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const yearStart = new Date(now.getFullYear(), 0, 1);

    return [
      {
        start: today,
        end: new Date(today.getTime() + 24 * 60 * 60 * 1000),
        label: 'Today',
      },
      {
        start: yesterday,
        end: today,
        label: 'Yesterday',
      },
      {
        start: weekStart,
        end: now,
        label: 'This Week',
      },
      {
        start: monthStart,
        end: now,
        label: 'This Month',
      },
      {
        start: yearStart,
        end: now,
        label: 'This Year',
      },
      {
        start: new Date(0),
        end: now,
        label: 'All Time',
      },
    ];
  }

  // Settings management
  public updateSettings(newSettings: Partial<AnalyticsSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.emit('settingsUpdated', this.settings);
  }

  public getSettings(): AnalyticsSettings {
    return { ...this.settings };
  }

  // Private calculation methods
  private filterTodosByTimeRange(todos: Todo[], timeRange: AnalyticsTimeRange): Todo[] {
    return todos.filter(todo => {
      const createdAt = new Date(todo.created_at);
      return createdAt >= timeRange.start && createdAt <= timeRange.end;
    });
  }

  private getOverdueTodos(todos: Todo[]): Todo[] {
    const now = new Date();
    return todos.filter(todo => 
      todo.due_date && 
      new Date(todo.due_date) < now && 
      todo.status !== 'completed'
    );
  }

  private getTodosCreatedOnDate(todos: Todo[], date: Date): Todo[] {
    const dateStr = date.toDateString();
    return todos.filter(todo => 
      new Date(todo.created_at).toDateString() === dateStr
    );
  }

  private getTodosCompletedOnDate(todos: Todo[], date: Date): Todo[] {
    const dateStr = date.toDateString();
    return todos.filter(todo => 
      todo.status === 'completed' && 
      todo.completed_at &&
      new Date(todo.completed_at).toDateString() === dateStr
    );
  }

  private calculateAverageCompletionTime(todos: Todo[]): number {
    const completedTodos = todos.filter(todo => 
      todo.status === 'completed' && todo.completed_at
    );

    if (completedTodos.length === 0) return 0;

    const totalTime = completedTodos.reduce((sum, todo) => {
      const created = new Date(todo.created_at).getTime();
      const completed = new Date(todo.completed_at!).getTime();
      return sum + (completed - created);
    }, 0);

    return totalTime / completedTodos.length / (1000 * 60 * 60); // Convert to hours
  }

  private calculateCurrentStreak(todos: Todo[]): number {
    if (!this.settings.enableStreakTracking) return 0;

    const today = new Date();
    let streak = 0;
    let currentDate = new Date(today);

    while (true) {
      const completedOnDate = this.getTodosCompletedOnDate(todos, currentDate);
      if (completedOnDate.length === 0) {
        break;
      }
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    }

    return streak;
  }

  private calculateLongestStreak(todos: Todo[]): number {
    if (!this.settings.enableStreakTracking) return 0;

    const completionDates = todos
      .filter(todo => todo.status === 'completed' && todo.completed_at)
      .map(todo => new Date(todo.completed_at!).toDateString())
      .filter((date, index, array) => array.indexOf(date) === index)
      .sort();

    let longestStreak = 0;
    let currentStreak = 0;
    let previousDate: Date | null = null;

    completionDates.forEach(dateStr => {
      const date = new Date(dateStr);
      
      if (previousDate) {
        const dayDiff = (date.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24);
        if (dayDiff === 1) {
          currentStreak++;
        } else {
          longestStreak = Math.max(longestStreak, currentStreak);
          currentStreak = 1;
        }
      } else {
        currentStreak = 1;
      }
      
      previousDate = date;
    });

    return Math.max(longestStreak, currentStreak);
  }

  private calculateProductivityScore(todos: Todo[]): number {
    if (!this.settings.enableProductivityScore) return 0;

    const totalTodos = todos.length;
    if (totalTodos === 0) return 0;

    const completedTodos = todos.filter(todo => todo.status === 'completed').length;
    const overdueTodos = this.getOverdueTodos(todos).length;
    const highPriorityCompleted = todos.filter(todo => 
      todo.status === 'completed' && ['high', 'very_high'].includes(todo.priority)
    ).length;

    // Base score from completion rate
    let score = (completedTodos / totalTodos) * 60;

    // Bonus for high priority completions
    score += (highPriorityCompleted / Math.max(1, totalTodos)) * 20;

    // Penalty for overdue todos
    score -= (overdueTodos / Math.max(1, totalTodos)) * 30;

    // Bonus for current streak
    const streak = this.calculateCurrentStreak(todos);
    score += Math.min(streak * 2, 20);

    return Math.max(0, Math.min(100, score));
  }

  private calculateCategoryDistribution(todos: Todo[]): { name: string; count: number; percentage: number }[] {
    const categoryMap = new Map<string, number>();
    
    todos.forEach(todo => {
      const category = todo.category_id || 'Uncategorized';
      categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
    });

    const total = todos.length;
    return Array.from(categoryMap.entries()).map(([name, count]) => ({
      name,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
    }));
  }

  private calculatePriorityDistribution(todos: Todo[]): { priority: TodoPriority; count: number; percentage: number }[] {
    const priorityMap = new Map<TodoPriority, number>();
    
    todos.forEach(todo => {
      priorityMap.set(todo.priority, (priorityMap.get(todo.priority) || 0) + 1);
    });

    const total = todos.length;
    const priorities: TodoPriority[] = ['very_low', 'low', 'medium', 'high', 'very_high'];
    
    return priorities.map(priority => ({
      priority,
      count: priorityMap.get(priority) || 0,
      percentage: total > 0 ? ((priorityMap.get(priority) || 0) / total) * 100 : 0,
    }));
  }

  private calculateCompletionTrend(todos: Todo[], days: number): { date: string; completed: number; created: number }[] {
    const trend: { date: string; completed: number; created: number }[] = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const created = this.getTodosCreatedOnDate(todos, date).length;
      const completed = this.getTodosCompletedOnDate(todos, date).length;
      
      trend.push({ date: dateStr, created, completed });
    }

    return trend;
  }

  private calculateProductivityTrend(todos: Todo[], days: number): { date: string; score: number }[] {
    const trend: { date: string; score: number }[] = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      // Calculate todos up to this date for cumulative score
      const todosUpToDate = todos.filter(todo => 
        new Date(todo.created_at) <= date
      );
      
      const score = this.calculateProductivityScore(todosUpToDate);
      trend.push({ date: dateStr, score });
    }

    return trend;
  }

  private calculateHourlyActivity(todos: Todo[]): { hour: number; count: number }[] {
    const hourlyMap = new Map<number, number>();
    
    todos.forEach(todo => {
      const hour = new Date(todo.created_at).getHours();
      hourlyMap.set(hour, (hourlyMap.get(hour) || 0) + 1);
    });

    const activity: { hour: number; count: number }[] = [];
    for (let hour = 0; hour < 24; hour++) {
      activity.push({ hour, count: hourlyMap.get(hour) || 0 });
    }

    return activity;
  }

  private calculateWeeklyActivity(todos: Todo[]): { day: string; count: number }[] {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const weeklyMap = new Map<number, number>();
    
    todos.forEach(todo => {
      const day = new Date(todo.created_at).getDay();
      weeklyMap.set(day, (weeklyMap.get(day) || 0) + 1);
    });

    return dayNames.map((day, index) => ({
      day,
      count: weeklyMap.get(index) || 0,
    }));
  }

  private calculateWeeklyProgress(todos: Todo[]): number {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weeklyCompleted = todos.filter(todo => 
      todo.status === 'completed' && 
      todo.completed_at &&
      new Date(todo.completed_at) >= weekStart
    ).length;

    return (weeklyCompleted / this.settings.weeklyGoal) * 100;
  }

  private initializeAchievements(): void {
    // This would typically load from storage
    this.achievements = [];
  }

  private checkAchievements(todos: Todo[]): void {
    if (!this.settings.enableAchievements) return;

    const analytics = this.generateAnalytics(todos);
    const newAchievements: Achievement[] = [];

    // First completion achievement
    if (analytics.completedTodos >= 1 && !this.hasAchievement('first_completion')) {
      newAchievements.push({
        id: 'first_completion',
        title: 'Getting Started',
        description: 'Complete your first todo',
        icon: '🎯',
        unlockedAt: new Date(),
        type: 'completion',
        rarity: 'common',
      });
    }

    // Streak achievements
    if (analytics.currentStreak >= 7 && !this.hasAchievement('week_streak')) {
      newAchievements.push({
        id: 'week_streak',
        title: 'Week Warrior',
        description: 'Complete todos for 7 days in a row',
        icon: '🔥',
        unlockedAt: new Date(),
        type: 'streak',
        rarity: 'rare',
      });
    }

    // Add new achievements
    newAchievements.forEach(achievement => {
      this.achievements.push(achievement);
      this.emit('achievementUnlocked', achievement);
    });
  }

  private hasAchievement(id: string): boolean {
    return this.achievements.some(achievement => achievement.id === id);
  }

  // Event system
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(callback => callback(data));
  }
}

}

// Singleton instance
export const analyticsService = new AnalyticsService();
