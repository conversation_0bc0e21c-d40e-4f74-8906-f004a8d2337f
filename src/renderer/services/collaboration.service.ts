import { Todo } from '@shared/types';
import { performanceMonitor } from './performance-monitor.service';

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen: Date;
  currentTodo?: string; // ID of todo being edited
  cursor?: {
    todoId: string;
    field: string;
    position: number;
  };
}

export interface PresenceUpdate {
  userId: string;
  action: 'join' | 'leave' | 'edit' | 'idle';
  todoId?: string;
  field?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface LiveUpdate {
  id: string;
  type: 'todo_created' | 'todo_updated' | 'todo_deleted' | 'todo_moved';
  userId: string;
  todoId: string;
  changes: Partial<Todo>;
  timestamp: Date;
  conflictResolution?: 'auto' | 'manual';
}

export interface CollaborationEvent {
  type: 'presence_update' | 'live_update' | 'conflict_detected' | 'user_typing';
  data: PresenceUpdate | LiveUpdate | ConflictData | TypingData;
  timestamp: Date;
}

export interface ConflictData {
  todoId: string;
  conflictingUsers: string[];
  changes: Array<{
    userId: string;
    field: string;
    oldValue: any;
    newValue: any;
    timestamp: Date;
  }>;
  resolution?: 'latest_wins' | 'merge' | 'manual';
}

export interface TypingData {
  userId: string;
  todoId: string;
  field: string;
  isTyping: boolean;
}

export interface CollaborationSettings {
  enablePresenceIndicators: boolean;
  enableLiveUpdates: boolean;
  enableTypingIndicators: boolean;
  conflictResolutionStrategy: 'latest_wins' | 'merge' | 'manual';
  updateThrottleMs: number;
  presenceTimeoutMs: number;
}

export class CollaborationService {
  private users: Map<string, CollaborationUser> = new Map();
  private currentUser: CollaborationUser | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private updateQueue: LiveUpdate[] = [];
  private isConnected = false;
  private settings: CollaborationSettings = {
    enablePresenceIndicators: true,
    enableLiveUpdates: true,
    enableTypingIndicators: true,
    conflictResolutionStrategy: 'latest_wins',
    updateThrottleMs: 500,
    presenceTimeoutMs: 30000,
  };

  private presenceInterval?: NodeJS.Timeout;
  private updateThrottle?: NodeJS.Timeout;

  constructor() {
    this.initializeCollaboration();
  }

  private initializeCollaboration(): void {
    // Initialize current user
    this.currentUser = {
      id: crypto.randomUUID(),
      name: 'Current User',
      email: '<EMAIL>',
      color: this.generateUserColor(),
      isOnline: true,
      lastSeen: new Date(),
    };

    // Start presence heartbeat
    this.startPresenceHeartbeat();
  }

  // User Management
  public setCurrentUser(user: Partial<CollaborationUser>): void {
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...user };
      this.broadcastPresenceUpdate('join');
    }
  }

  public getCurrentUser(): CollaborationUser | null {
    return this.currentUser;
  }

  public getUsers(): CollaborationUser[] {
    return Array.from(this.users.values());
  }

  public getOnlineUsers(): CollaborationUser[] {
    return this.getUsers().filter(user => user.isOnline);
  }

  public getUsersEditingTodo(todoId: string): CollaborationUser[] {
    return this.getUsers().filter(user => user.currentTodo === todoId);
  }

  // Presence Management
  public updatePresence(todoId?: string, field?: string): void {
    if (!this.currentUser) return;

    this.currentUser.currentTodo = todoId;
    this.currentUser.lastSeen = new Date();

    if (todoId && field) {
      this.currentUser.cursor = {
        todoId,
        field,
        position: 0, // Would be actual cursor position in real implementation
      };
    } else {
      this.currentUser.cursor = undefined;
    }

    this.broadcastPresenceUpdate('edit', todoId, field);
  }

  public setUserIdle(): void {
    if (!this.currentUser) return;

    this.currentUser.currentTodo = undefined;
    this.currentUser.cursor = undefined;
    this.broadcastPresenceUpdate('idle');
  }

  private broadcastPresenceUpdate(action: PresenceUpdate['action'], todoId?: string, field?: string): void {
    if (!this.currentUser) return;

    const update: PresenceUpdate = {
      userId: this.currentUser.id,
      action,
      todoId,
      field,
      timestamp: new Date(),
    };

    this.emit('presenceUpdate', update);
    
    // In a real implementation, this would be sent to other users via WebSocket/WebRTC
    this.simulateRemotePresenceUpdate(update);
  }

  private simulateRemotePresenceUpdate(update: PresenceUpdate): void {
    // Simulate receiving presence updates from other users
    setTimeout(() => {
      this.handleRemotePresenceUpdate(update);
    }, 100);
  }

  private handleRemotePresenceUpdate(update: PresenceUpdate): void {
    const user = this.users.get(update.userId);
    if (!user) return;

    user.lastSeen = update.timestamp;
    
    switch (update.action) {
      case 'join':
        user.isOnline = true;
        break;
      case 'leave':
        user.isOnline = false;
        user.currentTodo = undefined;
        user.cursor = undefined;
        break;
      case 'edit':
        user.currentTodo = update.todoId;
        if (update.todoId && update.field) {
          user.cursor = {
            todoId: update.todoId,
            field: update.field,
            position: 0,
          };
        }
        break;
      case 'idle':
        user.currentTodo = undefined;
        user.cursor = undefined;
        break;
    }

    this.emit('userPresenceChanged', { user, update });
  }

  // Live Updates
  public broadcastTodoUpdate(todoId: string, changes: Partial<Todo>, type: LiveUpdate['type'] = 'todo_updated'): void {
    return performanceMonitor.measureSync('collaboration_broadcast', () => {
      if (!this.currentUser) return;

      const update: LiveUpdate = {
        id: crypto.randomUUID(),
        type,
        userId: this.currentUser.id,
        todoId,
        changes,
        timestamp: new Date(),
      };

      this.queueUpdate(update);
    }, { todoId, changeCount: Object.keys(changes).length });
  }

  private queueUpdate(update: LiveUpdate): void {
    this.updateQueue.push(update);
    
    if (this.updateThrottle) {
      clearTimeout(this.updateThrottle);
    }

    this.updateThrottle = setTimeout(() => {
      this.flushUpdateQueue();
    }, this.settings.updateThrottleMs);
  }

  private flushUpdateQueue(): void {
    if (this.updateQueue.length === 0) return;

    const updates = [...this.updateQueue];
    this.updateQueue = [];

    // Batch updates for efficiency
    const batchedUpdates = this.batchUpdates(updates);
    
    batchedUpdates.forEach(update => {
      this.emit('liveUpdate', update);
      
      // In a real implementation, send to other users
      this.simulateRemoteLiveUpdate(update);
    });
  }

  private batchUpdates(updates: LiveUpdate[]): LiveUpdate[] {
    // Group updates by todoId and merge changes
    const grouped = new Map<string, LiveUpdate>();
    
    updates.forEach(update => {
      const existing = grouped.get(update.todoId);
      if (existing && existing.type === update.type && existing.userId === update.userId) {
        // Merge changes
        existing.changes = { ...existing.changes, ...update.changes };
        existing.timestamp = update.timestamp;
      } else {
        grouped.set(update.todoId, update);
      }
    });

    return Array.from(grouped.values());
  }

  private simulateRemoteLiveUpdate(update: LiveUpdate): void {
    // Simulate receiving updates from other users
    setTimeout(() => {
      this.handleRemoteLiveUpdate(update);
    }, Math.random() * 200 + 100);
  }

  private handleRemoteLiveUpdate(update: LiveUpdate): void {
    // Check for conflicts
    const conflicts = this.detectConflicts(update);
    
    if (conflicts.length > 0) {
      this.handleConflicts(update, conflicts);
    } else {
      this.emit('todoUpdateReceived', update);
    }
  }

  // Conflict Detection and Resolution
  private detectConflicts(update: LiveUpdate): ConflictData[] {
    const conflicts: ConflictData[] = [];
    
    // Check if multiple users are editing the same todo
    const editingUsers = this.getUsersEditingTodo(update.todoId);
    if (editingUsers.length > 1) {
      const conflictingUsers = editingUsers
        .filter(user => user.id !== update.userId)
        .map(user => user.id);

      if (conflictingUsers.length > 0) {
        conflicts.push({
          todoId: update.todoId,
          conflictingUsers,
          changes: [{
            userId: update.userId,
            field: 'multiple', // Would be specific fields in real implementation
            oldValue: null,
            newValue: update.changes,
            timestamp: update.timestamp,
          }],
          resolution: this.settings.conflictResolutionStrategy,
        });
      }
    }

    return conflicts;
  }

  private handleConflicts(update: LiveUpdate, conflicts: ConflictData[]): void {
    conflicts.forEach(conflict => {
      switch (conflict.resolution) {
        case 'latest_wins':
          // Apply the latest update
          this.emit('todoUpdateReceived', update);
          break;
        case 'merge':
          // Attempt to merge changes
          const mergedUpdate = this.mergeConflictingChanges(update, conflict);
          this.emit('todoUpdateReceived', mergedUpdate);
          break;
        case 'manual':
          // Require manual resolution
          this.emit('conflictDetected', conflict);
          break;
      }
    });
  }

  private mergeConflictingChanges(update: LiveUpdate, conflict: ConflictData): LiveUpdate {
    // Simple merge strategy - in practice, this would be more sophisticated
    return {
      ...update,
      conflictResolution: 'auto',
    };
  }

  // Typing Indicators
  public startTyping(todoId: string, field: string): void {
    if (!this.currentUser) return;

    const typingData: TypingData = {
      userId: this.currentUser.id,
      todoId,
      field,
      isTyping: true,
    };

    this.emit('userTyping', typingData);
    this.broadcastTypingIndicator(typingData);
  }

  public stopTyping(todoId: string, field: string): void {
    if (!this.currentUser) return;

    const typingData: TypingData = {
      userId: this.currentUser.id,
      todoId,
      field,
      isTyping: false,
    };

    this.emit('userTyping', typingData);
    this.broadcastTypingIndicator(typingData);
  }

  private broadcastTypingIndicator(data: TypingData): void {
    // In a real implementation, broadcast to other users
    setTimeout(() => {
      this.emit('remoteUserTyping', data);
    }, 50);
  }

  // Connection Management
  public connect(): Promise<void> {
    return performanceMonitor.measureAsync('collaboration_connect', async () => {
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 500));
      
      this.isConnected = true;
      this.emit('connected');
      
      // Add some mock users for demonstration
      this.addMockUsers();
    });
  }

  public disconnect(): void {
    this.isConnected = false;
    this.stopPresenceHeartbeat();
    this.emit('disconnected');
  }

  public isConnectedToCollaboration(): boolean {
    return this.isConnected;
  }

  // Settings
  public updateSettings(settings: Partial<CollaborationSettings>): void {
    this.settings = { ...this.settings, ...settings };
    this.emit('settingsUpdated', this.settings);
  }

  public getSettings(): CollaborationSettings {
    return { ...this.settings };
  }

  // Private Helper Methods
  private startPresenceHeartbeat(): void {
    this.presenceInterval = setInterval(() => {
      if (this.currentUser) {
        this.currentUser.lastSeen = new Date();
        this.broadcastPresenceUpdate('edit');
      }
      
      // Clean up offline users
      this.cleanupOfflineUsers();
    }, this.settings.presenceTimeoutMs / 3);
  }

  private stopPresenceHeartbeat(): void {
    if (this.presenceInterval) {
      clearInterval(this.presenceInterval);
      this.presenceInterval = undefined;
    }
  }

  private cleanupOfflineUsers(): void {
    const now = new Date();
    const timeout = this.settings.presenceTimeoutMs;
    
    this.users.forEach((user, userId) => {
      const timeSinceLastSeen = now.getTime() - user.lastSeen.getTime();
      if (timeSinceLastSeen > timeout && user.isOnline) {
        user.isOnline = false;
        user.currentTodo = undefined;
        user.cursor = undefined;
        this.emit('userPresenceChanged', { user, update: { action: 'leave' } });
      }
    });
  }

  private generateUserColor(): string {
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
      '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  private addMockUsers(): void {
    const mockUsers: CollaborationUser[] = [
      {
        id: 'user-1',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        color: '#3B82F6',
        isOnline: true,
        lastSeen: new Date(),
      },
      {
        id: 'user-2',
        name: 'Bob Smith',
        email: '<EMAIL>',
        color: '#EF4444',
        isOnline: true,
        lastSeen: new Date(),
      },
      {
        id: 'user-3',
        name: 'Carol Davis',
        email: '<EMAIL>',
        color: '#10B981',
        isOnline: false,
        lastSeen: new Date(Date.now() - 60000), // 1 minute ago
      },
    ];

    mockUsers.forEach(user => {
      this.users.set(user.id, user);
    });

    this.emit('usersUpdated', this.getUsers());
  }

  // Event System
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(callback => callback(data));
  }

  public destroy(): void {
    this.disconnect();
    this.users.clear();
    this.eventListeners.clear();
    this.updateQueue = [];
    
    if (this.updateThrottle) {
      clearTimeout(this.updateThrottle);
    }
  }
}

// Singleton instance
export const collaborationService = new CollaborationService();
