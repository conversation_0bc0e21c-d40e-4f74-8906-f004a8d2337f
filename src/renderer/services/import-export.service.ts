import { Todo, Category } from '@shared/types';
import { performanceMonitor } from './performance-monitor.service';

export type ExportFormat = 'json' | 'csv' | 'markdown' | 'pdf' | 'ics' | 'xml';
export type ImportFormat = 'json' | 'csv' | 'xml';

export interface ExportOptions {
  format: ExportFormat;
  includeCompleted?: boolean;
  includeArchived?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  categories?: string[];
  tags?: string[];
  customFields?: string[];
}

export interface ImportOptions {
  format: ImportFormat;
  validateData?: boolean;
  mergeStrategy?: 'replace' | 'merge' | 'skip';
  categoryMapping?: Record<string, string>;
  tagMapping?: Record<string, string>;
}

export interface ExportResult {
  success: boolean;
  data?: string | Blob;
  filename: string;
  format: ExportFormat;
  recordCount: number;
  errors?: string[];
}

export interface ImportResult {
  success: boolean;
  importedCount: number;
  skippedCount: number;
  errorCount: number;
  todos: Todo[];
  errors?: string[];
  warnings?: string[];
}

export interface ValidationError {
  row?: number;
  field: string;
  value: any;
  message: string;
}

export class ImportExportService {
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    // Initialize service
  }

  // Export Methods
  public async exportTodos(todos: Todo[], options: ExportOptions): Promise<ExportResult> {
    return performanceMonitor.measureAsync('export_todos', async () => {
      try {
        // Filter todos based on options
        const filteredTodos = this.filterTodosForExport(todos, options);
        
        let data: string | Blob;
        let filename: string;

        switch (options.format) {
          case 'json':
            data = this.exportToJSON(filteredTodos, options);
            filename = `todos_${this.getDateString()}.json`;
            break;
          case 'csv':
            data = this.exportToCSV(filteredTodos, options);
            filename = `todos_${this.getDateString()}.csv`;
            break;
          case 'markdown':
            data = this.exportToMarkdown(filteredTodos, options);
            filename = `todos_${this.getDateString()}.md`;
            break;
          case 'pdf':
            data = await this.exportToPDF(filteredTodos, options);
            filename = `todos_${this.getDateString()}.pdf`;
            break;
          case 'ics':
            data = this.exportToICS(filteredTodos, options);
            filename = `todos_${this.getDateString()}.ics`;
            break;
          case 'xml':
            data = this.exportToXML(filteredTodos, options);
            filename = `todos_${this.getDateString()}.xml`;
            break;
          default:
            throw new Error(`Unsupported export format: ${options.format}`);
        }

        const result: ExportResult = {
          success: true,
          data,
          filename,
          format: options.format,
          recordCount: filteredTodos.length,
        };

        this.emit('exportCompleted', result);
        return result;

      } catch (error) {
        const result: ExportResult = {
          success: false,
          filename: '',
          format: options.format,
          recordCount: 0,
          errors: [error instanceof Error ? error.message : 'Export failed'],
        };

        this.emit('exportFailed', result);
        return result;
      }
    }, { format: options.format, todoCount: todos.length });
  }

  // Import Methods
  public async importTodos(data: string | File, options: ImportOptions): Promise<ImportResult> {
    return performanceMonitor.measureAsync('import_todos', async () => {
      try {
        let content: string;
        
        if (data instanceof File) {
          content = await this.readFileContent(data);
        } else {
          content = data;
        }

        let parsedTodos: Partial<Todo>[];

        switch (options.format) {
          case 'json':
            parsedTodos = this.importFromJSON(content);
            break;
          case 'csv':
            parsedTodos = this.importFromCSV(content);
            break;
          case 'xml':
            parsedTodos = this.importFromXML(content);
            break;
          default:
            throw new Error(`Unsupported import format: ${options.format}`);
        }

        // Validate data if requested
        const validationErrors: ValidationError[] = [];
        if (options.validateData) {
          parsedTodos.forEach((todo, index) => {
            const errors = this.validateTodo(todo, index);
            validationErrors.push(...errors);
          });
        }

        // Transform and clean data
        const transformedTodos = this.transformImportedTodos(parsedTodos, options);
        
        // Apply merge strategy
        const finalTodos = this.applyMergeStrategy(transformedTodos, options);

        const result: ImportResult = {
          success: validationErrors.length === 0,
          importedCount: finalTodos.length,
          skippedCount: parsedTodos.length - finalTodos.length,
          errorCount: validationErrors.length,
          todos: finalTodos,
          errors: validationErrors.map(e => `Row ${e.row}: ${e.field} - ${e.message}`),
        };

        this.emit('importCompleted', result);
        return result;

      } catch (error) {
        const result: ImportResult = {
          success: false,
          importedCount: 0,
          skippedCount: 0,
          errorCount: 1,
          todos: [],
          errors: [error instanceof Error ? error.message : 'Import failed'],
        };

        this.emit('importFailed', result);
        return result;
      }
    }, { format: options.format });
  }

  // Export Format Implementations
  private exportToJSON(todos: Todo[], options: ExportOptions): string {
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        format: 'json',
        version: '1.0',
        recordCount: todos.length,
        options,
      },
      todos: todos.map(todo => ({
        ...todo,
        // Include custom fields if specified
        ...(options.customFields ? this.extractCustomFields(todo, options.customFields) : {}),
      })),
    };

    return JSON.stringify(exportData, null, 2);
  }

  private exportToCSV(todos: Todo[], options: ExportOptions): string {
    if (todos.length === 0) return '';

    // Define CSV headers
    const headers = [
      'id',
      'title',
      'description',
      'status',
      'priority',
      'due_date',
      'created_at',
      'updated_at',
      'completed_at',
      'category_id',
      'tags',
      'estimated_duration',
      ...(options.customFields || []),
    ];

    // Create CSV content
    const csvRows = [
      headers.join(','), // Header row
      ...todos.map(todo => 
        headers.map(header => {
          let value = todo[header as keyof Todo];
          
          // Handle special cases
          if (header === 'tags' && Array.isArray(value)) {
            value = (value as string[]).join(';');
          } else if (value === null || value === undefined) {
            value = '';
          } else if (typeof value === 'string' && value.includes(',')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }
          
          return value;
        }).join(',')
      ),
    ];

    return csvRows.join('\n');
  }

  private exportToMarkdown(todos: Todo[], options: ExportOptions): string {
    const lines: string[] = [];
    
    // Add header
    lines.push('# Todo Export');
    lines.push('');
    lines.push(`Exported on: ${new Date().toLocaleDateString()}`);
    lines.push(`Total todos: ${todos.length}`);
    lines.push('');

    // Group by status
    const groupedTodos = this.groupTodosByStatus(todos);
    
    Object.entries(groupedTodos).forEach(([status, statusTodos]) => {
      if (statusTodos.length === 0) return;
      
      lines.push(`## ${status.charAt(0).toUpperCase() + status.slice(1)} (${statusTodos.length})`);
      lines.push('');
      
      statusTodos.forEach(todo => {
        const checkbox = todo.status === 'completed' ? '[x]' : '[ ]';
        lines.push(`${checkbox} **${todo.title}**`);
        
        if (todo.description) {
          lines.push(`   ${todo.description}`);
        }
        
        const metadata: string[] = [];
        if (todo.priority !== 'medium') metadata.push(`Priority: ${todo.priority}`);
        if (todo.due_date) metadata.push(`Due: ${new Date(todo.due_date).toLocaleDateString()}`);
        if (todo.tags && todo.tags.length > 0) metadata.push(`Tags: ${todo.tags.join(', ')}`);
        
        if (metadata.length > 0) {
          lines.push(`   *${metadata.join(' | ')}*`);
        }
        
        lines.push('');
      });
    });

    return lines.join('\n');
  }

  private async exportToPDF(todos: Todo[], options: ExportOptions): Promise<Blob> {
    // For PDF export, we'll create a simple HTML structure and convert it
    // In a real implementation, you might use libraries like jsPDF or Puppeteer
    const htmlContent = this.generateHTMLForPDF(todos, options);
    
    // This is a simplified implementation
    // In practice, you'd use a proper PDF generation library
    const blob = new Blob([htmlContent], { type: 'text/html' });
    return blob;
  }

  private exportToICS(todos: Todo[], options: ExportOptions): string {
    const lines: string[] = [];
    
    // ICS header
    lines.push('BEGIN:VCALENDAR');
    lines.push('VERSION:2.0');
    lines.push('PRODID:-//Todo App//Todo Export//EN');
    lines.push('CALSCALE:GREGORIAN');
    
    // Add todos as events
    todos.forEach(todo => {
      if (todo.due_date) {
        lines.push('BEGIN:VEVENT');
        lines.push(`UID:${todo.id}@todoapp.com`);
        lines.push(`DTSTAMP:${this.formatDateForICS(new Date())}`);
        lines.push(`DTSTART:${this.formatDateForICS(new Date(todo.due_date))}`);
        lines.push(`SUMMARY:${this.escapeICSText(todo.title)}`);
        
        if (todo.description) {
          lines.push(`DESCRIPTION:${this.escapeICSText(todo.description)}`);
        }
        
        lines.push(`STATUS:${todo.status === 'completed' ? 'COMPLETED' : 'NEEDS-ACTION'}`);
        lines.push(`PRIORITY:${this.mapPriorityToICS(todo.priority)}`);
        lines.push('END:VEVENT');
      }
    });
    
    lines.push('END:VCALENDAR');
    return lines.join('\r\n');
  }

  private exportToXML(todos: Todo[], options: ExportOptions): string {
    const lines: string[] = [];
    
    lines.push('<?xml version="1.0" encoding="UTF-8"?>');
    lines.push('<todos>');
    lines.push(`  <metadata>`);
    lines.push(`    <exportDate>${new Date().toISOString()}</exportDate>`);
    lines.push(`    <recordCount>${todos.length}</recordCount>`);
    lines.push(`  </metadata>`);
    
    todos.forEach(todo => {
      lines.push('  <todo>');
      lines.push(`    <id>${this.escapeXML(todo.id)}</id>`);
      lines.push(`    <title>${this.escapeXML(todo.title)}</title>`);
      if (todo.description) lines.push(`    <description>${this.escapeXML(todo.description)}</description>`);
      lines.push(`    <status>${this.escapeXML(todo.status)}</status>`);
      lines.push(`    <priority>${this.escapeXML(todo.priority)}</priority>`);
      if (todo.due_date) lines.push(`    <dueDate>${this.escapeXML(todo.due_date)}</dueDate>`);
      lines.push(`    <createdAt>${this.escapeXML(todo.created_at)}</createdAt>`);
      lines.push(`    <updatedAt>${this.escapeXML(todo.updated_at)}</updatedAt>`);
      
      if (todo.tags && todo.tags.length > 0) {
        lines.push('    <tags>');
        todo.tags.forEach(tag => {
          lines.push(`      <tag>${this.escapeXML(tag)}</tag>`);
        });
        lines.push('    </tags>');
      }
      
      lines.push('  </todo>');
    });
    
    lines.push('</todos>');
    return lines.join('\n');
  }

  // Import Format Implementations
  private importFromJSON(content: string): Partial<Todo>[] {
    const data = JSON.parse(content);
    
    // Handle different JSON structures
    if (Array.isArray(data)) {
      return data;
    } else if (data.todos && Array.isArray(data.todos)) {
      return data.todos;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else {
      throw new Error('Invalid JSON structure: expected array of todos');
    }
  }

  private importFromCSV(content: string): Partial<Todo>[] {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) throw new Error('CSV must have at least a header and one data row');
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const todos: Partial<Todo>[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      const todo: Partial<Todo> = {};
      
      headers.forEach((header, index) => {
        if (values[index] !== undefined && values[index] !== '') {
          let value: any = values[index];
          
          // Handle special cases
          if (header === 'tags') {
            value = value.split(';').map((tag: string) => tag.trim()).filter(Boolean);
          } else if (header.includes('date') || header.includes('_at')) {
            value = new Date(value).toISOString();
          }
          
          (todo as any)[header] = value;
        }
      });
      
      todos.push(todo);
    }
    
    return todos;
  }

  private importFromXML(content: string): Partial<Todo>[] {
    // Simple XML parsing - in production, use a proper XML parser
    const todos: Partial<Todo>[] = [];
    const todoMatches = content.match(/<todo>([\s\S]*?)<\/todo>/g);
    
    if (!todoMatches) {
      throw new Error('No todos found in XML');
    }
    
    todoMatches.forEach(todoXML => {
      const todo: Partial<Todo> = {};
      
      // Extract fields
      const fieldMatches = todoXML.match(/<(\w+)>([\s\S]*?)<\/\1>/g);
      fieldMatches?.forEach(fieldMatch => {
        const match = fieldMatch.match(/<(\w+)>([\s\S]*?)<\/\1>/);
        if (match) {
          const [, fieldName, fieldValue] = match;
          (todo as any)[fieldName] = fieldValue.trim();
        }
      });
      
      // Handle tags specially
      const tagsMatch = todoXML.match(/<tags>([\s\S]*?)<\/tags>/);
      if (tagsMatch) {
        const tagMatches = tagsMatch[1].match(/<tag>([\s\S]*?)<\/tag>/g);
        if (tagMatches) {
          todo.tags = tagMatches.map(tagMatch => {
            const match = tagMatch.match(/<tag>([\s\S]*?)<\/tag>/);
            return match ? match[1].trim() : '';
          }).filter(Boolean);
        }
      }
      
      todos.push(todo);
    });
    
    return todos;
  }

  // Helper Methods
  private filterTodosForExport(todos: Todo[], options: ExportOptions): Todo[] {
    return todos.filter(todo => {
      // Filter by completion status
      if (!options.includeCompleted && todo.status === 'completed') return false;
      if (!options.includeArchived && todo.status === 'archived') return false;
      
      // Filter by date range
      if (options.dateRange) {
        const todoDate = new Date(todo.created_at);
        if (todoDate < options.dateRange.start || todoDate > options.dateRange.end) return false;
      }
      
      // Filter by categories
      if (options.categories && options.categories.length > 0) {
        if (!todo.category_id || !options.categories.includes(todo.category_id)) return false;
      }
      
      // Filter by tags
      if (options.tags && options.tags.length > 0) {
        if (!todo.tags || !options.tags.some(tag => todo.tags!.includes(tag))) return false;
      }
      
      return true;
    });
  }

  private validateTodo(todo: Partial<Todo>, index: number): ValidationError[] {
    const errors: ValidationError[] = [];
    
    if (!todo.title || typeof todo.title !== 'string' || todo.title.trim() === '') {
      errors.push({
        row: index + 1,
        field: 'title',
        value: todo.title,
        message: 'Title is required and must be a non-empty string',
      });
    }
    
    if (todo.status && !['pending', 'in_progress', 'completed', 'archived', 'cancelled'].includes(todo.status)) {
      errors.push({
        row: index + 1,
        field: 'status',
        value: todo.status,
        message: 'Invalid status value',
      });
    }
    
    if (todo.priority && !['very_low', 'low', 'medium', 'high', 'very_high'].includes(todo.priority)) {
      errors.push({
        row: index + 1,
        field: 'priority',
        value: todo.priority,
        message: 'Invalid priority value',
      });
    }
    
    return errors;
  }

  private transformImportedTodos(todos: Partial<Todo>[], options: ImportOptions): Todo[] {
    return todos.map(todo => {
      // Apply category mapping
      if (todo.category_id && options.categoryMapping) {
        todo.category_id = options.categoryMapping[todo.category_id] || todo.category_id;
      }
      
      // Apply tag mapping
      if (todo.tags && options.tagMapping) {
        todo.tags = todo.tags.map(tag => options.tagMapping![tag] || tag);
      }
      
      // Set defaults
      const transformedTodo: Todo = {
        id: todo.id || crypto.randomUUID(),
        title: todo.title || 'Untitled',
        description: todo.description || null,
        status: todo.status || 'pending',
        priority: todo.priority || 'medium',
        due_date: todo.due_date || null,
        created_at: todo.created_at || new Date().toISOString(),
        updated_at: todo.updated_at || new Date().toISOString(),
        completed_at: todo.completed_at || null,
        category_id: todo.category_id || null,
        tags: todo.tags || null,
        estimated_duration: todo.estimated_duration || null,
        metadata: todo.metadata || null,
      };
      
      return transformedTodo;
    });
  }

  private applyMergeStrategy(todos: Todo[], options: ImportOptions): Todo[] {
    // For now, just return all todos
    // In a real implementation, you'd check for existing todos and apply the merge strategy
    return todos;
  }

  // Utility Methods
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private getDateString(): string {
    return new Date().toISOString().split('T')[0];
  }

  private groupTodosByStatus(todos: Todo[]): Record<string, Todo[]> {
    return todos.reduce((groups, todo) => {
      const status = todo.status;
      if (!groups[status]) groups[status] = [];
      groups[status].push(todo);
      return groups;
    }, {} as Record<string, Todo[]>);
  }

  private extractCustomFields(todo: Todo, fields: string[]): Record<string, any> {
    const customData: Record<string, any> = {};
    fields.forEach(field => {
      if (todo.metadata && todo.metadata[field] !== undefined) {
        customData[field] = todo.metadata[field];
      }
    });
    return customData;
  }

  private generateHTMLForPDF(todos: Todo[], options: ExportOptions): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Todo Export</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .todo { margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; }
          .title { font-weight: bold; font-size: 16px; }
          .description { margin: 5px 0; color: #666; }
          .metadata { font-size: 12px; color: #999; }
        </style>
      </head>
      <body>
        <h1>Todo Export</h1>
        <p>Exported on: ${new Date().toLocaleDateString()}</p>
        <p>Total todos: ${todos.length}</p>
        ${todos.map(todo => `
          <div class="todo">
            <div class="title">${todo.title}</div>
            ${todo.description ? `<div class="description">${todo.description}</div>` : ''}
            <div class="metadata">
              Status: ${todo.status} | Priority: ${todo.priority}
              ${todo.due_date ? ` | Due: ${new Date(todo.due_date).toLocaleDateString()}` : ''}
            </div>
          </div>
        `).join('')}
      </body>
      </html>
    `;
  }

  private formatDateForICS(date: Date): string {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  }

  private escapeICSText(text: string): string {
    return text.replace(/[,;\\]/g, '\\$&').replace(/\n/g, '\\n');
  }

  private mapPriorityToICS(priority: string): string {
    const priorityMap: Record<string, string> = {
      very_low: '9',
      low: '7',
      medium: '5',
      high: '3',
      very_high: '1',
    };
    return priorityMap[priority] || '5';
  }

  private escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  // Event system
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(callback => callback(data));
  }
}

}

// Singleton instance
export const importExportService = new ImportExportService();
