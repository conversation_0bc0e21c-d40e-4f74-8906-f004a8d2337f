import { Todo, TodoStatus, TodoPriority } from '@shared/types';

declare global {
  interface Window {
    electronAPI: {
      todos: {
        getAll: (sessionId: string, filters?: any, pagination?: any) => Promise<any>;
        getAllOfflineFirst: (sessionId: string, filters?: any, pagination?: any, options?: any) => Promise<any>;
        create: (sessionId: string, todoData: any) => Promise<any>;
        createOfflineFirst: (sessionId: string, todoData: any, options?: any) => Promise<any>;
        update: (sessionId: string, todoId: string, updates: any) => Promise<any>;
        updateOfflineFirst: (sessionId: string, todoId: string, updates: any, options?: any) => Promise<any>;
        delete: (sessionId: string, todoId: string) => Promise<any>;
        deleteOfflineFirst: (sessionId: string, todoId: string, options?: any) => Promise<any>;
        updateStatus: (sessionId: string, todoId: string, status: string) => Promise<any>;
        updateStatusOfflineFirst: (sessionId: string, todoId: string, status: string, options?: any) => Promise<any>;
        getOfflineStatus: () => Promise<any>;
        forceSyncPending: () => Promise<any>;
        clearCache: () => Promise<any>;
      };
    };
  }
}

export interface CreateTodoRequest {
  title: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  priority?: TodoPriority;
  status?: TodoStatus;
  due_date?: Date;
  category_id?: string;
  tags?: string[];
  estimated_duration?: string;
  metadata?: Record<string, any>;
}

export interface TodoFilters {
  status?: TodoStatus;
  priority?: TodoPriority;
  category_id?: string;
  due_date_from?: Date;
  due_date_to?: Date;
  tags?: string[];
  search?: string;
  is_completed?: boolean;
  is_overdue?: boolean;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
}

export interface OfflineFirstOptions {
  useCache?: boolean;
  forceRefresh?: boolean;
  enableOptimisticUpdates?: boolean;
  priority?: 'high' | 'medium' | 'low';
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface OfflineStatus {
  isOfflineCapable: boolean;
  isOnline: boolean;
  pendingOperations: number;
  lastSyncAt: Date | null;
  localDatabaseStatus: any;
  cacheSize: number;
  networkQuality: string;
}

/**
 * Enhanced Todo Service with Offline-First capabilities
 * 
 * This service provides:
 * - Offline-first data operations
 * - Optimistic updates for immediate UI feedback
 * - Automatic background synchronization
 * - Graceful degradation when offline
 * - Local caching for improved performance
 */
export class OfflineFirstTodoService {
  private sessionId: string | null = null;

  constructor() {
    // For now, we'll use a mock session ID
    // In a real implementation, this would come from authentication
    this.sessionId = 'mock-session-id';
  }

  setSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  private async makeRequest<T>(
    operation: string,
    ...args: any[]
  ): Promise<T> {
    if (!this.sessionId) {
      throw new Error('No active session');
    }

    if (!window.electronAPI?.todos) {
      throw new Error('Electron API not available');
    }

    const response: APIResponse<T> = await (window.electronAPI.todos as any)[operation](
      this.sessionId,
      ...args
    );

    if (!response.success) {
      throw new Error(response.error || 'Unknown error');
    }

    return response.data as T;
  }

  /**
   * Get all todos with offline-first strategy
   */
  async getAllTodosOfflineFirst(
    filters?: TodoFilters,
    pagination?: PaginationOptions,
    options: OfflineFirstOptions = {}
  ): Promise<{ todos: Todo[]; total: number; hasMore: boolean }> {
    try {
      return await this.makeRequest('getAllOfflineFirst', filters, pagination, options);
    } catch (error) {
      console.warn('Offline-first getAllTodos failed, falling back to regular method:', error);
      // Fallback to regular method
      const result = await this.makeRequest('getAll', filters, pagination);
      return {
        todos: result.data || [],
        total: result.total || 0,
        hasMore: result.hasMore || false
      };
    }
  }

  /**
   * Create todo with offline-first strategy and optimistic updates
   */
  async createTodoOfflineFirst(
    todoData: CreateTodoRequest,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true }
  ): Promise<Todo> {
    try {
      return await this.makeRequest('createOfflineFirst', todoData, options);
    } catch (error) {
      console.warn('Offline-first createTodo failed, falling back to regular method:', error);
      return await this.makeRequest('create', todoData);
    }
  }

  /**
   * Update todo with offline-first strategy and optimistic updates
   */
  async updateTodoOfflineFirst(
    todoId: string,
    updates: UpdateTodoRequest,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true }
  ): Promise<Todo> {
    try {
      return await this.makeRequest('updateOfflineFirst', todoId, updates, options);
    } catch (error) {
      console.warn('Offline-first updateTodo failed, falling back to regular method:', error);
      return await this.makeRequest('update', todoId, updates);
    }
  }

  /**
   * Update todo status with offline-first strategy
   */
  async updateTodoStatusOfflineFirst(
    todoId: string,
    status: TodoStatus,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true, priority: 'high' }
  ): Promise<Todo> {
    try {
      return await this.makeRequest('updateStatusOfflineFirst', todoId, status, options);
    } catch (error) {
      console.warn('Offline-first updateTodoStatus failed, falling back to regular method:', error);
      return await this.makeRequest('updateStatus', todoId, status);
    }
  }

  /**
   * Delete todo with offline-first strategy
   */
  async deleteTodoOfflineFirst(
    todoId: string,
    options: OfflineFirstOptions = { enableOptimisticUpdates: true, priority: 'high' }
  ): Promise<Todo> {
    try {
      return await this.makeRequest('deleteOfflineFirst', todoId, options);
    } catch (error) {
      console.warn('Offline-first deleteTodo failed, falling back to regular method:', error);
      return await this.makeRequest('delete', todoId);
    }
  }

  /**
   * Get offline status and capabilities
   */
  async getOfflineStatus(): Promise<OfflineStatus> {
    try {
      return await this.makeRequest('getOfflineStatus');
    } catch (error) {
      console.error('Failed to get offline status:', error);
      return {
        isOfflineCapable: false,
        isOnline: navigator.onLine,
        pendingOperations: 0,
        lastSyncAt: null,
        localDatabaseStatus: null,
        cacheSize: 0,
        networkQuality: 'unknown'
      };
    }
  }

  /**
   * Force sync all pending operations
   */
  async forceSyncPendingOperations(): Promise<void> {
    try {
      await this.makeRequest('forceSyncPending');
    } catch (error) {
      console.error('Failed to force sync pending operations:', error);
      throw error;
    }
  }

  /**
   * Clear local cache
   */
  async clearLocalCache(): Promise<void> {
    try {
      await this.makeRequest('clearCache');
    } catch (error) {
      console.error('Failed to clear local cache:', error);
      throw error;
    }
  }

  /**
   * Check if the app can work offline
   */
  async isOfflineCapable(): Promise<boolean> {
    try {
      const status = await this.getOfflineStatus();
      return status.isOfflineCapable;
    } catch (error) {
      return false;
    }
  }

  /**
   * Legacy methods for backward compatibility
   */
  async getAllTodos(
    filters?: TodoFilters,
    pagination?: PaginationOptions
  ): Promise<{ data: Todo[]; total: number }> {
    return this.makeRequest('getAll', filters, pagination);
  }

  async createTodo(todoData: CreateTodoRequest): Promise<Todo> {
    return this.makeRequest('create', todoData);
  }

  async updateTodo(todoId: string, updates: UpdateTodoRequest): Promise<Todo> {
    return this.makeRequest('update', todoId, updates);
  }

  async deleteTodo(todoId: string): Promise<Todo> {
    return this.makeRequest('delete', todoId);
  }

  async updateTodoStatus(todoId: string, status: TodoStatus): Promise<Todo> {
    return this.makeRequest('updateStatus', todoId, status);
  }
}

// Export singleton instance
export const offlineFirstTodoService = new OfflineFirstTodoService();
