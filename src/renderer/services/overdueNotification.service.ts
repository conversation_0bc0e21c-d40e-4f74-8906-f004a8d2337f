import { Todo } from '@shared/types';
import { 
  getOverdueInfo, 
  shouldNotifyOverdue, 
  generateOverdueNotificationMessage,
  getNotificationPriority 
} from '@renderer/utils/overdueUtils';
import { useToast } from '@renderer/components/ui/Toast';

interface NotificationHistory {
  todoId: string;
  lastNotificationTime: Date;
  notificationCount: number;
}

export class OverdueNotificationService {
  private static instance: OverdueNotificationService;
  private intervalId: NodeJS.Timeout | null = null;
  private notificationHistory: Map<string, NotificationHistory> = new Map();
  private isRunning = false;
  private checkIntervalMs = 15 * 60 * 1000; // 15 minutes
  private getTodos: (() => Promise<Todo[]>) | null = null;
  private addNotification: ((notification: any) => void) | null = null;

  private constructor() {
    this.loadNotificationHistory();
  }

  public static getInstance(): OverdueNotificationService {
    if (!OverdueNotificationService.instance) {
      OverdueNotificationService.instance = new OverdueNotificationService();
    }
    return OverdueNotificationService.instance;
  }

  /**
   * Initialize the service with required dependencies
   */
  public initialize(
    getTodos: () => Promise<Todo[]>,
    addNotification: (notification: any) => void
  ): void {
    this.getTodos = getTodos;
    this.addNotification = addNotification;
  }

  /**
   * Start the periodic overdue checking
   */
  public start(): void {
    if (this.isRunning || !this.getTodos || !this.addNotification) {
      return;
    }

    this.isRunning = true;
    
    // Run initial check
    this.checkOverdueTodos();
    
    // Set up periodic checking
    this.intervalId = setInterval(() => {
      this.checkOverdueTodos();
    }, this.checkIntervalMs);

    console.log('OverdueNotificationService started');
  }

  /**
   * Stop the periodic checking
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('OverdueNotificationService stopped');
  }

  /**
   * Check for overdue todos and send notifications
   */
  private async checkOverdueTodos(): Promise<void> {
    if (!this.getTodos || !this.addNotification) {
      return;
    }

    try {
      const todos = await this.getTodos();
      const overdueTodos = todos.filter(todo => {
        const overdueInfo = getOverdueInfo(todo);
        return overdueInfo.isOverdue;
      });

      for (const todo of overdueTodos) {
        await this.processOverdueTodo(todo);
      }

      // Clean up notification history for completed/deleted todos
      this.cleanupNotificationHistory(todos);
    } catch (error) {
      console.error('Error checking overdue todos:', error);
    }
  }

  /**
   * Process a single overdue todo for notifications
   */
  private async processOverdueTodo(todo: Todo): Promise<void> {
    const overdueInfo = getOverdueInfo(todo);
    const history = this.notificationHistory.get(todo.id);
    const lastNotificationTime = history?.lastNotificationTime;

    if (shouldNotifyOverdue(overdueInfo, lastNotificationTime)) {
      await this.sendOverdueNotification(todo, overdueInfo);
      this.updateNotificationHistory(todo.id);
    }
  }

  /**
   * Send notification for overdue todo
   */
  private async sendOverdueNotification(todo: Todo, overdueInfo: any): Promise<void> {
    if (!this.addNotification) return;

    const { title, message } = generateOverdueNotificationMessage(todo, overdueInfo);
    const priority = getNotificationPriority(overdueInfo.severity);

    // Determine notification type and duration based on severity
    let notificationType: 'warning' | 'error' = 'warning';
    let duration = 5000; // 5 seconds

    if (overdueInfo.severity === 'severe') {
      notificationType = 'error';
      duration = 10000; // 10 seconds for severe overdue
    } else if (overdueInfo.severity === 'moderate') {
      duration = 7000; // 7 seconds for moderate overdue
    }

    try {
      this.addNotification({
        type: notificationType,
        title,
        message,
        duration,
        action: {
          label: 'View Task',
          onClick: () => {
            // This could trigger navigation to the specific todo
            // For now, we'll just log it
            console.log('Navigate to todo:', todo.id);
          }
        }
      });

      console.log(`Sent ${overdueInfo.severity} overdue notification for todo: ${todo.title}`);
    } catch (error) {
      console.error('Error sending overdue notification:', error);
    }
  }

  /**
   * Update notification history for a todo
   */
  private updateNotificationHistory(todoId: string): void {
    const existing = this.notificationHistory.get(todoId);
    const history: NotificationHistory = {
      todoId,
      lastNotificationTime: new Date(),
      notificationCount: (existing?.notificationCount || 0) + 1
    };

    this.notificationHistory.set(todoId, history);
    this.saveNotificationHistory();
  }

  /**
   * Clean up notification history for completed or deleted todos
   */
  private cleanupNotificationHistory(currentTodos: Todo[]): void {
    const currentTodoIds = new Set(currentTodos.map(t => t.id));
    const completedTodoIds = new Set(
      currentTodos
        .filter(t => t.status === 'completed' || t.status === 'cancelled')
        .map(t => t.id)
    );

    // Remove history for deleted todos
    for (const [todoId] of this.notificationHistory) {
      if (!currentTodoIds.has(todoId) || completedTodoIds.has(todoId)) {
        this.notificationHistory.delete(todoId);
      }
    }

    this.saveNotificationHistory();
  }

  /**
   * Manually trigger overdue check (useful for immediate checks after todo changes)
   */
  public async triggerCheck(): Promise<void> {
    if (!this.isRunning) return;
    await this.checkOverdueTodos();
  }

  /**
   * Get notification history for debugging
   */
  public getNotificationHistory(): NotificationHistory[] {
    return Array.from(this.notificationHistory.values());
  }

  /**
   * Clear all notification history
   */
  public clearNotificationHistory(): void {
    this.notificationHistory.clear();
    this.saveNotificationHistory();
  }

  /**
   * Save notification history to localStorage
   */
  private saveNotificationHistory(): void {
    try {
      const historyArray = Array.from(this.notificationHistory.entries());
      localStorage.setItem('overdueNotificationHistory', JSON.stringify(historyArray));
    } catch (error) {
      console.error('Error saving notification history:', error);
    }
  }

  /**
   * Load notification history from localStorage
   */
  private loadNotificationHistory(): void {
    try {
      const stored = localStorage.getItem('overdueNotificationHistory');
      if (stored) {
        const historyArray = JSON.parse(stored);
        this.notificationHistory = new Map(
          historyArray.map(([id, history]: [string, any]) => [
            id,
            {
              ...history,
              lastNotificationTime: new Date(history.lastNotificationTime)
            }
          ])
        );
      }
    } catch (error) {
      console.error('Error loading notification history:', error);
      this.notificationHistory = new Map();
    }
  }

  /**
   * Update check interval (in minutes)
   */
  public setCheckInterval(minutes: number): void {
    this.checkIntervalMs = minutes * 60 * 1000;
    
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }
}

// Export singleton instance
export const overdueNotificationService = OverdueNotificationService.getInstance();
