export interface PerformanceBenchmark {
  name: string;
  target: number; // Target time in ms
  warning: number; // Warning threshold in ms
  critical: number; // Critical threshold in ms
}

export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: Date;
  status: 'good' | 'warning' | 'critical';
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  metrics: PerformanceMetric[];
  summary: {
    totalOperations: number;
    averageDuration: number;
    slowestOperation: PerformanceMetric | null;
    fastestOperation: PerformanceMetric | null;
    warningCount: number;
    criticalCount: number;
  };
  recommendations: string[];
}

export interface PerformanceAlert {
  type: 'warning' | 'critical';
  message: string;
  metric: PerformanceMetric;
  timestamp: Date;
}

export class PerformanceMonitorService {
  private benchmarks: Map<string, PerformanceBenchmark> = new Map();
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private eventListeners: Map<string, Function[]> = new Map();
  private isMonitoring = false;
  private maxMetrics = 1000; // Keep last 1000 metrics

  constructor() {
    this.initializeBenchmarks();
    this.setupPerformanceObservers();
  }

  private initializeBenchmarks(): void {
    // Search performance benchmarks
    this.benchmarks.set('todo_search', {
      name: 'Todo Search',
      target: 50,
      warning: 100,
      critical: 200,
    });

    this.benchmarks.set('search_indexing', {
      name: 'Search Indexing',
      target: 100,
      warning: 500,
      critical: 1000,
    });

    // Filter performance benchmarks
    this.benchmarks.set('todo_filtering', {
      name: 'Todo Filtering',
      target: 20,
      warning: 50,
      critical: 100,
    });

    // UI performance benchmarks
    this.benchmarks.set('component_render', {
      name: 'Component Render',
      target: 16, // 60fps
      warning: 33, // 30fps
      critical: 50, // 20fps
    });

    this.benchmarks.set('list_virtualization', {
      name: 'List Virtualization',
      target: 10,
      warning: 20,
      critical: 50,
    });

    // Database operation benchmarks
    this.benchmarks.set('db_query', {
      name: 'Database Query',
      target: 50,
      warning: 100,
      critical: 300,
    });

    this.benchmarks.set('db_transaction', {
      name: 'Database Transaction',
      target: 100,
      warning: 200,
      critical: 500,
    });

    // Sync operation benchmarks
    this.benchmarks.set('sync_operation', {
      name: 'Sync Operation',
      target: 1000,
      warning: 3000,
      critical: 5000,
    });
  }

  private setupPerformanceObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not available');
      return;
    }

    // Observe Long Tasks
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric({
            name: 'long_task',
            duration: entry.duration,
            timestamp: new Date(),
            status: 'warning',
            metadata: {
              startTime: entry.startTime,
              name: entry.name,
            },
          });
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    } catch (error) {
      console.warn('Long task observation not supported');
    }

    // Observe Layout Shifts
    try {
      const layoutShiftObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.value > 0.1) { // CLS threshold
            this.recordMetric({
              name: 'layout_shift',
              duration: entry.value * 1000, // Convert to ms equivalent
              timestamp: new Date(),
              status: entry.value > 0.25 ? 'critical' : 'warning',
              metadata: {
                value: entry.value,
                sources: entry.sources,
              },
            });
          }
        });
      });

      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(layoutShiftObserver);
    } catch (error) {
      console.warn('Layout shift observation not supported');
    }

    // Observe Paint Timing
    try {
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric({
            name: entry.name.replace('-', '_'),
            duration: entry.startTime,
            timestamp: new Date(),
            status: this.getStatusForPaintTiming(entry.name, entry.startTime),
            metadata: {
              entryType: entry.entryType,
            },
          });
        });
      });

      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      console.warn('Paint timing observation not supported');
    }
  }

  private getStatusForPaintTiming(name: string, time: number): 'good' | 'warning' | 'critical' {
    if (name === 'first-contentful-paint') {
      if (time < 1800) return 'good';
      if (time < 3000) return 'warning';
      return 'critical';
    }
    
    if (name === 'largest-contentful-paint') {
      if (time < 2500) return 'good';
      if (time < 4000) return 'warning';
      return 'critical';
    }

    return 'good';
  }

  public startMonitoring(): void {
    this.isMonitoring = true;
    console.log('Performance monitoring started');
  }

  public stopMonitoring(): void {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    console.log('Performance monitoring stopped');
  }

  public async measureAsync<T>(
    operationName: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: operationName,
        duration,
        timestamp: new Date(),
        status: this.getStatusForOperation(operationName, duration),
        metadata,
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: operationName,
        duration,
        timestamp: new Date(),
        status: 'critical',
        metadata: {
          ...metadata,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      throw error;
    }
  }

  public measureSync<T>(
    operationName: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    const startTime = performance.now();
    
    try {
      const result = operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: operationName,
        duration,
        timestamp: new Date(),
        status: this.getStatusForOperation(operationName, duration),
        metadata,
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name: operationName,
        duration,
        timestamp: new Date(),
        status: 'critical',
        metadata: {
          ...metadata,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      throw error;
    }
  }

  public recordMetric(metric: PerformanceMetric): void {
    if (!this.isMonitoring) return;

    // Add to metrics array
    this.metrics.push(metric);

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Check for alerts
    this.checkForAlerts(metric);

    // Emit event
    this.emit('metricRecorded', metric);
  }

  private getStatusForOperation(operationName: string, duration: number): 'good' | 'warning' | 'critical' {
    const benchmark = this.benchmarks.get(operationName);
    if (!benchmark) {
      // Default thresholds if no benchmark exists
      if (duration < 50) return 'good';
      if (duration < 100) return 'warning';
      return 'critical';
    }

    if (duration <= benchmark.target) return 'good';
    if (duration <= benchmark.warning) return 'warning';
    return 'critical';
  }

  private checkForAlerts(metric: PerformanceMetric): void {
    if (metric.status === 'warning' || metric.status === 'critical') {
      const alert: PerformanceAlert = {
        type: metric.status,
        message: this.generateAlertMessage(metric),
        metric,
        timestamp: new Date(),
      };

      this.emit('performanceAlert', alert);
    }
  }

  private generateAlertMessage(metric: PerformanceMetric): string {
    const benchmark = this.benchmarks.get(metric.name);
    
    if (metric.status === 'critical') {
      return `Critical performance issue: ${metric.name} took ${metric.duration.toFixed(1)}ms${
        benchmark ? ` (target: ${benchmark.target}ms)` : ''
      }`;
    } else {
      return `Performance warning: ${metric.name} took ${metric.duration.toFixed(1)}ms${
        benchmark ? ` (target: ${benchmark.target}ms)` : ''
      }`;
    }
  }

  public getMetrics(operationName?: string, timeRange?: { start: Date; end: Date }): PerformanceMetric[] {
    let filteredMetrics = this.metrics;

    if (operationName) {
      filteredMetrics = filteredMetrics.filter(metric => metric.name === operationName);
    }

    if (timeRange) {
      filteredMetrics = filteredMetrics.filter(metric => 
        metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
      );
    }

    return filteredMetrics;
  }

  public generateReport(timeRange?: { start: Date; end: Date }): PerformanceReport {
    const metrics = this.getMetrics(undefined, timeRange);
    
    if (metrics.length === 0) {
      return {
        metrics: [],
        summary: {
          totalOperations: 0,
          averageDuration: 0,
          slowestOperation: null,
          fastestOperation: null,
          warningCount: 0,
          criticalCount: 0,
        },
        recommendations: [],
      };
    }

    const totalDuration = metrics.reduce((sum, metric) => sum + metric.duration, 0);
    const averageDuration = totalDuration / metrics.length;
    const slowestOperation = metrics.reduce((slowest, metric) => 
      !slowest || metric.duration > slowest.duration ? metric : slowest
    );
    const fastestOperation = metrics.reduce((fastest, metric) => 
      !fastest || metric.duration < fastest.duration ? metric : fastest
    );
    const warningCount = metrics.filter(metric => metric.status === 'warning').length;
    const criticalCount = metrics.filter(metric => metric.status === 'critical').length;

    const recommendations = this.generateRecommendations(metrics);

    return {
      metrics,
      summary: {
        totalOperations: metrics.length,
        averageDuration,
        slowestOperation,
        fastestOperation,
        warningCount,
        criticalCount,
      },
      recommendations,
    };
  }

  private generateRecommendations(metrics: PerformanceMetric[]): string[] {
    const recommendations: string[] = [];
    const operationStats = new Map<string, { count: number; totalDuration: number; issues: number }>();

    // Analyze metrics by operation
    metrics.forEach(metric => {
      const stats = operationStats.get(metric.name) || { count: 0, totalDuration: 0, issues: 0 };
      stats.count++;
      stats.totalDuration += metric.duration;
      if (metric.status !== 'good') stats.issues++;
      operationStats.set(metric.name, stats);
    });

    // Generate recommendations based on analysis
    operationStats.forEach((stats, operationName) => {
      const avgDuration = stats.totalDuration / stats.count;
      const issueRate = stats.issues / stats.count;

      if (issueRate > 0.5) {
        recommendations.push(`Consider optimizing ${operationName} - ${Math.round(issueRate * 100)}% of operations are slow`);
      }

      if (operationName.includes('search') && avgDuration > 100) {
        recommendations.push('Search operations are slow - consider implementing search result caching or index optimization');
      }

      if (operationName.includes('render') && avgDuration > 16) {
        recommendations.push('UI rendering is slow - consider using React.memo, useMemo, or virtual scrolling');
      }

      if (operationName.includes('db') && avgDuration > 200) {
        recommendations.push('Database operations are slow - consider query optimization or connection pooling');
      }
    });

    return recommendations;
  }

  public clearMetrics(): void {
    this.metrics = [];
    this.emit('metricsCleared');
  }

  public setBenchmark(operationName: string, benchmark: PerformanceBenchmark): void {
    this.benchmarks.set(operationName, benchmark);
  }

  public getBenchmark(operationName: string): PerformanceBenchmark | undefined {
    return this.benchmarks.get(operationName);
  }

  // Event system
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(callback => callback(data));
  }

  public destroy(): void {
    this.stopMonitoring();
    this.clearMetrics();
    this.eventListeners.clear();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitorService();
