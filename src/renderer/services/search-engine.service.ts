import lunr from 'lunr';
import { Todo, TodoPriority, TodoStatus } from '@shared/types';
import { performanceMonitor } from './performance-monitor.service';

export interface SearchQuery {
  text: string;
  filters: SearchFilters;
  sortBy: SearchSortOption;
  sortOrder: 'asc' | 'desc';
}

export interface SearchFilters {
  categories: string[];
  priorities: TodoPriority[];
  statuses: TodoStatus[];
  tags: string[];
  dateRange: {
    start?: Date;
    end?: Date;
    type: 'created' | 'due' | 'completed';
  };
  hasAttachments: boolean | null;
  hasReminder: boolean | null;
}

export interface SearchResult<T = Todo> {
  item: T;
  score: number;
  highlights: SearchHighlight[];
  matchReasons: MatchReason[];
}

export interface SearchHighlight {
  field: string;
  start: number;
  end: number;
  snippet: string;
  highlightedText: string;
}

export interface MatchReason {
  field: string;
  type: 'exact' | 'partial' | 'fuzzy';
  score: number;
}

export interface SearchSuggestion {
  text: string;
  type: 'query' | 'filter' | 'category' | 'tag';
  score: number;
  metadata?: Record<string, any>;
}

export type SearchSortOption = 'relevance' | 'created' | 'updated' | 'due' | 'priority' | 'title';

export interface SearchMetrics {
  duration: number;
  totalResults: number;
  indexSize: number;
  queryComplexity: number;
}

export class SearchEngine {
  private searchIndex: lunr.Index | null = null;
  private documents: Map<string, Todo> = new Map();
  private stopWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.initializeIndex();
  }

  private initializeIndex(): void {
    this.searchIndex = lunr(function () {
      this.ref('id');
      this.field('title', { boost: 10 });
      this.field('description', { boost: 5 });
      this.field('tags', { boost: 3 });
      this.field('category', { boost: 2 });
      this.field('priority');
      this.field('status');
      
      // Custom tokenizer for better search
      this.tokenizer.separator = /[\s\-_\.]+/;
    });
  }

  public async indexTodos(todos: Todo[]): Promise<void> {
    return performanceMonitor.measureAsync('search_indexing', async () => {
      // Clear existing documents
      this.documents.clear();

      // Prepare documents for indexing
      const documents = todos.map(todo => {
        this.documents.set(todo.id, todo);

        return {
          id: todo.id,
          title: todo.title || '',
          description: todo.description || '',
          tags: (todo.tags || []).join(' '),
          category: todo.category_id || '',
          priority: todo.priority || '',
          status: todo.status || '',
        };
      });

      // Rebuild index with new documents
      this.searchIndex = lunr(function () {
        this.ref('id');
        this.field('title', { boost: 10 });
        this.field('description', { boost: 5 });
        this.field('tags', { boost: 3 });
        this.field('category', { boost: 2 });
        this.field('priority');
        this.field('status');

        documents.forEach(doc => {
          this.add(doc);
        });
      });

      this.emit('indexUpdated', { documentCount: todos.length });
    }, { documentCount: todos.length });
  }

  public async search(query: SearchQuery): Promise<SearchResult[]> {
    return performanceMonitor.measureAsync('todo_search', async () => {
      if (!this.searchIndex || !query.text.trim()) {
        return [];
      }

      try {
        // Parse and enhance the search query
        const enhancedQuery = this.enhanceQuery(query.text);

        // Perform the search
        const results = this.searchIndex.search(enhancedQuery);

        // Convert to SearchResult objects with highlights and scoring
        const searchResults = results.map(result => {
          const todo = this.documents.get(result.ref);
          if (!todo) return null;

          const highlights = this.generateHighlights(todo, query.text);
          const matchReasons = this.analyzeMatches(result, query.text);

          return {
            item: todo,
            score: result.score,
            highlights,
            matchReasons,
          };
        }).filter(Boolean) as SearchResult[];

        // Apply additional filters
        const filteredResults = this.applyFilters(searchResults, query.filters);

        // Sort results
        const sortedResults = this.sortResults(filteredResults, query.sortBy, query.sortOrder);

        const metrics: SearchMetrics = {
          duration: 0, // Will be set by performance monitor
          totalResults: sortedResults.length,
          indexSize: this.documents.size,
          queryComplexity: this.calculateQueryComplexity(query.text),
        };

        this.emit('searchPerformed', metrics);

        return sortedResults;
      } catch (error) {
        console.error('Search failed:', error);
        return [];
      }
    }, {
      queryText: query.text,
      queryComplexity: this.calculateQueryComplexity(query.text),
      indexSize: this.documents.size
    });
  }

  private enhanceQuery(query: string): string {
    // Remove stop words and enhance query for better search
    const words = query.toLowerCase().split(/\s+/)
      .filter(word => word.length > 1 && !this.stopWords.has(word));
    
    if (words.length === 0) return query;

    // Build enhanced query with wildcards and boosting
    const enhancedTerms = words.map(word => {
      // Exact match gets highest boost
      let term = `${word}^10`;
      
      // Add wildcard for partial matches
      if (word.length > 2) {
        term += ` ${word}*^5`;
      }
      
      // Add fuzzy search for typos
      if (word.length > 3) {
        term += ` ${word}~1^2`;
      }
      
      return `(${term})`;
    });

    return enhancedTerms.join(' ');
  }

  private generateHighlights(todo: Todo, query: string): SearchHighlight[] {
    const highlights: SearchHighlight[] = [];
    const queryWords = query.toLowerCase().split(/\s+/)
      .filter(word => word.length > 1 && !this.stopWords.has(word));

    // Check title for highlights
    if (todo.title) {
      const titleHighlights = this.findHighlightsInText(todo.title, queryWords, 'title');
      highlights.push(...titleHighlights);
    }

    // Check description for highlights
    if (todo.description) {
      const descHighlights = this.findHighlightsInText(todo.description, queryWords, 'description');
      highlights.push(...descHighlights);
    }

    // Check tags for highlights
    if (todo.tags && todo.tags.length > 0) {
      const tagsText = todo.tags.join(' ');
      const tagHighlights = this.findHighlightsInText(tagsText, queryWords, 'tags');
      highlights.push(...tagHighlights);
    }

    return highlights;
  }

  private findHighlightsInText(text: string, queryWords: string[], field: string): SearchHighlight[] {
    const highlights: SearchHighlight[] = [];
    const lowerText = text.toLowerCase();

    queryWords.forEach(word => {
      let index = 0;
      while ((index = lowerText.indexOf(word, index)) !== -1) {
        const start = Math.max(0, index - 20);
        const end = Math.min(text.length, index + word.length + 20);
        const snippet = text.substring(start, end);
        
        const highlightedText = text.substring(index, index + word.length);
        
        highlights.push({
          field,
          start: index,
          end: index + word.length,
          snippet: start > 0 ? '...' + snippet : snippet,
          highlightedText,
        });
        
        index += word.length;
      }
    });

    return highlights;
  }

  private analyzeMatches(result: lunr.Index.Result, query: string): MatchReason[] {
    const reasons: MatchReason[] = [];
    
    // Analyze which fields matched and how
    Object.entries(result.matchData.metadata).forEach(([term, termData]) => {
      Object.entries(termData).forEach(([field, fieldData]) => {
        const score = (fieldData as any).tf || 0;
        
        reasons.push({
          field,
          type: this.determineMatchType(term, query),
          score,
        });
      });
    });

    return reasons;
  }

  private determineMatchType(term: string, query: string): 'exact' | 'partial' | 'fuzzy' {
    const queryLower = query.toLowerCase();
    const termLower = term.toLowerCase();
    
    if (queryLower.includes(termLower)) {
      return 'exact';
    } else if (termLower.includes(queryLower) || queryLower.includes(termLower)) {
      return 'partial';
    } else {
      return 'fuzzy';
    }
  }

  private applyFilters(results: SearchResult[], filters: SearchFilters): SearchResult[] {
    return results.filter(result => {
      const todo = result.item;

      // Category filter
      if (filters.categories.length > 0 && !filters.categories.includes(todo.category_id || '')) {
        return false;
      }

      // Priority filter
      if (filters.priorities.length > 0 && !filters.priorities.includes(todo.priority)) {
        return false;
      }

      // Status filter
      if (filters.statuses.length > 0 && !filters.statuses.includes(todo.status)) {
        return false;
      }

      // Tags filter
      if (filters.tags.length > 0) {
        const todoTags = todo.tags || [];
        const hasMatchingTag = filters.tags.some(tag => todoTags.includes(tag));
        if (!hasMatchingTag) return false;
      }

      // Date range filter
      if (filters.dateRange.start || filters.dateRange.end) {
        const dateField = this.getDateField(todo, filters.dateRange.type);
        if (!dateField) return false;

        const date = new Date(dateField);
        if (filters.dateRange.start && date < filters.dateRange.start) return false;
        if (filters.dateRange.end && date > filters.dateRange.end) return false;
      }

      return true;
    });
  }

  private getDateField(todo: Todo, type: 'created' | 'due' | 'completed'): string | null {
    switch (type) {
      case 'created':
        return todo.created_at;
      case 'due':
        return todo.due_date || null;
      case 'completed':
        return todo.completed_at || null;
      default:
        return null;
    }
  }

  private sortResults(results: SearchResult[], sortBy: SearchSortOption, sortOrder: 'asc' | 'desc'): SearchResult[] {
    return results.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'relevance':
          comparison = b.score - a.score;
          break;
        case 'created':
          comparison = new Date(a.item.created_at).getTime() - new Date(b.item.created_at).getTime();
          break;
        case 'updated':
          comparison = new Date(a.item.updated_at).getTime() - new Date(b.item.updated_at).getTime();
          break;
        case 'due':
          const aDue = a.item.due_date ? new Date(a.item.due_date).getTime() : Infinity;
          const bDue = b.item.due_date ? new Date(b.item.due_date).getTime() : Infinity;
          comparison = aDue - bDue;
          break;
        case 'priority':
          const priorityOrder = { very_low: 1, low: 2, medium: 3, high: 4, very_high: 5 };
          comparison = (priorityOrder[a.item.priority] || 0) - (priorityOrder[b.item.priority] || 0);
          break;
        case 'title':
          comparison = a.item.title.localeCompare(b.item.title);
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  private calculateQueryComplexity(query: string): number {
    const words = query.split(/\s+/).length;
    const hasWildcards = query.includes('*') || query.includes('?');
    const hasFuzzy = query.includes('~');
    const hasBoosts = query.includes('^');
    
    let complexity = words;
    if (hasWildcards) complexity += 2;
    if (hasFuzzy) complexity += 3;
    if (hasBoosts) complexity += 1;
    
    return complexity;
  }

  public async getSuggestions(query: string): Promise<SearchSuggestion[]> {
    if (query.length < 2) return [];

    const suggestions: SearchSuggestion[] = [];
    
    // Get query-based suggestions
    const queryWords = query.toLowerCase().split(/\s+/);
    const lastWord = queryWords[queryWords.length - 1];
    
    // Suggest completions based on existing todos
    this.documents.forEach(todo => {
      // Title suggestions
      if (todo.title.toLowerCase().includes(lastWord)) {
        const words = todo.title.split(/\s+/);
        words.forEach(word => {
          if (word.toLowerCase().startsWith(lastWord) && word.toLowerCase() !== lastWord) {
            suggestions.push({
              text: query.replace(lastWord, word),
              type: 'query',
              score: 0.8,
            });
          }
        });
      }

      // Tag suggestions
      if (todo.tags) {
        todo.tags.forEach(tag => {
          if (tag.toLowerCase().includes(lastWord)) {
            suggestions.push({
              text: `tag:${tag}`,
              type: 'tag',
              score: 0.6,
              metadata: { tag },
            });
          }
        });
      }
    });

    // Remove duplicates and sort by score
    const uniqueSuggestions = suggestions
      .filter((suggestion, index, self) => 
        index === self.findIndex(s => s.text === suggestion.text)
      )
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);

    return uniqueSuggestions;
  }

  // Event system for performance monitoring
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(callback => callback(data));
  }

  public getMetrics(): { indexSize: number; documentsCount: number } {
    return {
      indexSize: this.searchIndex ? 1 : 0, // Simplified metric
      documentsCount: this.documents.size,
    };
  }
}

// Singleton instance
export const searchEngine = new SearchEngine();
