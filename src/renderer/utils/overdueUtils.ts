import { Todo } from '@shared/types';

export type OverdueSeverity = 'none' | 'slight' | 'moderate' | 'severe';

export interface OverdueInfo {
  isOverdue: boolean;
  severity: OverdueSeverity;
  daysOverdue: number;
  hoursOverdue: number;
  formattedDuration: string;
}

/**
 * Get comprehensive overdue information for a todo
 */
export function getOverdueInfo(todo: Todo): OverdueInfo {
  if (!todo.due_date || todo.status === 'completed' || todo.status === 'cancelled') {
    return {
      isOverdue: false,
      severity: 'none',
      daysOverdue: 0,
      hoursOverdue: 0,
      formattedDuration: '',
    };
  }

  const now = new Date();
  const dueDate = new Date(todo.due_date);
  const diffMs = now.getTime() - dueDate.getTime();
  
  if (diffMs <= 0) {
    return {
      isOverdue: false,
      severity: 'none',
      daysOverdue: 0,
      hoursOverdue: 0,
      formattedDuration: '',
    };
  }

  const hoursOverdue = Math.floor(diffMs / (1000 * 60 * 60));
  const daysOverdue = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  let severity: OverdueSeverity = 'slight';
  if (daysOverdue >= 7) {
    severity = 'severe';
  } else if (daysOverdue >= 1) {
    severity = 'moderate';
  }

  const formattedDuration = formatOverdueDuration(hoursOverdue, daysOverdue);

  return {
    isOverdue: true,
    severity,
    daysOverdue,
    hoursOverdue,
    formattedDuration,
  };
}

/**
 * Format overdue duration in a human-readable way
 */
function formatOverdueDuration(hoursOverdue: number, daysOverdue: number): string {
  if (daysOverdue >= 1) {
    return daysOverdue === 1 ? '1 day overdue' : `${daysOverdue} days overdue`;
  } else if (hoursOverdue >= 1) {
    return hoursOverdue === 1 ? '1 hour overdue' : `${hoursOverdue} hours overdue`;
  } else {
    return 'Just overdue';
  }
}

/**
 * Get CSS classes for overdue styling based on severity
 */
export function getOverdueClasses(severity: OverdueSeverity): {
  borderClass: string;
  bgClass: string;
  textClass: string;
  iconClass: string;
  pulseClass: string;
} {
  switch (severity) {
    case 'slight':
      return {
        borderClass: 'border-l-4 border-orange-400',
        bgClass: 'bg-orange-50',
        textClass: 'text-orange-600',
        iconClass: 'text-orange-500',
        pulseClass: '',
      };
    case 'moderate':
      return {
        borderClass: 'border-l-4 border-red-500',
        bgClass: 'bg-red-50',
        textClass: 'text-red-700',
        iconClass: 'text-red-600',
        pulseClass: '',
      };
    case 'severe':
      return {
        borderClass: 'border-l-4 border-red-600',
        bgClass: 'bg-red-100',
        textClass: 'text-red-800',
        iconClass: 'text-red-700',
        pulseClass: 'animate-pulse',
      };
    default:
      return {
        borderClass: '',
        bgClass: '',
        textClass: '',
        iconClass: '',
        pulseClass: '',
      };
  }
}

/**
 * Get the appropriate icon for overdue severity
 */
export function getOverdueIcon(severity: OverdueSeverity): string {
  switch (severity) {
    case 'slight':
      return 'AlertCircle';
    case 'moderate':
      return 'AlertTriangle';
    case 'severe':
      return 'AlertOctagon';
    default:
      return 'AlertCircle';
  }
}

/**
 * Check if a todo should trigger a notification based on its overdue status
 */
export function shouldNotifyOverdue(
  overdueInfo: OverdueInfo,
  lastNotificationTime?: Date
): boolean {
  if (!overdueInfo.isOverdue) return false;

  const now = new Date();
  const timeSinceLastNotification = lastNotificationTime
    ? now.getTime() - lastNotificationTime.getTime()
    : Infinity;

  // Convert to hours
  const hoursSinceLastNotification = timeSinceLastNotification / (1000 * 60 * 60);

  switch (overdueInfo.severity) {
    case 'slight':
      // Notify immediately when first overdue, then every 4 hours
      return !lastNotificationTime || hoursSinceLastNotification >= 4;
    case 'moderate':
      // Notify every 12 hours for moderately overdue tasks
      return !lastNotificationTime || hoursSinceLastNotification >= 12;
    case 'severe':
      // Notify every 6 hours for severely overdue tasks
      return !lastNotificationTime || hoursSinceLastNotification >= 6;
    default:
      return false;
  }
}

/**
 * Get notification priority based on overdue severity
 */
export function getNotificationPriority(severity: OverdueSeverity): 'low' | 'normal' | 'high' {
  switch (severity) {
    case 'slight':
      return 'low';
    case 'moderate':
      return 'normal';
    case 'severe':
      return 'high';
    default:
      return 'low';
  }
}

/**
 * Generate notification message for overdue todo
 */
export function generateOverdueNotificationMessage(
  todo: Todo,
  overdueInfo: OverdueInfo
): { title: string; message: string } {
  const title = `Task Overdue: ${todo.title}`;
  const message = `This task is ${overdueInfo.formattedDuration}. ${
    overdueInfo.severity === 'severe' ? 'Please address it urgently!' : 'Please review when possible.'
  }`;

  return { title, message };
}
